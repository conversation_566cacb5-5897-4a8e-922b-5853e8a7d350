import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { target } = req.body;
    
    if (!target) {
      return res.status(400).json({ error: '缺少目标URL参数' });
    }

    // 确保目标URL格式正确
    const fullTarget = target.startsWith('http') ? target : `https://${target}`;
    
    // 调用 Cloudflare Workers API
    const cloudflareUrl = `https://ping-api.wobys.dpdns.org?target=${encodeURIComponent(fullTarget)}`;
    
    console.log('🔥 调用 Cloudflare Workers API:', cloudflareUrl);
    
    const response = await fetch(cloudflareUrl, {
      method: 'GET',
      signal: AbortSignal.timeout(10000) // 10秒超时
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Cloudflare Workers API 错误:', response.status, errorText);
      return res.status(response.status).json({ 
        error: `Cloudflare Workers API 错误: ${response.status}`,
        details: errorText
      });
    }

    const data = await response.json();
    
    if (data.success) {
      // 转换数据格式以匹配组件期望的格式
      const result = {
        success: true,
        results: [{
          node: data.cloudflare?.city || data.cloudflare?.colo || 'Cloudflare',
          city: data.cloudflare?.city || data.cloudflare?.region || 'Unknown',
          ping: data.latency || data.testResult?.latency || 999,
          status: data.success ? 'success' : 'failed',
          location: { 
            country: data.cloudflare?.country || 'Unknown', 
            region: data.cloudflare?.continent || 'Unknown' 
          },
          testMethod: 'Cloudflare Workers',
          apiSource: 'Cloudflare Workers',
          datacenter: data.cloudflare?.datacenter,
          timestamp: new Date().toISOString(),
          rawData: data // 保留原始数据用于调试
        }],
        metadata: {
          source: 'Cloudflare Workers',
          timestamp: new Date().toISOString(),
          target: fullTarget
        }
      };

      console.log('✅ Cloudflare Workers 数据转换成功:', result);
      return res.status(200).json(result);
    } else {
      console.error('❌ Cloudflare Workers 返回失败:', data);
      return res.status(500).json({ 
        error: 'Cloudflare Workers 测试失败',
        details: data
      });
    }

  } catch (error) {
    console.error('❌ Cloudflare Workers API 调用异常:', error);
    return res.status(500).json({ 
      error: '服务器内部错误',
      details: error instanceof Error ? error.message : String(error)
    });
  }
}
