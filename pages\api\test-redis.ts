import type { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // 检查环境变量
    const kvRestApiToken = process.env.KV_REST_API_TOKEN
    const kvUrl = process.env.KV_URL || process.env.REDIS_URL
    const kvRestApiUrl = process.env.KV_REST_API_URL

    const envCheck = {
      KV_REST_API_TOKEN: !!kvRestApiToken,
      KV_URL: !!kvUrl,
      KV_REST_API_URL: !!kvRestApiUrl,
      tokenLength: kvRestApiToken ? kvRestApiToken.length : 0,
      urlValid: kvUrl ? kvUrl.startsWith('redis') : false
    }

    if (!kvRestApiToken || !kvUrl) {
      return res.status(200).json({
        success: false,
        error: 'Missing Redis configuration',
        envCheck,
        suggestion: 'Please check your Vercel environment variables'
      })
    }

    // 构建 REST API URL
    const restApiUrl = kvRestApiUrl || (() => {
      try {
        const redisUrl = new URL(kvUrl)
        return `https://${redisUrl.hostname.replace(':6379', '')}`
      } catch (error) {
        return null
      }
    })()

    if (!restApiUrl) {
      return res.status(200).json({
        success: false,
        error: 'Invalid Redis URL format',
        envCheck,
        kvUrl: kvUrl?.substring(0, 20) + '...'
      })
    }

    // 测试Redis连接
    const testKey = 'test_connection_' + Date.now()
    const testValue = 'test_value_' + Math.random()

    // 1. 测试SET操作
    const setResponse = await fetch(`${restApiUrl}/set/${testKey}/${testValue}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${kvRestApiToken}`,
      },
      signal: AbortSignal.timeout(5000)
    })

    if (!setResponse.ok) {
      return res.status(200).json({
        success: false,
        error: `SET operation failed: ${setResponse.status}`,
        envCheck,
        restApiUrl: restApiUrl.substring(0, 30) + '...',
        setStatus: setResponse.status
      })
    }

    // 2. 测试GET操作
    const getResponse = await fetch(`${restApiUrl}/get/${testKey}`, {
      headers: {
        'Authorization': `Bearer ${kvRestApiToken}`,
      },
      signal: AbortSignal.timeout(5000)
    })

    if (!getResponse.ok) {
      return res.status(200).json({
        success: false,
        error: `GET operation failed: ${getResponse.status}`,
        envCheck,
        restApiUrl: restApiUrl.substring(0, 30) + '...',
        getStatus: getResponse.status
      })
    }

    const getData = await getResponse.json()
    const retrievedValue = getData.result

    // 3. 清理测试数据
    await fetch(`${restApiUrl}/del/${testKey}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${kvRestApiToken}`,
      },
      signal: AbortSignal.timeout(3000)
    }).catch(() => {}) // 忽略清理错误

    // 4. 测试访问计数
    const visitCountResponse = await fetch(`${restApiUrl}/get/visit_count`, {
      headers: {
        'Authorization': `Bearer ${kvRestApiToken}`,
      },
      signal: AbortSignal.timeout(5000)
    })

    let currentVisitCount = null
    if (visitCountResponse.ok) {
      const visitData = await visitCountResponse.json()
      currentVisitCount = visitData.result
    }

    return res.status(200).json({
      success: true,
      message: 'Redis connection test successful',
      testResults: {
        setValue: testValue,
        retrievedValue: retrievedValue,
        valuesMatch: testValue === retrievedValue
      },
      visitCount: {
        current: currentVisitCount,
        exists: currentVisitCount !== null
      },
      envCheck,
      restApiUrl: restApiUrl.substring(0, 30) + '...',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    })
  }
}
