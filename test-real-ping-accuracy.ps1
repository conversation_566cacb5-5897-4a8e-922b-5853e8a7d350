# 🌐 真实Ping API准确性测试脚本
# 测试新的Globalping API与本地ping的准确性对比

param(
    [string[]]$TestSites = @(
        "baidu.com",
        "qq.com", 
        "taobao.com",
        "google.com",
        "facebook.com",
        "wobshare.us.kg",
        "github.com"
    )
)

Write-Host "🌐 真实Ping API准确性测试" -ForegroundColor Green
Write-Host "测试要求: 可访问网站误差 ±20ms，被墙网站 >250ms" -ForegroundColor Yellow
Write-Host "=" * 60

foreach ($site in $TestSites) {
    Write-Host "`n🎯 测试网站: $site" -ForegroundColor Cyan
    
    # 本地ping测试
    Write-Host "📍 本地ping测试..." -ForegroundColor Gray
    try {
        $pingResult = ping $site -n 4 2>$null
        if ($pingResult -match "平均 = (\d+)ms") {
            $localPing = [int]$matches[1]
            Write-Host "   本地ping: ${localPing}ms" -ForegroundColor White
        } else {
            $localPing = 9999
            Write-Host "   本地ping: 超时/失败" -ForegroundColor Red
        }
    } catch {
        $localPing = 9999
        Write-Host "   本地ping: 超时/失败" -ForegroundColor Red
    }
    
    # API测试
    Write-Host "🌐 真实Ping API测试..." -ForegroundColor Gray
    try {
        $body = @{
            target = $site
            maxNodes = 10
        } | ConvertTo-Json
        
        $response = Invoke-RestMethod -Uri "http://localhost:3002/api/real-ping" -Method POST -ContentType "application/json" -Body $body -TimeoutSec 60
        
        # 找到中国节点的结果
        $chinaNodes = $response.results | Where-Object { $_.location.country -eq "CN" }
        
        if ($chinaNodes.Count -gt 0) {
            $apiPing = ($chinaNodes | Measure-Object -Property ping -Average).Average
            $apiPing = [math]::Round($apiPing)
            Write-Host "   API ping (中国节点平均): ${apiPing}ms" -ForegroundColor White
            
            # 计算误差
            if ($localPing -eq 9999) {
                if ($apiPing -gt 250) {
                    Write-Host "   ✅ 被墙网站检测正确 (API: ${apiPing}ms > 250ms)" -ForegroundColor Green
                } else {
                    Write-Host "   ❌ 被墙网站检测失败 (API: ${apiPing}ms <= 250ms)" -ForegroundColor Red
                }
            } else {
                $error = [math]::Abs($apiPing - $localPing)
                if ($error -le 20) {
                    Write-Host "   ✅ 准确性合格 (误差: ${error}ms <= 20ms)" -ForegroundColor Green
                } else {
                    Write-Host "   ❌ 准确性不合格 (误差: ${error}ms > 20ms)" -ForegroundColor Red
                }
            }
        } else {
            Write-Host "   ⚠️  未找到中国节点数据" -ForegroundColor Yellow
        }
        
        # 显示所有节点信息
        Write-Host "   节点详情:" -ForegroundColor Gray
        foreach ($node in $response.results) {
            $country = $node.location.country
            $city = $node.location.city
            $ping = $node.ping
            $status = $node.status

            if ($status -eq "success") {
                Write-Host "     $country-$city : ${ping}ms" -ForegroundColor DarkGray
            } else {
                Write-Host "     $country-$city : 超时" -ForegroundColor DarkRed
            }
        }

    } catch {
        Write-Host "   API调用失败: $($_.Exception.Message)" -ForegroundColor Red
    }

    Start-Sleep -Seconds 2
}

Write-Host ""
Write-Host "=" * 60
Write-Host "测试完成！" -ForegroundColor Green
Write-Host "真实Ping API基于Globalping全球网络，提供真实的网络测试数据" -ForegroundColor Yellow
