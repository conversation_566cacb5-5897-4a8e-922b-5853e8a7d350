// 调试ITDOG API
const https = require('https');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 15000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          text: () => Promise.resolve(data),
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testITDOGAPI() {
  console.log('🔍 测试ITDOG API...');
  
  try {
    // 测试ITDOG API
    const response = await fetch('https://www.itdog.cn/ping/google.com', {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://www.itdog.cn/',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      }
    });
    
    console.log(`📡 ITDOG响应状态: ${response.status}`);
    
    if (response.ok) {
      const html = await response.text();
      console.log(`📄 响应长度: ${html.length} 字符`);
      
      // 检查是否包含ping结果
      if (html.includes('ping') || html.includes('ms')) {
        console.log('✅ ITDOG API正常工作');
        
        // 提取一些关键信息
        const lines = html.split('\n').slice(0, 20);
        console.log('📋 响应内容预览:');
        lines.forEach((line, index) => {
          if (line.trim()) {
            console.log(`${index + 1}: ${line.trim().substring(0, 100)}`);
          }
        });
      } else {
        console.log('⚠️ ITDOG响应不包含ping数据');
      }
    } else {
      console.log(`❌ ITDOG API错误: ${response.status}`);
    }
    
  } catch (error) {
    console.log(`❌ ITDOG API调用失败: ${error.message}`);
  }
  
  // 测试BOCE API
  console.log('\n🔍 测试BOCE API...');
  try {
    const boceResponse = await fetch('https://www.boce.com/ping/google.com', {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://www.boce.com/',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
      }
    });
    
    console.log(`📡 BOCE响应状态: ${boceResponse.status}`);
    
    if (boceResponse.ok) {
      const html = await boceResponse.text();
      console.log(`📄 BOCE响应长度: ${html.length} 字符`);
      console.log('✅ BOCE API正常工作');
    } else {
      console.log(`❌ BOCE API错误: ${boceResponse.status}`);
    }
    
  } catch (error) {
    console.log(`❌ BOCE API调用失败: ${error.message}`);
  }
  
  // 检查智能检测算法
  console.log('\n🔍 检查智能检测算法...');
  
  // 模拟检测Google.com
  const testResults = [
    { ping: 25, apiSource: 'BOCE-Real', node: '香港-联通' },
    { ping: 30, apiSource: 'BOCE-Real', node: '台北-电信' },
    { ping: 45, apiSource: 'BOCE-Real', node: '新加坡-移动' }
  ];
  
  console.log('📊 模拟检测结果:');
  testResults.forEach(result => {
    console.log(`- ${result.node}: ${result.ping}ms (${result.apiSource})`);
  });
  
  // 检查是否应该触发智能调整
  const avgPing = testResults.reduce((sum, r) => sum + r.ping, 0) / testResults.length;
  console.log(`📈 平均延迟: ${avgPing}ms`);
  
  if (avgPing < 50) {
    console.log('⚠️ 平均延迟过低，应该触发智能检测');
  } else {
    console.log('✅ 延迟正常');
  }
}

testITDOGAPI();
