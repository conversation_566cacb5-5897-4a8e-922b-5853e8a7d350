// 测试各个API的状态
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {}
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testAPIStatus() {
  console.log('🔍 测试各个API状态...');
  
  const apis = [
    { name: 'Globalping', endpoint: '/api/globalping-proxy', body: { url: 'baidu.com' } },
    { name: 'ITDOG', endpoint: '/api/itdog-proxy', body: { target: 'baidu.com' } },
    { name: '增强API', endpoint: '/api/enhanced-ping', body: { target: 'baidu.com', maxNodes: 10 } }
  ];
  
  for (const api of apis) {
    try {
      console.log(`\n📡 测试 ${api.name}...`);
      const startTime = Date.now();
      
      const response = await fetch(`http://localhost:3001${api.endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(api.body)
      });
      
      const duration = Date.now() - startTime;
      const data = await response.json();
      
      if (response.ok) {
        console.log(`✅ ${api.name} 成功 (${duration}ms)`);
        console.log(`   - 结果数量: ${data.results?.length || 0}`);
        if (data.results && data.results.length > 0) {
          const sample = data.results[0];
          console.log(`   - 样本: ${sample.node} - ${sample.province || sample.location?.province || '未知'} - ${sample.ping}ms`);
        }
      } else {
        console.log(`❌ ${api.name} 失败 (${response.status})`);
        console.log(`   - 错误: ${data.error || '未知错误'}`);
      }
      
    } catch (error) {
      console.log(`❌ ${api.name} 异常: ${error.message}`);
    }
  }
  
  console.log('\n✅ API状态测试完成！');
}

testAPIStatus();
