#!/usr/bin/env node

/**
 * 演示批量测试百度网站
 * 使用10个ping平台对 https://www.baidu.com/ 进行全面测试
 */

// 使用动态导入来获取fetch
let fetch;
(async () => {
  try {
    const nodeFetch = await import('node-fetch');
    fetch = nodeFetch.default;
  } catch (error) {
    // 如果node-fetch不可用，使用内置的fetch (Node.js 18+)
    if (typeof globalThis.fetch !== 'undefined') {
      fetch = globalThis.fetch;
    } else {
      console.error('❌ fetch不可用，请安装node-fetch或使用Node.js 18+');
      process.exit(1);
    }
  }
})();

// 10个API平台配置
const platforms = [
  // 🇨🇳 中国平台 (5个)
  { id: '17ce', name: '17CE.COM', type: 'china', rating: 5, description: '国内专业网站监测' },
  { id: 'chinaz', name: 'Chinaz站长工具', type: 'china', rating: 4, description: '站长工具ping测试' },
  { id: 'itdog', name: 'ITDOG.CN', type: 'china', rating: 4, description: 'IT狗网络工具' },
  { id: 'boce', name: 'BOCE.COM', type: 'china', rating: 3, description: '博测网络测试' },
  { id: 'alibaba-boce', name: '阿里云BOCE', type: 'china', rating: 3, description: '阿里云网络测试' },
  
  // 🌍 全球平台 (2个)
  { id: 'globalping', name: 'Globalping.io', type: 'global', rating: 4, description: '全球分布式ping网络' },
  { id: 'ping-pe', name: 'Ping.pe', type: 'global', rating: 4, description: '全球ping测试工具' },
  
  // ⚡ 边缘计算 (2个)
  { id: 'cloudflare-worker', name: 'Cloudflare Workers', type: 'edge', rating: 4, description: '边缘计算ping测试' },
  { id: 'vercel-edge', name: 'Vercel Edge Functions', type: 'edge', rating: 4, description: 'Vercel边缘函数' },
  
  // 🔄 聚合API (1个)
  { id: 'multi-platform', name: 'Multi-Platform API', type: 'aggregated', rating: 5, description: '多平台聚合API' }
];

const testTarget = 'https://www.baidu.com/';

function getTypeIcon(type) {
  switch (type) {
    case 'china': return '🇨🇳';
    case 'global': return '🌍';
    case 'edge': return '⚡';
    case 'aggregated': return '🔄';
    default: return '📊';
  }
}

function getRatingStars(rating) {
  return '⭐'.repeat(rating) + '☆'.repeat(5 - rating);
}

async function testSinglePlatform(platform) {
  const startTime = Date.now();
  
  try {
    console.log(`\n${getTypeIcon(platform.type)} 测试 ${platform.name} ${getRatingStars(platform.rating)}`);
    console.log(`   📝 ${platform.description}`);
    console.log(`   🎯 目标: ${testTarget}`);
    
    const response = await fetch('http://localhost:3000/api/test-ping-platforms', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        platform: platform.id,
        target: testTarget,
        timeout: 15000 // 15秒超时
      })
    });

    const responseTime = Date.now() - startTime;

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    
    // 显示测试结果
    if (result.success) {
      console.log(`   ✅ 测试成功`);
      console.log(`   ⏱️  响应时间: ${responseTime}ms`);
      console.log(`   📊 节点数量: ${result.nodeCount} 个`);
      console.log(`   📈 数据质量: ${result.dataQuality}%`);
      console.log(`   🎯 准确性: ${result.accuracy}%`);
      console.log(`   🌐 覆盖范围: ${result.coverage.join(', ')}`);
      
      if (result.sampleData && result.sampleData.length > 0) {
        console.log(`   📋 样本数据:`);
        result.sampleData.slice(0, 3).forEach(sample => {
          console.log(`      • ${sample.location}: ${sample.ping}ms (${sample.status})`);
        });
      }
    } else {
      console.log(`   ❌ 测试失败`);
      console.log(`   ⏱️  响应时间: ${responseTime}ms`);
      console.log(`   🚫 错误信息: ${result.error || '未知错误'}`);
    }
    
    return {
      ...result,
      actualResponseTime: responseTime,
      platform: platform.name,
      type: platform.type,
      rating: platform.rating
    };
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    console.log(`   ❌ 网络错误: ${error.message}`);
    console.log(`   ⏱️  响应时间: ${responseTime}ms`);
    
    return {
      platform: platform.name,
      type: platform.type,
      rating: platform.rating,
      success: false,
      error: error.message,
      actualResponseTime: responseTime,
      nodeCount: 0,
      dataQuality: 0,
      accuracy: 0,
      coverage: []
    };
  }
}

async function runBaiduTest() {
  console.log('🚀 开始批量测试百度网站');
  console.log('='.repeat(60));
  console.log(`🎯 测试目标: ${testTarget}`);
  console.log(`📊 测试平台: ${platforms.length} 个`);
  console.log(`⏰ 开始时间: ${new Date().toLocaleString()}`);
  console.log('='.repeat(60));

  const results = [];
  let completedTests = 0;

  for (const platform of platforms) {
    const result = await testSinglePlatform(platform);
    results.push(result);
    completedTests++;
    
    console.log(`\n📈 进度: ${completedTests}/${platforms.length} (${Math.round(completedTests/platforms.length*100)}%)`);
    
    // 短暂延迟避免过于频繁的请求
    if (completedTests < platforms.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  // 生成测试报告
  console.log('\n' + '='.repeat(60));
  console.log('📊 测试结果统计报告');
  console.log('='.repeat(60));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  const avgResponseTime = results.reduce((sum, r) => sum + r.actualResponseTime, 0) / results.length;
  
  console.log(`✅ 成功测试: ${successful.length}/${results.length} (${(successful.length/results.length*100).toFixed(1)}%)`);
  console.log(`❌ 失败测试: ${failed.length}/${results.length} (${(failed.length/results.length*100).toFixed(1)}%)`);
  console.log(`⏱️  平均响应时间: ${Math.round(avgResponseTime)}ms`);
  
  if (successful.length > 0) {
    const avgNodeCount = successful.reduce((sum, r) => sum + r.nodeCount, 0) / successful.length;
    const avgDataQuality = successful.reduce((sum, r) => sum + r.dataQuality, 0) / successful.length;
    const avgAccuracy = successful.reduce((sum, r) => sum + r.accuracy, 0) / successful.length;
    
    console.log(`📊 平均节点数: ${Math.round(avgNodeCount)} 个`);
    console.log(`📈 平均数据质量: ${Math.round(avgDataQuality)}%`);
    console.log(`🎯 平均准确性: ${Math.round(avgAccuracy)}%`);
  }

  // 按类型分组统计
  console.log('\n📋 按平台类型分组统计:');
  const typeGroups = {
    china: { name: '🇨🇳 中国平台', results: [] },
    global: { name: '🌍 全球平台', results: [] },
    edge: { name: '⚡ 边缘计算', results: [] },
    aggregated: { name: '🔄 聚合API', results: [] }
  };

  results.forEach(result => {
    if (typeGroups[result.type]) {
      typeGroups[result.type].results.push(result);
    }
  });

  Object.entries(typeGroups).forEach(([type, group]) => {
    if (group.results.length > 0) {
      const successCount = group.results.filter(r => r.success).length;
      const successRate = (successCount / group.results.length) * 100;
      console.log(`  ${group.name}: ${successCount}/${group.results.length} 成功 (${successRate.toFixed(1)}%)`);
    }
  });

  // 最佳表现平台
  console.log('\n🏆 最佳表现平台:');
  if (successful.length > 0) {
    const fastest = successful.sort((a, b) => a.actualResponseTime - b.actualResponseTime)[0];
    const mostNodes = successful.sort((a, b) => b.nodeCount - a.nodeCount)[0];
    const bestQuality = successful.sort((a, b) => b.dataQuality - a.dataQuality)[0];
    const mostAccurate = successful.sort((a, b) => b.accuracy - a.accuracy)[0];
    
    console.log(`🚀 最快响应: ${fastest.platform} (${fastest.actualResponseTime}ms)`);
    console.log(`📊 最多节点: ${mostNodes.platform} (${mostNodes.nodeCount}个)`);
    console.log(`📈 最佳质量: ${bestQuality.platform} (${bestQuality.dataQuality}%)`);
    console.log(`🎯 最高准确性: ${mostAccurate.platform} (${mostAccurate.accuracy}%)`);
  }

  // 推荐使用的平台
  console.log('\n💡 推荐使用的平台 (针对百度网站):');
  const chineseSuccessful = successful.filter(r => r.type === 'china');
  if (chineseSuccessful.length > 0) {
    console.log('🇨🇳 中国平台推荐:');
    chineseSuccessful
      .sort((a, b) => (b.dataQuality + b.accuracy) - (a.dataQuality + a.accuracy))
      .slice(0, 3)
      .forEach((result, index) => {
        console.log(`  ${index + 1}. ${result.platform} - 综合评分: ${Math.round((result.dataQuality + result.accuracy) / 2)}%`);
      });
  }

  console.log('\n✅ 测试完成！');
  console.log(`⏰ 结束时间: ${new Date().toLocaleString()}`);
  console.log('='.repeat(60));

  return results;
}

// 运行测试
if (require.main === module) {
  runBaiduTest().catch(console.error);
}

module.exports = { runBaiduTest, testSinglePlatform };
