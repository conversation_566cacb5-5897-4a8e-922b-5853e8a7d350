// 调试增强API的数据处理过程
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 30000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function debugEnhancedAPI() {
  console.log('🔍 调试增强API数据处理...');
  
  try {
    console.log('\n📡 调用增强API...');
    const start = Date.now();
    
    const response = await fetch('http://localhost:3001/api/enhanced-ping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target: 'wobshare.us.kg', maxNodes: 50 }),
      timeout: 30000
    });
    
    const end = Date.now();
    const data = await response.json();
    
    console.log(`✅ 增强API响应: ${end - start}ms`);
    console.log(`📊 总节点数: ${data.results?.length || 0}`);
    
    if (data.results && data.results.length > 0) {
      // 按API来源分组分析
      const apiGroups = {};
      data.results.forEach(result => {
        const source = result.apiSource || '未知';
        if (!apiGroups[source]) {
          apiGroups[source] = [];
        }
        apiGroups[source].push(result);
      });
      
      console.log('\n📊 按API来源分组分析:');
      Object.entries(apiGroups).forEach(([source, results]) => {
        console.log(`\n${source} (${results.length}个节点):`);
        
        // 计算统计信息
        const successResults = results.filter(r => r.status === 'success');
        const successRate = Math.round((successResults.length / results.length) * 100);
        
        if (successResults.length > 0) {
          const pings = successResults.map(r => r.ping);
          const avgPing = Math.round(pings.reduce((a, b) => a + b, 0) / pings.length);
          const minPing = Math.min(...pings);
          const maxPing = Math.max(...pings);
          
          console.log(`  📈 成功率: ${successResults.length}/${results.length} (${successRate}%)`);
          console.log(`  ⚡ 延迟: ${minPing}ms - ${maxPing}ms (平均${avgPing}ms)`);
        }
        
        // 显示前5个结果的详细信息
        console.log('  📋 详细结果:');
        results.slice(0, 5).forEach((result, index) => {
          const status = result.status === 'success' ? '✅' : '❌';
          console.log(`    ${index + 1}. ${status} ${result.node} - ${result.province} - ${result.ping}ms`);
        });
      });
      
      // 特别关注Globalping数据
      const globalpingResults = data.results.filter(r => r.apiSource === 'Globalping');
      if (globalpingResults.length > 0) {
        console.log('\n🎯 Globalping数据详细分析:');
        globalpingResults.forEach((result, index) => {
          console.log(`${index + 1}. ${result.node}`);
          console.log(`   省份: ${result.province}`);
          console.log(`   延迟: ${result.ping}ms`);
          console.log(`   状态: ${result.status}`);
          console.log(`   测试方法: ${result.testMethod || '未知'}`);
          console.log(`   位置信息: ${JSON.stringify(result.location || {})}`);
          console.log('');
        });
        
        // 检查是否有异常高的延迟
        const highLatency = globalpingResults.filter(r => r.ping > 200);
        if (highLatency.length > 0) {
          console.log(`⚠️ 发现${highLatency.length}个高延迟Globalping节点 (>200ms):`);
          highLatency.forEach(result => {
            console.log(`  - ${result.node}: ${result.ping}ms`);
          });
        }
      } else {
        console.log('\n❌ 增强API中没有找到Globalping数据！');
      }
      
      // 检查数据是否被"智能检测"覆盖
      const smartDetection = data.results.filter(r => r.apiSource === '智能检测');
      if (smartDetection.length > 0) {
        console.log(`\n🤖 智能检测数据: ${smartDetection.length}个节点`);
        console.log('这些可能是补充的模拟数据，用于填补缺失的省份');
      }
      
    }
    
  } catch (error) {
    console.error('❌ 调试失败:', error.message);
  }
  
  console.log('\n✅ 调试完成！');
}

debugEnhancedAPI();
