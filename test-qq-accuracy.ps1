# QQ.com 延迟准确性测试

$apiUrl = "https://ping.wobshare.us.kg/api/hybrid-ping"

Write-Host "🎯 QQ.com 延迟准确性测试" -ForegroundColor Green
Write-Host ("=" * 50) -ForegroundColor Yellow

# 1. 测试本地ping
Write-Host "`n📍 本地ping测试:" -ForegroundColor Cyan
$pingResult = ping qq.com -n 4
$pingResult | ForEach-Object { Write-Host $_ -ForegroundColor White }

# 提取平均延迟
$avgLine = $pingResult | Where-Object { $_ -match "平均 = (\d+)ms" }
if ($avgLine) {
    $localAvg = [regex]::Match($avgLine, "平均 = (\d+)ms").Groups[1].Value
    Write-Host "本地ping平均延迟: ${localAvg}ms" -ForegroundColor Green
} else {
    $localAvg = "未知"
    Write-Host "无法提取本地ping平均延迟" -ForegroundColor Red
}

# 2. 测试混合策略API
Write-Host "`n🌐 混合策略API测试:" -ForegroundColor Cyan

try {
    $body = @{target = "qq.com"} | ConvertTo-Json
    $response = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -Body $body -TimeoutSec 30
    
    Write-Host "测试方法: $($response.metadata.testMethod)" -ForegroundColor White
    Write-Host "网站类别: $($response.metadata.category)" -ForegroundColor White
    Write-Host "API预测延迟: $($response.metadata.averageLatency)ms" -ForegroundColor White
    Write-Host "置信度: $([math]::Round($response.metadata.confidence * 100, 1))%" -ForegroundColor White
    
    $apiLatency = $response.metadata.averageLatency
    
    # 3. 计算误差
    if ($localAvg -ne "未知") {
        $error = [math]::Abs($apiLatency - [int]$localAvg)
        $errorPercent = [math]::Round(($error / [int]$localAvg) * 100, 1)
        
        Write-Host "`n📊 准确性分析:" -ForegroundColor Yellow
        Write-Host "本地ping: ${localAvg}ms" -ForegroundColor White
        Write-Host "API预测: ${apiLatency}ms" -ForegroundColor White
        Write-Host "绝对误差: ${error}ms" -ForegroundColor White
        Write-Host "相对误差: ${errorPercent}%" -ForegroundColor White
        
        if ($error -le 20) {
            Write-Host "✅ 准确性评估: 优秀 (误差 ≤ 20ms)" -ForegroundColor Green
        } elseif ($error -le 50) {
            Write-Host "⚠️ 准确性评估: 良好 (误差 ≤ 50ms)" -ForegroundColor Yellow
        } else {
            Write-Host "❌ 准确性评估: 需要改进 (误差 > 50ms)" -ForegroundColor Red
        }
    }
    
} catch {
    Write-Host "API测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n测试完成" -ForegroundColor Green
