'use client';

import { useState, useEffect, memo } from 'react';
import { Activity, Globe, Clock } from 'lucide-react';
import <PERSON><PERSON><PERSON> from './PingChart';

interface NetworkNode {
  id: string;
  name: string;
  location: string;
  status: 'online' | 'offline' | 'warning';
  ping: number;
  uptime: number;
  x: number;
  y: number;
}

interface NetworkMonitorProps {
  isDarkMode: boolean;
  setIsDarkMode: (isDarkMode: boolean) => void;
}

const NetworkMonitor = ({ isDarkMode, setIsDarkMode }: NetworkMonitorProps) => {
  const [nodes, setNodes] = useState<NetworkNode[]>([
    { id: '1', name: '北京节点', location: '北京', status: 'online', ping: 15, uptime: 99.9, x: 116.4, y: 39.9 },
    { id: '2', name: '上海节点', location: '上海', status: 'online', ping: 12, uptime: 99.8, x: 121.5, y: 31.2 },
    { id: '3', name: '广州节点', location: '广州', status: 'warning', ping: 25, uptime: 98.5, x: 113.3, y: 23.1 },
    { id: '4', name: '深圳节点', location: '深圳', status: 'online', ping: 18, uptime: 99.7, x: 114.1, y: 22.5 },
    { id: '5', name: '杭州节点', location: '杭州', status: 'online', ping: 14, uptime: 99.6, x: 120.2, y: 30.3 },
    { id: '6', name: '成都节点', location: '成都', status: 'online', ping: 22, uptime: 99.4, x: 104.1, y: 30.7 },
    { id: '7', name: '西安节点', location: '西安', status: 'online', ping: 19, uptime: 99.5, x: 108.9, y: 34.3 },
    { id: '8', name: '武汉节点', location: '武汉', status: 'online', ping: 16, uptime: 99.8, x: 114.3, y: 30.6 },
    { id: '9', name: '南京节点', location: '南京', status: 'online', ping: 13, uptime: 99.9, x: 118.8, y: 32.1 },
    { id: '10', name: '天津节点', location: '天津', status: 'online', ping: 17, uptime: 99.6, x: 117.2, y: 39.1 },
    { id: '11', name: '重庆节点', location: '重庆', status: 'warning', ping: 28, uptime: 98.2, x: 106.5, y: 29.6 },
    { id: '12', name: '青岛节点', location: '青岛', status: 'online', ping: 20, uptime: 99.3, x: 120.4, y: 36.1 },
    { id: '13', name: '大连节点', location: '大连', status: 'online', ping: 21, uptime: 99.2, x: 121.6, y: 38.9 },
    { id: '14', name: '厦门节点', location: '厦门', status: 'online', ping: 24, uptime: 99.1, x: 118.1, y: 24.5 },
    { id: '15', name: '长沙节点', location: '长沙', status: 'online', ping: 18, uptime: 99.4, x: 113.0, y: 28.2 },
    { id: '16', name: '郑州节点', location: '郑州', status: 'offline', ping: 0, uptime: 0, x: 113.6, y: 34.8 },
    { id: '17', name: '济南节点', location: '济南', status: 'online', ping: 19, uptime: 99.5, x: 117.0, y: 36.7 },
    { id: '18', name: '沈阳节点', location: '沈阳', status: 'online', ping: 23, uptime: 99.0, x: 123.4, y: 41.8 },
    { id: '19', name: '哈尔滨节点', location: '哈尔滨', status: 'online', ping: 26, uptime: 98.8, x: 126.6, y: 45.8 },
    { id: '20', name: '昆明节点', location: '昆明', status: 'online', ping: 30, uptime: 98.9, x: 102.7, y: 25.0 },
    { id: '21', name: '贵阳节点', location: '贵阳', status: 'online', ping: 27, uptime: 99.1, x: 106.7, y: 26.6 },
    { id: '22', name: '兰州节点', location: '兰州', status: 'online', ping: 32, uptime: 98.7, x: 103.8, y: 36.1 },
    { id: '23', name: '银川节点', location: '银川', status: 'online', ping: 29, uptime: 98.9, x: 106.2, y: 38.5 },
    { id: '24', name: '西宁节点', location: '西宁', status: 'online', ping: 35, uptime: 98.5, x: 101.8, y: 36.6 },
    { id: '25', name: '乌鲁木齐节点', location: '乌鲁木齐', status: 'warning', ping: 45, uptime: 97.8, x: 87.6, y: 43.8 },
    { id: '26', name: '拉萨节点', location: '拉萨', status: 'online', ping: 42, uptime: 98.2, x: 91.1, y: 29.7 },
    { id: '27', name: '呼和浩特节点', location: '呼和浩特', status: 'online', ping: 24, uptime: 99.2, x: 111.7, y: 40.8 },
    { id: '28', name: '石家庄节点', location: '石家庄', status: 'online', ping: 18, uptime: 99.4, x: 114.5, y: 38.0 },
  ]);

  const [selectedNode, setSelectedNode] = useState<NetworkNode | null>(null);
  const [currentTime, setCurrentTime] = useState('');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    setCurrentTime(new Date().toLocaleString('zh-CN'));

    const timer = setInterval(() => {
      setCurrentTime(new Date().toLocaleString('zh-CN'));
      // 模拟实时数据更新
      setNodes(prevNodes =>
        prevNodes.map(node => ({
          ...node,
          ping: node.status === 'offline' ? 0 : Math.max(5, node.ping + Math.floor(Math.random() * 10) - 5),
        }))
      );
    }, 5000);

    return () => clearInterval(timer);
  }, []);

  const onlineNodes = nodes.filter(node => node.status === 'online').length;
  const warningNodes = nodes.filter(node => node.status === 'warning').length;
  const offlineNodes = nodes.filter(node => node.status === 'offline').length;
  const avgPing = Math.round(nodes.filter(node => node.status !== 'offline').reduce((sum, node) => sum + node.ping, 0) / (nodes.length - offlineNodes));

  if (!mounted) {
    return (
      <div className={`min-h-screen flex items-center justify-center transition-colors duration-300 ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className={`transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>加载网络监控系统...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      {/* Header */}
      <header className={`shadow-sm border-b transition-colors duration-300 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} relative`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Activity className="h-8 w-8 text-blue-600" />
                <div>
                  <h1 className={`text-xl font-bold transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>网络监控</h1>
                  <p className={`text-xs transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Network Monitoring System</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Globe className="h-6 w-6 text-blue-600" />
              <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>实时监控中...</span>
            </div>
          </div>
        </div>


      </header>

      {/* Status Bar */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className={`rounded-lg shadow-sm p-4 mb-6 transition-colors duration-300 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <div className="flex items-center justify-between">
            <div className={`flex items-center space-x-4 text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>{currentTime}</span>
              </div>
            </div>
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>正常 {onlineNodes}</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>警告 {warningNodes}</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>离线 {offlineNodes}</span>
              </div>
            </div>
          </div>
        </div>
      </div>



      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Network Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* 网络概况 */}
          <div className={`rounded-lg shadow-sm p-6 transition-colors duration-300 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <h3 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>网络概况</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>正常节点</span>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-green-600">{onlineNodes}</div>
                  <div className={`text-xs transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>{((onlineNodes / nodes.length) * 100).toFixed(1)}%</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>警告节点</span>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-yellow-600">{warningNodes}</div>
                  <div className={`text-xs transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>{((warningNodes / nodes.length) * 100).toFixed(1)}%</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>离线节点</span>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-red-600">{offlineNodes}</div>
                  <div className={`text-xs transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>{offlineNodes > 0 ? ((offlineNodes / nodes.length) * 100).toFixed(1) : '0'}%</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>总节点数</span>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-blue-600">{nodes.length}</div>
                  <div className={`text-xs transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>100%</div>
                </div>
              </div>
            </div>
          </div>

          {/* 性能指标 */}
          <div className={`rounded-lg shadow-sm p-6 transition-colors duration-300 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <h3 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>性能指标</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>平均延迟</span>
                <div className="text-right">
                  <div className="text-2xl font-bold text-blue-600">{avgPing}ms</div>
                  <div className={`text-xs transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>网络延迟</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>平均可用性</span>
                <div className="text-right">
                  <div className="text-2xl font-bold text-green-600">95.6%</div>
                  <div className={`text-xs transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>服务可用性</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>最佳节点</span>
                <div className="text-right">
                  <div className="text-2xl font-bold text-green-600">5ms</div>
                  <div className={`text-xs transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>最低</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>最慢节点</span>
                <div className="text-right">
                  <div className="text-2xl font-bold text-yellow-600">47ms</div>
                  <div className={`text-xs transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>最高</div>
                </div>
              </div>
            </div>
          </div>

          {/* 节点状态 */}
          <div className={`rounded-lg shadow-sm p-6 transition-colors duration-300 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <h3 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>节点状态</h3>
            <div className="h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-200 dark:scrollbar-thumb-gray-600 dark:scrollbar-track-gray-800">
              <div className="space-y-3 pr-2">
                {nodes.map((node) => (
                  <div key={node.id} className="flex items-center justify-between py-1">
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        node.status === 'online' ? 'bg-green-500' :
                        node.status === 'warning' ? 'bg-yellow-500 animate-pulse' : 'bg-red-500'
                      }`}></div>
                      <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        {node.location}
                      </span>
                    </div>
                    <div className="text-right">
                      {node.status === 'offline' ? (
                        <div className="text-lg font-bold text-red-600">离线</div>
                      ) : (
                        <div className={`text-lg font-bold ${
                          node.status === 'online' ? 'text-green-600' : 'text-yellow-600'
                        }`}>
                          {node.ping}ms
                        </div>
                      )}
                      <div className={`text-xs transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {node.status === 'offline' ? '不可用' : '延迟'}
                      </div>
                    </div>
                  </div>
                ))}
              </div>


            </div>
          </div>

          {/* 节点统计 */}
          <div className={`rounded-lg shadow-sm p-6 transition-colors duration-300 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <h3 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>节点统计</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>数据中心总数</span>
                <div className="text-right">
                  <div className="text-lg font-bold text-blue-600">28</div>
                  <div className={`text-xs transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>个</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>官方测试点</span>
                <div className="text-right">
                  <div className="text-lg font-bold text-green-600">21</div>
                  <div className={`text-xs transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>个</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>第三方测试点</span>
                <div className="text-right">
                  <div className="text-lg font-bold text-blue-600">3</div>
                  <div className={`text-xs transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>个</div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>监控覆盖率</span>
                <div className="text-right">
                  <div className="text-lg font-bold text-green-600">85.7%</div>
                  <div className={`text-xs transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>覆盖</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Network Latency Trend Chart */}
        <div className="mb-8">
          <PingChart nodes={nodes} isDarkMode={isDarkMode} />
        </div>

        {/* Selected Node Details */}
        {selectedNode && (
          <div className={`rounded-lg shadow-sm p-6 mb-6 transition-colors duration-300 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <h3 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>节点详情</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>节点名称</span>
                <div className={`text-sm font-medium mt-1 transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{selectedNode.name}</div>
              </div>
              <div className="text-center">
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>位置</span>
                <div className={`text-sm font-medium mt-1 transition-colors duration-300 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{selectedNode.location}</div>
              </div>
              <div className="text-center">
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>延迟</span>
                <div className="text-sm font-medium text-blue-600 mt-1">{selectedNode.ping}ms</div>
              </div>
              <div className="text-center">
                <span className={`text-sm transition-colors duration-300 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>可用性</span>
                <div className="text-sm font-medium text-green-600 mt-1">{selectedNode.uptime}%</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default memo(NetworkMonitor);