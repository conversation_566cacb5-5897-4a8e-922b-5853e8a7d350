import { NextApiRequest, NextApiResponse } from 'next';

// 🌐 真实Ping服务 - 基于Globalping API
// 放弃预测算法，改为调用真实的ping服务API

interface GlobalpingLocation {
  country?: string;
  city?: string;
  continent?: string;
  region?: string;
  asn?: number;
  tags?: string[];
}

interface GlobalpingRequest {
  type: 'ping';
  target: string;
  limit: number;
  locations?: GlobalpingLocation[];
  measurementOptions?: {
    packets?: number;
  };
}

interface GlobalpingProbe {
  continent: string;
  region: string;
  country: string;
  state?: string;
  city: string;
  asn: number;
  longitude: number;
  latitude: number;
  network: string;
  tags: string[];
}

interface GlobalpingResult {
  probe: GlobalpingProbe;
  result: {
    status: string;
    rawOutput: string;
    stats?: {
      min: number;
      max: number;
      avg: number;
      loss: number;
    };
  };
}

interface GlobalpingResponse {
  id: string;
  type: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  probesCount: number;
  results: GlobalpingResult[];
}

// 🎯 调用Globalping API进行真实ping测试
async function callGlobalpingAPI(target: string, limit: number = 10): Promise<GlobalpingResponse> {
  console.log(`🌐 调用Globalping API: ${target}, limit: ${limit}`);
  
  const requestBody: GlobalpingRequest = {
    type: 'ping',
    target: target,
    limit: limit,
    locations: [
      { country: 'CN' }, // 中国节点
      { country: 'HK' }, // 香港节点
      { country: 'TW' }, // 台湾节点
    ],
    measurementOptions: {
      packets: 3
    }
  };

  try {
    // 创建测试
    const createResponse = await fetch('https://api.globalping.io/v1/measurements', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'PingTool/1.0'
      },
      body: JSON.stringify(requestBody)
    });

    if (!createResponse.ok) {
      throw new Error(`Globalping API error: ${createResponse.status}`);
    }

    const createResult = await createResponse.json();
    const measurementId = createResult.id;
    
    console.log(`📊 测试创建成功，ID: ${measurementId}`);

    // 等待测试完成
    let attempts = 0;
    const maxAttempts = 30; // 最多等待30秒
    
    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
      
      const resultResponse = await fetch(`https://api.globalping.io/v1/measurements/${measurementId}`, {
        headers: {
          'User-Agent': 'PingTool/1.0'
        }
      });

      if (!resultResponse.ok) {
        throw new Error(`Failed to get results: ${resultResponse.status}`);
      }

      const result: GlobalpingResponse = await resultResponse.json();
      
      if (result.status === 'finished') {
        console.log(`✅ 测试完成，获得 ${result.results.length} 个结果`);
        return result;
      }
      
      attempts++;
      console.log(`⏳ 等待测试完成... (${attempts}/${maxAttempts})`);
    }
    
    throw new Error('测试超时');
  } catch (error) {
    console.error('❌ Globalping API调用失败:', error);
    throw error;
  }
}

// 🔄 转换Globalping结果为我们的格式
function convertGlobalpingResults(globalpingResponse: GlobalpingResponse, target: string) {
  const results = globalpingResponse.results
    .filter(result => result.result.status === 'finished' && result.result.stats)
    .map(result => {
      const probe = result.probe;
      const stats = result.result.stats!;
      
      // 构建节点名称
      let nodeName = probe.city;
      if (probe.state) {
        nodeName = `${probe.state}-${probe.city}`;
      }
      if (probe.country !== 'CN') {
        nodeName = `${probe.country}-${nodeName}`;
      }
      
      return {
        node: nodeName,
        ping: Math.round(stats.avg),
        status: 'success',
        timestamp: Date.now(),
        location: {
          country: probe.country,
          city: probe.city,
          latitude: probe.latitude,
          longitude: probe.longitude
        },
        testMethod: 'globalping-real',
        apiSource: 'Globalping',
        rawOutput: result.result.rawOutput,
        packetLoss: stats.loss,
        minLatency: stats.min,
        maxLatency: stats.max,
        avgLatency: stats.avg,
        asn: probe.asn,
        network: probe.network
      };
    });

  // 计算统计信息
  const latencies = results.map(r => r.ping);
  const averageLatency = latencies.length > 0 
    ? Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length)
    : 0;

  return {
    results,
    metadata: {
      totalNodes: results.length,
      successfulNodes: results.length,
      averageLatency,
      dataSource: 'Globalping Real Network Tests',
      testMethod: 'globalping-api',
      target,
      measurementId: globalpingResponse.id,
      fastMode: false
    },
    features: {
      monitoringEnabled: true,
      historicalDataAvailable: true,
      recommendationsGenerated: false
    },
    timestamp: new Date().toISOString()
  };
}

const handler = async function(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { target, maxNodes = 20 } = req.body;

    if (!target) {
      return res.status(400).json({ error: 'Target is required' });
    }

    console.log(`🎯 真实Ping测试请求: ${target}`);

    // 调用Globalping API
    const globalpingResponse = await callGlobalpingAPI(target, Math.min(maxNodes, 50));
    
    // 转换结果格式
    const response = convertGlobalpingResults(globalpingResponse, target);

    console.log(`✅ 返回 ${response.results.length} 个真实测试结果`);

    res.status(200).json(response);
  } catch (error) {
    console.error('❌ 真实Ping测试失败:', error);
    res.status(500).json({ 
      error: 'Real ping test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export default handler;
