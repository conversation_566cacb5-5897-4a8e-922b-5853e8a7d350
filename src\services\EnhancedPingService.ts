// 增强的Ping服务 - 多API混合策略
import { isBlockedDomain } from '../config/blockedSites';

export interface PingResult {
  node: string;
  province: string;
  ping: number;
  status: 'success' | 'timeout' | 'error';
  timestamp: number;
  location?: {
    city: string;
    country: string;
    region: string;
    province: string;
    latitude: number;
    longitude: number;
    asn: number;
    network: string;
  };
  apiSource?: string;
  testMethod?: string;
}

export interface PingAPIConfig {
  name: string;
  endpoint: string;
  headers?: Record<string, string>;
  timeout: number;
  rateLimit: number; // requests per minute
  reliability: number; // 1-10
  coverage: {
    china: number; // 1-10
    global: number; // 1-10
  };
  costLevel: 'free' | 'freemium' | 'paid';
}

// 高质量Ping API配置
export const ENHANCED_PING_APIS: PingAPIConfig[] = [
  {
    name: '17CE',
    endpoint: 'https://www.17ce.com/api/ping',
    timeout: 15000,
    rateLimit: 60,
    reliability: 9,
    coverage: { china: 10, global: 6 },
    costLevel: 'freemium'
  },
  {
    name: 'Pingdom',
    endpoint: 'https://api.pingdom.com/api/3.1/checks',
    headers: {
      'Authorization': 'Bearer YOUR_PINGDOM_TOKEN'
    },
    timeout: 10000,
    rateLimit: 300,
    reliability: 10,
    coverage: { china: 7, global: 10 },
    costLevel: 'paid'
  },
  {
    name: 'UptimeRobot',
    endpoint: 'https://api.uptimerobot.com/v2/getMonitors',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    timeout: 10000,
    rateLimit: 600,
    reliability: 8,
    coverage: { china: 5, global: 9 },
    costLevel: 'freemium'
  },
  {
    name: 'Cloudflare',
    endpoint: 'https://speed.cloudflare.com/api/v1/trace',
    timeout: 8000,
    rateLimit: 1000,
    reliability: 9,
    coverage: { china: 8, global: 10 },
    costLevel: 'free'
  },
  {
    name: 'KeyCDN',
    endpoint: 'https://tools.keycdn.com/api/performance',
    timeout: 12000,
    rateLimit: 100,
    reliability: 7,
    coverage: { china: 6, global: 8 },
    costLevel: 'free'
  }
];

export class EnhancedPingService {
  private apiUsage: Map<string, { count: number; lastReset: number }> = new Map();

  // 智能API选择策略
  selectOptimalAPIs(targetUrl: string, maxAPIs: number = 3): PingAPIConfig[] {
    const domain = new URL(targetUrl).hostname.toLowerCase();
    
    // 判断网站类型
    const isDomestic = this.isDomesticSite(domain);
    const isBlocked = this.isBlockedSite(domain);
    
    let sortedAPIs = [...ENHANCED_PING_APIS];
    
    if (isDomestic) {
      // 国内网站：优先选择中国覆盖好的API
      sortedAPIs.sort((a, b) => {
        const scoreA = a.coverage.china * 0.7 + a.reliability * 0.3;
        const scoreB = b.coverage.china * 0.7 + b.reliability * 0.3;
        return scoreB - scoreA;
      });
    } else if (isBlocked) {
      // 被墙网站：优先选择全球覆盖好且可靠的API
      sortedAPIs.sort((a, b) => {
        const scoreA = a.coverage.global * 0.6 + a.reliability * 0.4;
        const scoreB = b.coverage.global * 0.6 + b.reliability * 0.4;
        return scoreB - scoreA;
      });
    } else {
      // 国外网站：平衡选择
      sortedAPIs.sort((a, b) => {
        const scoreA = (a.coverage.china + a.coverage.global) * 0.5 + a.reliability * 0.5;
        const scoreB = (b.coverage.china + b.coverage.global) * 0.5 + b.reliability * 0.5;
        return scoreB - scoreA;
      });
    }
    
    // 过滤掉超出速率限制的API
    const availableAPIs = sortedAPIs.filter(api => this.canUseAPI(api.name));
    
    return availableAPIs.slice(0, maxAPIs);
  }

  // 并发测试多个API
  async performEnhancedPing(targetUrl: string): Promise<PingResult[]> {
    const selectedAPIs = this.selectOptimalAPIs(targetUrl);
    console.log(`🎯 选择的API: ${selectedAPIs.map(api => api.name).join(', ')}`);
    
    const promises = selectedAPIs.map(api => 
      this.callPingAPI(api, targetUrl).catch(error => {
        console.warn(`❌ ${api.name} API失败:`, error.message);
        return [];
      })
    );
    
    const results = await Promise.allSettled(promises);
    const allResults: PingResult[] = [];
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.length > 0) {
        console.log(`✅ ${selectedAPIs[index].name} 返回 ${result.value.length} 个节点`);
        allResults.push(...result.value);
      }
    });
    
    return this.deduplicateAndSort(allResults);
  }

  // 调用具体的Ping API
  private async callPingAPI(api: PingAPIConfig, targetUrl: string): Promise<PingResult[]> {
    this.recordAPIUsage(api.name);
    
    switch (api.name) {
      case '17CE':
        return this.call17CEAPI(targetUrl);
      case 'Pingdom':
        return this.callPingdomAPI(targetUrl);
      case 'UptimeRobot':
        return this.callUptimeRobotAPI(targetUrl);
      case 'Cloudflare':
        return this.callCloudflareAPI(targetUrl);
      case 'KeyCDN':
        return this.callKeyCDNAPI(targetUrl);
      default:
        throw new Error(`未支持的API: ${api.name}`);
    }
  }

  // 17CE API实现
  private async call17CEAPI(targetUrl: string): Promise<PingResult[]> {
    // 注意：17CE需要特殊的请求格式和可能的验证
    const response = await fetch('/api/17ce-proxy', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url: targetUrl })
    });
    
    if (!response.ok) throw new Error('17CE API请求失败');
    
    const data = await response.json();
    return this.parse17CEResponse(data);
  }

  // Cloudflare API实现（利用全球数据中心）
  private async callCloudflareAPI(targetUrl: string): Promise<PingResult[]> {
    const response = await fetch('/api/cloudflare-ping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url: targetUrl })
    });
    
    if (!response.ok) throw new Error('Cloudflare API请求失败');
    
    const data = await response.json();
    return this.parseCloudflareResponse(data);
  }

  // 其他API实现...
  private async callPingdomAPI(targetUrl: string): Promise<PingResult[]> {
    // Pingdom API实现
    throw new Error('Pingdom API需要付费账户');
  }

  private async callUptimeRobotAPI(targetUrl: string): Promise<PingResult[]> {
    // UptimeRobot API实现
    throw new Error('UptimeRobot API实现中');
  }

  private async callKeyCDNAPI(targetUrl: string): Promise<PingResult[]> {
    // 使用现有的KeyCDN实现
    const response = await fetch('/api/keycdn-ping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url: targetUrl })
    });
    
    if (!response.ok) throw new Error('KeyCDN API请求失败');
    
    const data = await response.json();
    return data.results || [];
  }

  // 解析响应数据
  private parse17CEResponse(data: any): PingResult[] {
    // 解析17CE的响应格式
    return [];
  }

  private parseCloudflareResponse(data: any): PingResult[] {
    // 解析Cloudflare的响应格式
    return [];
  }

  // 工具方法
  private isDomesticSite(domain: string): boolean {
    const domesticDomains = [
      'baidu.com', 'qq.com', 'taobao.com', 'tmall.com', 'jd.com',
      'weibo.com', 'sina.com', 'sohu.com', 'netease.com', '163.com',
      'alipay.com', 'aliyun.com', 'tencent.com', 'bilibili.com'
    ];
    return domesticDomains.some(d => domain.includes(d));
  }

  private isBlockedSite(domain: string): boolean {
    return isBlockedDomain(domain);
  }

  private canUseAPI(apiName: string): boolean {
    const usage = this.apiUsage.get(apiName);
    if (!usage) return true;
    
    const now = Date.now();
    const oneMinute = 60 * 1000;
    
    if (now - usage.lastReset > oneMinute) {
      this.apiUsage.set(apiName, { count: 0, lastReset: now });
      return true;
    }
    
    const api = ENHANCED_PING_APIS.find(a => a.name === apiName);
    return usage.count < (api?.rateLimit || 60);
  }

  private recordAPIUsage(apiName: string): void {
    const usage = this.apiUsage.get(apiName) || { count: 0, lastReset: Date.now() };
    usage.count++;
    this.apiUsage.set(apiName, usage);
  }

  private deduplicateAndSort(results: PingResult[]): PingResult[] {
    // 去重和排序逻辑
    const uniqueResults = results.filter((result, index, self) => 
      index === self.findIndex(r => r.node === result.node && r.province === result.province)
    );
    
    return uniqueResults.sort((a, b) => a.ping - b.ping);
  }
}

export const enhancedPingService = new EnhancedPingService();
