// 测试ping.pe的API接口
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 15000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data)),
          text: () => Promise.resolve(data)
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testPingPE() {
  console.log('🔍 测试ping.pe API...');
  
  try {
    // 1. 访问ping.pe主页
    console.log('\n📡 访问ping.pe主页...');
    
    const response = await fetch('https://ping.pe/', {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Connection': 'keep-alive'
      },
      timeout: 10000
    });
    
    console.log(`✅ ping.pe响应状态: ${response.status}`);
    
    if (response.ok) {
      const html = await response.text();
      console.log(`📄 页面长度: ${html.length} 字符`);
      
      // 查找API相关信息
      const apiMatches = html.match(/api[^"'\s]*/gi);
      if (apiMatches) {
        console.log('🔍 发现API相关内容:', [...new Set(apiMatches)].slice(0, 5));
      }
      
      // 查找可能的端点
      const endpointMatches = html.match(/\/[a-z]+\/[a-z]+/gi);
      if (endpointMatches) {
        console.log('🔍 发现可能的端点:', [...new Set(endpointMatches)].slice(0, 10));
      }
    }
    
    // 2. 尝试直接ping测试
    console.log('\n📡 尝试ping测试...');
    
    const pingUrl = 'https://ping.pe/google.com';
    const pingResponse = await fetch(pingUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Referer': 'https://ping.pe/'
      },
      timeout: 15000
    });
    
    console.log(`📡 ping测试响应状态: ${pingResponse.status}`);
    
    if (pingResponse.ok) {
      const pingHtml = await pingResponse.text();
      console.log(`📄 ping页面长度: ${pingHtml.length} 字符`);
      
      // 查找ping结果
      const pingResults = pingHtml.match(/\d+\.\d+\s*ms/gi);
      if (pingResults) {
        console.log('🎯 发现ping结果:', pingResults.slice(0, 10));
      }
      
      // 查找节点信息
      const locationMatches = pingHtml.match(/[A-Z]{2,3}\s*\|\s*[^<\n]+/gi);
      if (locationMatches) {
        console.log('🌍 发现节点信息:', locationMatches.slice(0, 5));
      }
    }
    
    // 3. 尝试API端点
    console.log('\n🔍 尝试API端点...');
    
    const apiPaths = [
      '/api/ping/google.com',
      '/api/v1/ping/google.com',
      '/ping/api/google.com',
      '/json/google.com',
      '/api/test/google.com'
    ];
    
    for (const path of apiPaths) {
      try {
        const apiUrl = `https://ping.pe${path}`;
        console.log(`📡 测试: ${apiUrl}`);
        
        const apiResponse = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Referer': 'https://ping.pe/'
          },
          timeout: 8000
        });
        
        if (apiResponse.ok) {
          console.log(`✅ ${path} - 状态: ${apiResponse.status}`);
          const data = await apiResponse.text();
          
          // 尝试解析JSON
          try {
            const jsonData = JSON.parse(data);
            console.log(`   JSON响应:`, JSON.stringify(jsonData, null, 2).substring(0, 300));
          } catch (e) {
            console.log(`   文本响应: ${data.substring(0, 200)}...`);
          }
        } else {
          console.log(`❌ ${path} - 状态: ${apiResponse.status}`);
        }
      } catch (error) {
        console.log(`❌ ${path} - 错误: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('❌ ping.pe测试失败:', error.message);
  }
  
  console.log('\n✅ ping.pe API测试完成！');
}

// 测试其他可能的免费ping服务
async function testOtherFreePingServices() {
  console.log('\n🔍 测试其他免费ping服务...');
  
  const services = [
    {
      name: 'ping.sx',
      url: 'https://ping.sx/google.com',
      description: 'ping.pe的替代品'
    },
    {
      name: 'ipinfo.io',
      url: 'https://ipinfo.io/*******/json',
      description: 'IP信息服务，可能有ping功能'
    },
    {
      name: 'httpbin.org',
      url: 'https://httpbin.org/ip',
      description: 'HTTP测试服务'
    }
  ];
  
  for (const service of services) {
    try {
      console.log(`\n📡 测试 ${service.name} (${service.description})...`);
      
      const response = await fetch(service.url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'application/json, text/html, */*'
        },
        timeout: 8000
      });
      
      console.log(`✅ ${service.name} - 状态: ${response.status}`);
      
      if (response.ok) {
        const data = await response.text();
        console.log(`📄 响应长度: ${data.length} 字符`);
        
        // 尝试解析JSON
        try {
          const jsonData = JSON.parse(data);
          console.log(`   JSON响应:`, JSON.stringify(jsonData, null, 2).substring(0, 200));
        } catch (e) {
          console.log(`   文本响应: ${data.substring(0, 100)}...`);
        }
      }
      
    } catch (error) {
      console.log(`❌ ${service.name} 测试失败: ${error.message}`);
    }
  }
}

async function main() {
  await testPingPE();
  await testOtherFreePingServices();
  
  console.log('\n📋 总结建议:');
  console.log('1. 保留现有的BOCE和ITDOG API - 它们工作良好');
  console.log('2. 移除Globalping - 数据质量有问题');
  console.log('3. 考虑自建SmartPing节点 - 更可控的解决方案');
  console.log('4. 或者寻找其他稳定的国内ping服务');
}

main();
