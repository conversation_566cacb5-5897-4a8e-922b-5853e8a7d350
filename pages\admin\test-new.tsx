import React, { useState, useEffect } from 'react';
import Head from 'next/head';

interface APITestResult {
  apiName: string;
  success: boolean;
  results: any[];
  error?: string;
  duration: number;
  status: 'loading' | 'success' | 'error' | 'pending';
  nodes: number;
  avgPing: number;
  priority: number;
}

interface TestSession {
  target: string;
  timestamp: string;
  apiResults: APITestResult[];
  totalDuration: number;
}

export default function AdminTestPageNew() {
  const [testUrl, setTestUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentTest, setCurrentTest] = useState<TestSession | null>(null);
  const [testHistory, setTestHistory] = useState<TestSession[]>([]);

  // 所有API平台配置 - 16个平台 (包含4个免费平台)
  const apiPlatforms = [
    // 中国平台 (免费)
    { name: 'ITDOG', icon: '🇨🇳', description: '中国主力平台', priority: 1, category: 'china', free: true },
    { name: '17CE', icon: '🔍', description: '中国备用平台', priority: 2, category: 'china', free: true },
    { name: 'Chinaz', icon: '🛠️', description: '站长工具', priority: 3, category: 'china', free: true },

    // 免费全球平台 (无需API密钥)
    { name: 'Globalping', icon: '🌍', description: '免费全球ping', priority: 1, category: 'free', free: true },
    { name: 'KeyCDN', icon: '🔧', description: '免费CDN工具', priority: 2, category: 'free', free: true },
    { name: 'IPInfo', icon: '📍', description: '免费IP服务', priority: 3, category: 'free', free: true },
    { name: 'Just-Ping', icon: '🎯', description: '免费ping服务', priority: 4, category: 'free', free: true },

    // 国际平台 (需要API密钥)
    { name: 'Freshping', icon: '🌍', description: '国际主力', priority: 1, category: 'international', free: false },
    { name: 'HetrixTools', icon: '🔧', description: '国际备用', priority: 2, category: 'international', free: false },
    { name: 'StatusCake', icon: '🍰', description: '专业监控', priority: 3, category: 'international', free: false },
    { name: 'UptimeRobot', icon: '🤖', description: '流行服务', priority: 4, category: 'international', free: false },
    { name: 'Site24x7', icon: '🌐', description: '企业级监控', priority: 5, category: 'international', free: false },
    { name: 'BetterUptime', icon: '⚡', description: '现代平台', priority: 6, category: 'international', free: false },
    { name: 'Checkly', icon: '✅', description: 'API专家', priority: 7, category: 'international', free: false },

    // 全球平台
    { name: 'Ping.pe', icon: '🌏', description: '全球服务', priority: 8, category: 'global', free: true },

    // 降级平台
    { name: 'Multi-Platform', icon: '☁️', description: '云服务降级', priority: 9, category: 'fallback', free: true }
  ];

  // 预设测试网站
  const presetUrls = [
    { url: 'https://www.baidu.com/', name: '百度', flag: '🇨🇳', category: 'china' },
    { url: 'https://www.google.com/', name: '谷歌', flag: '🌍', category: 'global' },
    { url: 'https://www.qq.com/', name: 'QQ', flag: '🇨🇳', category: 'china' },
    { url: 'https://www.facebook.com/', name: 'Facebook', flag: '🌍', category: 'global' },
    { url: 'https://www.taobao.com/', name: '淘宝', flag: '🇨🇳', category: 'china' },
    { url: 'https://www.youtube.com/', name: 'YouTube', flag: '🌍', category: 'global' }
  ];

  const startComprehensiveTest = async (url: string) => {
    if (!url.trim()) {
      alert('请输入要测试的URL');
      return;
    }

    setIsLoading(true);
    const startTime = Date.now();
    
    // 初始化测试会话
    const testSession: TestSession = {
      target: url,
      timestamp: new Date().toISOString(),
      apiResults: apiPlatforms.map(platform => ({
        apiName: platform.name,
        success: false,
        results: [],
        duration: 0,
        status: 'pending' as const,
        nodes: 0,
        avgPing: 0,
        priority: platform.priority
      })),
      totalDuration: 0
    };

    setCurrentTest(testSession);

    try {
      // 调用全平台测试API
      console.log(`🚀 开始全平台测试: ${url}`);

      const response = await fetch('/api/ping-comprehensive', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          target: url
        }),
      });

      const data = await response.json();
      const totalDuration = Date.now() - startTime;

      // 处理API结果，按平台分组
      const apiStats: { [key: string]: APITestResult } = {};

      // 初始化所有平台
      apiPlatforms.forEach(platform => {
        apiStats[platform.name] = {
          apiName: platform.name,
          success: false,
          results: [],
          duration: 0,
          status: 'error',
          nodes: 0,
          avgPing: 0,
          priority: platform.priority
        };
      });

      // 分析返回的结果 - 使用新的apiBreakdown数据
      if (data.success && data.apiBreakdown) {
        Object.entries(data.apiBreakdown).forEach(([apiName, results]: [string, any[]]) => {
          if (apiStats[apiName]) {
            apiStats[apiName].success = results.length > 0;
            apiStats[apiName].status = results.length > 0 ? 'success' : 'error';
            apiStats[apiName].results = results;
            apiStats[apiName].nodes = results.length;

            // 计算平均延迟
            if (results.length > 0) {
              const validPings = results.filter(r => r.ping > 0).map(r => r.ping);
              if (validPings.length > 0) {
                apiStats[apiName].avgPing = Math.round(
                  validPings.reduce((sum, ping) => sum + ping, 0) / validPings.length
                );
              }
            }
          }
        });
      }

      // 如果没有apiBreakdown，尝试从results中解析
      if (data.success && data.results && !data.apiBreakdown) {
        data.results.forEach((result: any) => {
          const apiSource = result.apiSource || result.testMethod || 'Unknown';

          // 匹配API平台
          let matchedPlatform = null;
          for (const platform of apiPlatforms) {
            if (apiSource.includes(platform.name) ||
                result.testMethod?.includes(platform.name) ||
                (platform.name === 'Multi-Platform' && apiSource.includes('Multi'))) {
              matchedPlatform = platform.name;
              break;
            }
          }

          if (matchedPlatform && apiStats[matchedPlatform]) {
            apiStats[matchedPlatform].success = true;
            apiStats[matchedPlatform].status = 'success';
            apiStats[matchedPlatform].results.push(result);
            apiStats[matchedPlatform].nodes++;

            if (result.ping > 0) {
              const currentAvg = apiStats[matchedPlatform].avgPing;
              const currentCount = apiStats[matchedPlatform].nodes;
              apiStats[matchedPlatform].avgPing =
                Math.round((currentAvg * (currentCount - 1) + result.ping) / currentCount);
            }
          }
        });
      }

      // 更新测试会话
      const finalTestSession: TestSession = {
        ...testSession,
        apiResults: Object.values(apiStats),
        totalDuration
      };

      setCurrentTest(finalTestSession);
      setTestHistory(prev => [finalTestSession, ...prev.slice(0, 4)]); // 保留最近5次测试

    } catch (error) {
      console.error('测试失败:', error);
      alert('测试失败: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-500';
      case 'error': return 'bg-red-500';
      case 'loading': return 'bg-yellow-500 animate-pulse';
      case 'pending': return 'bg-gray-300';
      default: return 'bg-gray-300';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'china': return 'border-red-200 bg-red-50';
      case 'free': return 'border-green-200 bg-green-50';
      case 'international': return 'border-blue-200 bg-blue-50';
      case 'global': return 'border-purple-200 bg-purple-50';
      case 'fallback': return 'border-gray-200 bg-gray-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <>
      <Head>
        <title>全平台API测试中心 - 真实延迟监控</title>
        <meta name="description" content="全平台API测试中心，真实延迟数据监控" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          
          {/* 页面标题 */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              🌐 智能Ping测试中心
            </h1>
            <p className="text-lg text-gray-600">
              ITDOG.CN + Globalping.io 主力架构，100%真实延迟数据
            </p>
            <div className="mt-4 flex justify-center space-x-4 text-sm">
              <span className="px-4 py-2 bg-red-100 text-red-800 rounded-full font-medium">
                🇨🇳 ITDOG.CN 主力 (中国网站)
              </span>
              <span className="px-4 py-2 bg-green-100 text-green-800 rounded-full font-medium">
                🌍 Globalping.io 主力 (国际网站)
              </span>
              <span className="px-4 py-2 bg-blue-100 text-blue-800 rounded-full font-medium">
                🆓 完全免费，无需密钥
              </span>
            </div>
            <div className="mt-2 text-sm text-gray-500">
              智能识别网站类型，自动选择最佳API平台 | 删除所有模拟数据，仅使用真实API
            </div>
          </div>

          {/* 测试输入区域 */}
          <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  输入测试URL
                </label>
                <input
                  type="url"
                  value={testUrl}
                  onChange={(e) => setTestUrl(e.target.value)}
                  placeholder="https://www.example.com/"
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={isLoading}
                />
              </div>
              <div className="flex flex-col justify-end">
                <button
                  onClick={() => startComprehensiveTest(testUrl)}
                  disabled={isLoading}
                  className="px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium"
                >
                  {isLoading ? '🔄 测试中...' : '🚀 开始全平台测试'}
                </button>
              </div>
            </div>

            {/* 预设网站快速测试 */}
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                快速测试预设网站
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                {presetUrls.map((preset, index) => (
                  <button
                    key={index}
                    onClick={() => startComprehensiveTest(preset.url)}
                    disabled={isLoading}
                    className={`p-3 rounded-xl border-2 hover:shadow-md transition-all duration-200 disabled:opacity-50 ${getCategoryColor(preset.category)}`}
                  >
                    <div className="text-2xl mb-1">{preset.flag}</div>
                    <div className="text-sm font-medium">{preset.name}</div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* API平台状态概览 */}
          <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              📊 API平台状态概览
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {apiPlatforms.map((platform, index) => {
                const apiResult = currentTest?.apiResults.find(r => r.apiName === platform.name);
                const status = apiResult?.status || 'pending';
                
                return (
                  <div
                    key={index}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 ${getCategoryColor(platform.category)}`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <span className="text-2xl mr-2">{platform.icon}</span>
                        <div>
                          <div className="font-bold text-gray-900">{platform.name}</div>
                          <div className="text-xs text-gray-600">{platform.description}</div>
                        </div>
                      </div>
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(status)}`}></div>
                    </div>
                    
                    {apiResult && apiResult.success && (
                      <div className="mt-3 space-y-1">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">节点数:</span>
                          <span className="font-medium">{apiResult.nodes}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">平均延迟:</span>
                          <span className="font-medium">{apiResult.avgPing}ms</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">优先级:</span>
                          <span className="font-medium">P{apiResult.priority}</span>
                        </div>
                      </div>
                    )}
                    
                    {apiResult && !apiResult.success && apiResult.error && (
                      <div className="mt-2 text-xs text-red-600">
                        {apiResult.error}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* 当前测试结果详情 */}
          {currentTest && (
            <div className="bg-white rounded-2xl shadow-xl p-8 mb-8">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-gray-900">
                  📈 测试结果详情
                </h2>
                <div className="text-sm text-gray-600">
                  测试时间: {new Date(currentTest.timestamp).toLocaleString()}
                </div>
              </div>
              
              <div className="mb-4 p-4 bg-gray-50 rounded-xl">
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium text-gray-900">目标网站: {currentTest.target}</div>
                    <div className="text-sm text-gray-600">总耗时: {currentTest.totalDuration}ms</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-600">成功平台数</div>
                    <div className="text-2xl font-bold text-green-600">
                      {currentTest.apiResults.filter(r => r.success).length}/{currentTest.apiResults.length}
                    </div>
                  </div>
                </div>
              </div>

              {/* 成功的API结果 */}
              <div className="space-y-4">
                {currentTest.apiResults
                  .filter(result => result.success)
                  .sort((a, b) => a.priority - b.priority)
                  .map((result, index) => (
                    <div key={index} className="border border-green-200 rounded-xl p-4 bg-green-50">
                      <div className="flex justify-between items-center mb-3">
                        <div className="flex items-center">
                          <span className="text-2xl mr-2">
                            {apiPlatforms.find(p => p.name === result.apiName)?.icon}
                          </span>
                          <div>
                            <div className="font-bold text-green-900">{result.apiName}</div>
                            <div className="text-sm text-green-700">
                              {result.nodes} 个节点 | 平均 {result.avgPing}ms | P{result.priority}
                            </div>
                          </div>
                        </div>
                        <div className="text-green-600 font-bold">✅ 成功</div>
                      </div>
                      
                      {result.results.length > 0 && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                          {result.results.slice(0, 6).map((node: any, nodeIndex: number) => (
                            <div key={nodeIndex} className="bg-white px-3 py-2 rounded-lg border text-sm">
                              <div className="font-medium">{node.node}</div>
                              <div className="text-gray-600">{node.province} | {node.ping}ms</div>
                            </div>
                          ))}
                          {result.results.length > 6 && (
                            <div className="text-gray-500 text-sm px-3 py-2">
                              ... 还有 {result.results.length - 6} 个节点
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
              </div>

              {/* 失败的API结果 */}
              {currentTest.apiResults.some(r => !r.success) && (
                <div className="mt-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-3">❌ 未成功的平台</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {currentTest.apiResults
                      .filter(result => !result.success)
                      .map((result, index) => (
                        <div key={index} className="border border-red-200 rounded-lg p-3 bg-red-50">
                          <div className="flex items-center">
                            <span className="text-xl mr-2">
                              {apiPlatforms.find(p => p.name === result.apiName)?.icon}
                            </span>
                            <div>
                              <div className="font-medium text-red-900">{result.apiName}</div>
                              <div className="text-xs text-red-700">
                                {result.error || '无数据返回'}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 测试历史 */}
          {testHistory.length > 0 && (
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                📚 测试历史
              </h2>
              
              <div className="space-y-4">
                {testHistory.map((test, index) => (
                  <div key={index} className="border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-medium text-gray-900">{test.target}</div>
                        <div className="text-sm text-gray-600">
                          {new Date(test.timestamp).toLocaleString()} | 
                          耗时: {test.totalDuration}ms
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-gray-600">成功率</div>
                        <div className="text-lg font-bold text-blue-600">
                          {Math.round((test.apiResults.filter(r => r.success).length / test.apiResults.length) * 100)}%
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
