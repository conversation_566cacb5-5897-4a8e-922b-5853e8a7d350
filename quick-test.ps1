$apiUrl = "https://ping.wobshare.us.kg/api/hybrid-ping"

Write-Host "Testing baidu.com (domestic)..."
$body1 = @{target = "baidu.com"} | ConvertTo-Json
$response1 = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -Body $body1
Write-Host "baidu.com: $($response1.averageLatency)ms | $($response1.testMethod) | $($response1.category)"

Start-Sleep -Seconds 2

Write-Host "Testing google.com (blocked)..."
$body2 = @{target = "google.com"} | ConvertTo-Json
$response2 = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -Body $body2
Write-Host "google.com: $($response2.averageLatency)ms | $($response2.testMethod) | $($response2.category)"

Start-Sleep -Seconds 2

Write-Host "Testing taobao.com (domestic)..."
$body3 = @{target = "taobao.com"} | ConvertTo-Json
$response3 = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -Body $body3
Write-Host "taobao.com: $($response3.averageLatency)ms | $($response3.testMethod) | $($response3.category)"

Start-Sleep -Seconds 2

Write-Host "Testing facebook.com (blocked)..."
$body4 = @{target = "facebook.com"} | ConvertTo-Json
$response4 = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -Body $body4
Write-Host "facebook.com: $($response4.averageLatency)ms | $($response4.testMethod) | $($response4.category)"

Start-Sleep -Seconds 2

Write-Host "Testing reddit.com (blocked)..."
$body5 = @{target = "reddit.com"} | ConvertTo-Json
$response5 = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -Body $body5
Write-Host "reddit.com: $($response5.averageLatency)ms | $($response5.testMethod) | $($response5.category)"

Write-Host "`nAnalysis:"
Write-Host "Domestic sites should be <100ms:"
Write-Host "  baidu.com: $($response1.averageLatency)ms - $(if ($response1.averageLatency -lt 100) {'PASS'} else {'FAIL'})"
Write-Host "  taobao.com: $($response3.averageLatency)ms - $(if ($response3.averageLatency -lt 100) {'PASS'} else {'FAIL'})"

Write-Host "Blocked sites should be >250ms:"
Write-Host "  google.com: $($response2.averageLatency)ms - $(if ($response2.averageLatency -gt 250) {'PASS'} else {'FAIL'})"
Write-Host "  facebook.com: $($response4.averageLatency)ms - $(if ($response4.averageLatency -gt 250) {'PASS'} else {'FAIL'})"
Write-Host "  reddit.com: $($response5.averageLatency)ms - $(if ($response5.averageLatency -gt 250) {'PASS'} else {'FAIL'})"
