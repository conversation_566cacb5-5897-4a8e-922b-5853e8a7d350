# 简单的混合策略准确性测试

$apiUrl = "https://ping.wobshare.us.kg/api/hybrid-ping"

$testWebsites = @(
    "baidu.com",
    "taobao.com", 
    "jd.com",
    "qq.com",
    "weibo.com",
    "google.com",
    "facebook.com",
    "twitter.com",
    "instagram.com",
    "reddit.com"
)

Write-Host "开始测试混合策略准确性..." -ForegroundColor Green
Write-Host "API地址: $apiUrl" -ForegroundColor Cyan
Write-Host ("=" * 60) -ForegroundColor Yellow

$results = @()

foreach ($website in $testWebsites) {
    Write-Host "`n测试网站: $website" -ForegroundColor Yellow
    
    try {
        $body = @{target = $website} | ConvertTo-Json
        $response = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -Body $body -TimeoutSec 30
        
        Write-Host "测试方法: $($response.metadata.testMethod)" -ForegroundColor Cyan
        Write-Host "网站类别: $($response.metadata.category)" -ForegroundColor Cyan
        Write-Host "预测延迟: $($response.metadata.averageLatency)ms" -ForegroundColor Cyan
        
        $results += @{
            website = $website
            success = $true
            predictedLatency = $response.metadata.averageLatency
            testMethod = $response.metadata.testMethod
            category = $response.metadata.category
        }
        
    } catch {
        Write-Host "测试失败: $($_.Exception.Message)" -ForegroundColor Red
        $results += @{
            website = $website
            success = $false
        }
    }
    
    Start-Sleep -Seconds 1
}

Write-Host "`n测试结果汇总:" -ForegroundColor Green
foreach ($result in $results) {
    if ($result.success) {
        Write-Host "$($result.website): $($result.predictedLatency)ms ($($result.category))" -ForegroundColor White
    }
}

Write-Host "`n测试完成" -ForegroundColor Green
