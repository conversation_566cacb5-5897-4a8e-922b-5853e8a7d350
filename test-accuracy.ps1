# 测试混合策略准确性

$apiUrl = "https://ping.wobshare.us.kg/api/hybrid-ping"

# 国内知名网站
$domesticWebsites = @(
    "baidu.com",
    "taobao.com", 
    "jd.com",
    "qq.com",
    "weibo.com",
    "zhihu.com",
    "bilibili.com",
    "xiaohongshu.com",
    "163.com",
    "iqiyi.com"
)

# 被墙的国外网站
$blockedWebsites = @(
    "google.com",
    "facebook.com",
    "twitter.com",
    "instagram.com",
    "reddit.com",
    "pinterest.com",
    "medium.com",
    "bbc.com",
    "netflix.com",
    "soundcloud.com"
)

# 合并所有测试网站
$testWebsites = $domesticWebsites + $blockedWebsites

Write-Host "开始测试混合策略准确性..." -ForegroundColor Green
Write-Host "API地址: $apiUrl" -ForegroundColor Cyan
Write-Host "测试时间: $(Get-Date)" -ForegroundColor Cyan
Write-Host ("=" * 60) -ForegroundColor Yellow

$results = @()

foreach ($website in $testWebsites) {
    Write-Host "`n测试网站: $website" -ForegroundColor Yellow
    Write-Host ("-" * 40) -ForegroundColor Gray
    
    try {
        $body = @{target = $website} | ConvertTo-Json
        $startTime = Get-Date
        
        $response = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -Body $body -TimeoutSec 30
        
        $endTime = Get-Date
        $apiLatency = ($endTime - $startTime).TotalMilliseconds
        
        Write-Host "API响应时间: $([math]::Round($apiLatency))ms" -ForegroundColor Green
        Write-Host "测试方法: $($response.metadata.testMethod)" -ForegroundColor Cyan
        Write-Host "网站类别: $($response.metadata.category)" -ForegroundColor Cyan
        Write-Host "置信度: $([math]::Round($response.metadata.confidence * 100, 1))%" -ForegroundColor Cyan
        Write-Host "预测延迟: $($response.metadata.averageLatency)ms" -ForegroundColor Cyan
        
        $results += @{
            website = $website
            success = $true
            apiLatency = [math]::Round($apiLatency)
            predictedLatency = $response.metadata.averageLatency
            testMethod = $response.metadata.testMethod
            category = $response.metadata.category
            confidence = $response.metadata.confidence
            isDomestic = $domesticWebsites -contains $website
            isBlocked = $blockedWebsites -contains $website
        }
        
    } catch {
        Write-Host "测试失败: $($_.Exception.Message)" -ForegroundColor Red
        $results += @{
            website = $website
            success = $false
            error = $_.Exception.Message
            isDomestic = $domesticWebsites -contains $website
            isBlocked = $blockedWebsites -contains $website
        }
    }
    
    Start-Sleep -Seconds 1
}

# 生成汇总报告
Write-Host "`n`n混合策略测试汇总报告" -ForegroundColor Green
Write-Host ("=" * 60) -ForegroundColor Yellow

$successfulTests = $results | Where-Object { $_.success -eq $true }
$failedTests = $results | Where-Object { $_.success -eq $false }

Write-Host "成功测试: $($successfulTests.Count)/$($results.Count)" -ForegroundColor Green
Write-Host "失败测试: $($failedTests.Count)/$($results.Count)" -ForegroundColor Red

if ($successfulTests.Count -gt 0) {
    Write-Host "`n国内网站测试结果:" -ForegroundColor Magenta
    $domesticResults = $successfulTests | Where-Object { $_.isDomestic -eq $true }
    foreach ($result in $domesticResults) {
        Write-Host "  $($result.website): $($result.predictedLatency)ms ($($result.category))" -ForegroundColor White
    }
    
    Write-Host "`n被墙网站测试结果:" -ForegroundColor Magenta
    $blockedResults = $successfulTests | Where-Object { $_.isBlocked -eq $true }
    foreach ($result in $blockedResults) {
        $status = if ($result.predictedLatency -gt 250) { "PASS" } else { "FAIL" }
        $color = if ($result.predictedLatency -gt 250) { "Green" } else { "Red" }
        Write-Host "  $($result.website): $($result.predictedLatency)ms ($($result.category)) - $status" -ForegroundColor $color
    }
}

Write-Host "`n测试完成时间: $(Get-Date)" -ForegroundColor Cyan
