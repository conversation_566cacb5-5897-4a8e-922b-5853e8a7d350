// 测试17CE.COM的API接口
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 15000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data)),
          text: () => Promise.resolve(data)
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function test17CE() {
  console.log('🔍 测试17CE.COM API...');
  
  try {
    // 1. 尝试直接访问17CE的ping接口
    console.log('\n📡 测试17CE ping接口...');
    
    // 17CE的ping测试页面
    const testUrl = 'https://www.17ce.com/site/ping';
    
    const response = await fetch(testUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
      },
      timeout: 10000
    });
    
    console.log(`✅ 17CE响应状态: ${response.status}`);
    
    if (response.ok) {
      const html = await response.text();
      console.log(`📄 页面长度: ${html.length} 字符`);
      
      // 查找API相关信息
      if (html.includes('api') || html.includes('API')) {
        console.log('🔍 页面中发现API相关内容');
      }
      
      // 查找表单或AJAX请求
      if (html.includes('ajax') || html.includes('xhr')) {
        console.log('🔍 页面中发现AJAX请求');
      }
      
      // 查找ping相关的JavaScript
      const jsMatches = html.match(/ping[^"']*\.js/gi);
      if (jsMatches) {
        console.log('🔍 发现ping相关JS文件:', jsMatches);
      }
      
      // 查找可能的API端点
      const apiMatches = html.match(/\/api\/[^"'\s]*/gi);
      if (apiMatches) {
        console.log('🔍 发现可能的API端点:', apiMatches);
      }
    }
    
    // 2. 尝试常见的API路径
    console.log('\n🔍 尝试常见API路径...');
    
    const apiPaths = [
      '/api/ping',
      '/api/test',
      '/ping/api',
      '/test/api',
      '/api/v1/ping',
      '/api/v2/ping'
    ];
    
    for (const path of apiPaths) {
      try {
        const apiUrl = `https://www.17ce.com${path}`;
        console.log(`📡 测试: ${apiUrl}`);
        
        const apiResponse = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Referer': 'https://www.17ce.com/'
          },
          timeout: 5000
        });
        
        if (apiResponse.ok) {
          console.log(`✅ ${path} - 状态: ${apiResponse.status}`);
          const data = await apiResponse.text();
          console.log(`   响应: ${data.substring(0, 200)}...`);
        } else {
          console.log(`❌ ${path} - 状态: ${apiResponse.status}`);
        }
      } catch (error) {
        console.log(`❌ ${path} - 错误: ${error.message}`);
      }
    }
    
    // 3. 尝试POST请求模拟表单提交
    console.log('\n📝 尝试POST请求...');
    
    try {
      const postResponse = await fetch('https://www.17ce.com/site/ping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Referer': 'https://www.17ce.com/site/ping',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: 'url=google.com&type=ping&area=all',
        timeout: 10000
      });
      
      console.log(`📝 POST响应状态: ${postResponse.status}`);
      
      if (postResponse.ok) {
        const postData = await postResponse.text();
        console.log(`📄 POST响应长度: ${postData.length}`);
        
        // 尝试解析JSON
        try {
          const jsonData = JSON.parse(postData);
          console.log('✅ 成功解析JSON响应:');
          console.log(JSON.stringify(jsonData, null, 2));
        } catch (e) {
          console.log('❌ 响应不是JSON格式');
          console.log(`前100字符: ${postData.substring(0, 100)}`);
        }
      }
    } catch (error) {
      console.log(`❌ POST请求失败: ${error.message}`);
    }
    
  } catch (error) {
    console.error('❌ 17CE测试失败:', error.message);
  }
  
  console.log('\n✅ 17CE API测试完成！');
}

// 测试其他可能的国内ping服务
async function testOtherServices() {
  console.log('\n🔍 测试其他国内ping服务...');
  
  const services = [
    {
      name: 'ping.chinaz.com',
      url: 'https://ping.chinaz.com/',
      testUrl: 'https://ping.chinaz.com/google.com'
    },
    {
      name: 'ping.aizhan.com', 
      url: 'https://ping.aizhan.com/',
      testUrl: 'https://ping.aizhan.com/google.com'
    },
    {
      name: 'tool.lu ping',
      url: 'https://tool.lu/ping/',
      testUrl: 'https://tool.lu/ping/'
    }
  ];
  
  for (const service of services) {
    try {
      console.log(`\n📡 测试 ${service.name}...`);
      
      const response = await fetch(service.testUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        },
        timeout: 8000
      });
      
      console.log(`✅ ${service.name} - 状态: ${response.status}`);
      
      if (response.ok) {
        const html = await response.text();
        console.log(`📄 页面长度: ${html.length} 字符`);
        
        // 查找API相关信息
        if (html.includes('api') || html.includes('API')) {
          console.log(`🔍 ${service.name} 页面中发现API相关内容`);
        }
      }
      
    } catch (error) {
      console.log(`❌ ${service.name} 测试失败: ${error.message}`);
    }
  }
}

async function main() {
  await test17CE();
  await testOtherServices();
}

main();
