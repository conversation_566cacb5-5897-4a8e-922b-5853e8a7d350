<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试新API优先级</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .container {
            background: #2d2d2d;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #005a9e;
        }
        .test-button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .result {
            background: #1e1e1e;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
        }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        .loading {
            color: #ffc107;
        }
        .api-source {
            display: inline-block;
            background: #007acc;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 5px;
        }
        .ping-value {
            font-weight: bold;
            color: #28a745;
        }
        .ping-high { color: #ffc107; }
        .ping-very-high { color: #dc3545; }
        pre {
            background: #1e1e1e;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🚀 测试新API优先级</h1>
    <p>新的优先级：ITDOG → BOCE → Globalping → 17CE → 其他</p>
    
    <div class="container">
        <h2>快速测试</h2>
        <button class="test-button" onclick="testSite('baidu.com', '百度')">测试百度</button>
        <button class="test-button" onclick="testSite('wobshare.us.kg', '你的网站')">测试你的网站</button>
        <button class="test-button" onclick="testSite('google.com', 'Google')">测试Google</button>
        <button class="test-button" onclick="testSite('github.com', 'GitHub')">测试GitHub</button>
    </div>

    <div class="container">
        <h2>自定义测试</h2>
        <input type="text" id="customUrl" placeholder="输入网址，如：example.com" style="padding: 8px; width: 300px; margin-right: 10px; background: #1e1e1e; color: white; border: 1px solid #555; border-radius: 3px;">
        <button class="test-button" onclick="testCustomSite()">测试</button>
    </div>

    <div id="results"></div>

    <script>
        async function testSite(url, name) {
            const resultsDiv = document.getElementById('results');
            const resultId = 'result-' + Date.now();
            
            // 添加加载状态
            resultsDiv.innerHTML = `
                <div class="result loading" id="${resultId}">
                    <h3>🔄 正在测试 ${name} (${url})...</h3>
                    <p>请稍候，正在调用新的API优先级...</p>
                </div>
            ` + resultsDiv.innerHTML;

            try {
                const startTime = Date.now();
                const response = await fetch('/api/enhanced-ping', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        target: url,
                        maxNodes: 20
                    })
                });

                const endTime = Date.now();
                const data = await response.json();

                if (data.success && data.results) {
                    // 按API来源分组
                    const apiSources = {};
                    data.results.forEach(result => {
                        const source = result.apiSource || 'Unknown';
                        if (!apiSources[source]) {
                            apiSources[source] = [];
                        }
                        apiSources[source].push(result.ping);
                    });

                    // 生成API来源统计
                    let apiSourcesHtml = '';
                    Object.entries(apiSources).forEach(([source, pings]) => {
                        const avgPing = Math.round(pings.reduce((sum, ping) => sum + ping, 0) / pings.length);
                        apiSourcesHtml += `<div><span class="api-source">${source}</span> ${pings.length}个节点, 平均${avgPing}ms</div>`;
                    });

                    // 获取前5个最快节点
                    const topNodes = data.results
                        .filter(r => r.status === 'success' && r.ping > 0)
                        .sort((a, b) => a.ping - b.ping)
                        .slice(0, 5);

                    let topNodesHtml = '';
                    topNodes.forEach((node, index) => {
                        const pingClass = node.ping > 200 ? 'ping-very-high' : node.ping > 100 ? 'ping-high' : 'ping-value';
                        topNodesHtml += `<div>${index + 1}. ${node.node} (${node.province}) - <span class="${pingClass}">${node.ping}ms</span> <span class="api-source">${node.apiSource}</span></div>`;
                    });

                    const avgLatency = data.metadata?.averageLatency || 0;
                    const avgClass = avgLatency > 200 ? 'ping-very-high' : avgLatency > 100 ? 'ping-high' : 'ping-value';

                    document.getElementById(resultId).innerHTML = `
                        <h3>✅ ${name} (${url}) 测试完成</h3>
                        <p><strong>响应时间:</strong> ${endTime - startTime}ms</p>
                        <p><strong>总节点数:</strong> ${data.results.length}</p>
                        <p><strong>平均延迟:</strong> <span class="${avgClass}">${avgLatency}ms</span></p>
                        
                        <h4>🔍 API来源分析:</h4>
                        ${apiSourcesHtml}
                        
                        <h4>🏆 最快的5个节点:</h4>
                        ${topNodesHtml}
                        
                        <details>
                            <summary>📊 完整数据 (点击展开)</summary>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </details>
                    `;
                    document.getElementById(resultId).className = 'result success';
                } else {
                    document.getElementById(resultId).innerHTML = `
                        <h3>❌ ${name} (${url}) 测试失败</h3>
                        <p>错误: ${data.error || '未知错误'}</p>
                    `;
                    document.getElementById(resultId).className = 'result error';
                }
            } catch (error) {
                document.getElementById(resultId).innerHTML = `
                    <h3>❌ ${name} (${url}) 请求失败</h3>
                    <p>错误: ${error.message}</p>
                `;
                document.getElementById(resultId).className = 'result error';
            }
        }

        function testCustomSite() {
            const url = document.getElementById('customUrl').value.trim();
            if (!url) {
                alert('请输入网址');
                return;
            }
            testSite(url, '自定义网站');
        }

        // 页面加载完成后自动测试百度
        window.addEventListener('load', () => {
            setTimeout(() => {
                testSite('baidu.com', '百度');
            }, 1000);
        });
    </script>
</body>
</html>
