'use client';

import React, { useState, useEffect } from 'react';
import { Zap, Globe, Users, TrendingUp, AlertTriangle, CheckCircle, Lightbulb, Settings } from 'lucide-react';

interface OptimizationSuggestion {
  category: 'cdn' | 'performance' | 'geographic' | 'infrastructure';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  impact: string;
  implementation: string;
}

interface WebsiteOptimizerProps {
  target: string;
  isDarkMode: boolean;
  pingResults: any[];
}

const WebsiteOptimizer: React.FC<WebsiteOptimizerProps> = ({ target, isDarkMode, pingResults }) => {
  const [suggestions, setSuggestions] = useState<OptimizationSuggestion[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // 生成优化建议
  const generateOptimizationSuggestions = () => {
    if (!target || pingResults.length === 0) return;

    setIsAnalyzing(true);
    
    const newSuggestions: OptimizationSuggestion[] = [];
    
    // 分析延迟数据
    const successfulResults = pingResults.filter(r => r.status === 'success');
    const avgLatency = successfulResults.length > 0 
      ? successfulResults.reduce((sum, r) => sum + r.ping, 0) / successfulResults.length 
      : 0;
    
    const chinaResults = successfulResults.filter(r => 
      r.location?.country === 'China' || r.location?.country === 'CN' || r.province
    );
    const globalResults = successfulResults.filter(r => 
      r.testMethod === 'Cloudflare Workers' || r.testMethod === 'Globalping'
    );
    
    const chinaAvgLatency = chinaResults.length > 0 
      ? chinaResults.reduce((sum, r) => sum + r.ping, 0) / chinaResults.length 
      : 0;

    // CDN优化建议
    if (avgLatency > 200) {
      newSuggestions.push({
        category: 'cdn',
        priority: 'high',
        title: '部署全球CDN加速',
        description: `当前平均延迟${Math.round(avgLatency)}ms，建议使用CDN服务`,
        impact: '可降低全球访问延迟60-80%',
        implementation: '推荐使用Cloudflare、AWS CloudFront或阿里云CDN'
      });
    }

    if (chinaAvgLatency > 150) {
      newSuggestions.push({
        category: 'geographic',
        priority: 'high',
        title: '优化中国大陆访问',
        description: `中国大陆平均延迟${Math.round(chinaAvgLatency)}ms，需要专门优化`,
        impact: '提升中国用户体验，减少跳出率',
        implementation: '考虑使用阿里云、腾讯云或华为云的CDN服务'
      });
    }

    // 性能优化建议
    const timeoutCount = pingResults.filter(r => r.status === 'timeout').length;
    const timeoutRate = (timeoutCount / pingResults.length) * 100;
    
    if (timeoutRate > 20) {
      newSuggestions.push({
        category: 'performance',
        priority: 'high',
        title: '提升服务器稳定性',
        description: `${Math.round(timeoutRate)}%的测试节点出现超时`,
        impact: '提高网站可用性和用户满意度',
        implementation: '检查服务器配置、网络带宽和负载均衡设置'
      });
    }

    // 地理分布建议
    const asiaResults = successfulResults.filter(r => 
      r.location?.region && ['Asia', 'Eastern Asia', '亚洲'].includes(r.location.region)
    );
    
    if (asiaResults.length > 0 && chinaResults.length < 10) {
      newSuggestions.push({
        category: 'geographic',
        priority: 'medium',
        title: '扩展亚太地区覆盖',
        description: '亚太地区节点覆盖不足，影响用户体验',
        impact: '提升亚太地区用户访问速度',
        implementation: '在新加坡、日本、韩国等地部署更多节点'
      });
    }

    // 基础设施建议
    if (globalResults.length > 0) {
      const globalAvgLatency = globalResults.reduce((sum, r) => sum + r.ping, 0) / globalResults.length;
      if (globalAvgLatency < 100) {
        newSuggestions.push({
          category: 'infrastructure',
          priority: 'low',
          title: '全球基础设施表现良好',
          description: `全球平均延迟${Math.round(globalAvgLatency)}ms，基础设施优秀`,
          impact: '继续保持当前配置',
          implementation: '定期监控性能指标，确保服务质量'
        });
      }
    }

    // 智能路由建议
    if (successfulResults.length > 15) {
      newSuggestions.push({
        category: 'infrastructure',
        priority: 'medium',
        title: '实施智能DNS解析',
        description: '多节点部署完善，可启用智能路由',
        impact: '自动将用户路由到最近的服务器',
        implementation: '配置GeoDNS或使用智能DNS服务商'
      });
    }

    // 监控建议
    newSuggestions.push({
      category: 'performance',
      priority: 'medium',
      title: '建立性能监控体系',
      description: '持续监控网站性能，及时发现问题',
      impact: '提前发现性能问题，保障用户体验',
      implementation: '使用UptimeRobot、Pingdom或自建监控系统'
    });

    setSuggestions(newSuggestions);
    setIsAnalyzing(false);
  };

  useEffect(() => {
    if (target && pingResults.length > 0) {
      generateOptimizationSuggestions();
    }
  }, [target, pingResults]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-500 bg-red-100 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-100 border-green-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return <AlertTriangle className="h-4 w-4" />;
      case 'medium': return <Lightbulb className="h-4 w-4" />;
      case 'low': return <CheckCircle className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'cdn': return <Zap className="h-5 w-5" />;
      case 'performance': return <TrendingUp className="h-5 w-5" />;
      case 'geographic': return <Globe className="h-5 w-5" />;
      case 'infrastructure': return <Settings className="h-5 w-5" />;
      default: return <Lightbulb className="h-5 w-5" />;
    }
  };

  const filteredSuggestions = selectedCategory === 'all' 
    ? suggestions 
    : suggestions.filter(s => s.category === selectedCategory);

  if (!target) {
    return (
      <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
        <div className="text-center">
          <Lightbulb className={`mx-auto h-12 w-12 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} mb-4`} />
          <h3 className={`text-lg font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
            网站优化建议
          </h3>
          <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            输入网址并开始测试以获取优化建议
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
      {/* 标题 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Lightbulb className={`h-6 w-6 ${isDarkMode ? 'text-yellow-400' : 'text-yellow-600'}`} />
          <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            优化建议
          </h3>
        </div>
        
        {isAnalyzing && (
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-500"></div>
            <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>分析中...</span>
          </div>
        )}
      </div>

      {/* 分类筛选 */}
      <div className="flex flex-wrap gap-2 mb-6">
        {[
          { key: 'all', label: '全部', icon: Users },
          { key: 'cdn', label: 'CDN', icon: Zap },
          { key: 'performance', label: '性能', icon: TrendingUp },
          { key: 'geographic', label: '地理', icon: Globe },
          { key: 'infrastructure', label: '基础设施', icon: Settings }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => setSelectedCategory(key)}
            className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedCategory === key
                ? 'bg-yellow-600 text-white'
                : isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Icon className="h-4 w-4" />
            <span>{label}</span>
          </button>
        ))}
      </div>

      {/* 建议列表 */}
      <div className="space-y-4">
        {filteredSuggestions.map((suggestion, index) => (
          <div key={index} className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
            <div className="flex items-start space-x-3">
              <div className={`p-2 rounded-lg ${isDarkMode ? 'bg-gray-600' : 'bg-white'}`}>
                {getCategoryIcon(suggestion.category)}
              </div>
              
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <h4 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {suggestion.title}
                  </h4>
                  <span className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(suggestion.priority)} ${isDarkMode ? 'bg-opacity-20' : ''}`}>
                    {getPriorityIcon(suggestion.priority)}
                    <span>{suggestion.priority === 'high' ? '高' : suggestion.priority === 'medium' ? '中' : '低'}</span>
                  </span>
                </div>
                
                <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                  {suggestion.description}
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  <div>
                    <span className={`font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>预期效果：</span>
                    <span className={`ml-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>{suggestion.impact}</span>
                  </div>
                  <div>
                    <span className={`font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>实施方案：</span>
                    <span className={`ml-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>{suggestion.implementation}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
        
        {filteredSuggestions.length === 0 && !isAnalyzing && (
          <div className={`text-center py-8 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            <Lightbulb className="mx-auto h-8 w-8 mb-2" />
            <p>暂无{selectedCategory === 'all' ? '' : '该分类的'}优化建议</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default WebsiteOptimizer;
