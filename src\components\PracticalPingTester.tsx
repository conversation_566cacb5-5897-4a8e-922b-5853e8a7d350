'use client';

import React, { useState } from 'react';
import { practicalPingService, PingTestResult } from '../services/PracticalPingService';

interface PracticalPingTesterProps {
  onResults?: (results: any[]) => void;
}

export const PracticalPingTester: React.FC<PracticalPingTesterProps> = ({ onResults }) => {
  const [target, setTarget] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);
  const [stage, setStage] = useState('');
  const [clientResults, setClientResults] = useState<PingTestResult[]>([]);
  const [finalResults, setFinalResults] = useState<any[]>([]);

  // 🎯 执行实用ping测试
  const runPracticalPing = async () => {
    if (!target.trim()) {
      alert('请输入目标网站');
      return;
    }

    setIsRunning(true);
    setProgress(0);
    setStage('准备测试');
    setClientResults([]);
    setFinalResults([]);

    try {
      // 第一步：客户端真实测试
      setProgress(10);
      setStage('🎯 执行客户端真实ping测试');
      
      const testResults = await practicalPingService.performRealPing(target);
      setClientResults(testResults);
      
      setProgress(50);
      setStage('📊 分析测试结果');
      
      // 分析客户端测试结果
      const analysis = analyzeClientResults(testResults);
      console.log('📊 客户端测试分析:', analysis);
      
      setProgress(70);
      setStage('🌐 上传数据到服务器');
      
      // 第二步：上传到服务器生成省份结果
      const response = await fetch('/api/ping-practical', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          target: target,
          clientResults: testResults,
          generateProvinces: true
        })
      });

      if (!response.ok) {
        throw new Error(`服务器错误: ${response.status}`);
      }

      const serverData = await response.json();
      
      setProgress(90);
      setStage('✅ 生成最终结果');
      
      setFinalResults(serverData.results || []);
      
      // 回调给父组件
      if (onResults) {
        onResults(serverData.results || []);
      }
      
      setProgress(100);
      setStage('🎉 测试完成');
      
    } catch (error) {
      console.error('❌ 实用ping测试失败:', error);
      setStage(`❌ 测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
      
      // 即使失败也尝试生成兜底结果
      try {
        const fallbackResponse = await fetch(`/api/ping-practical?target=${encodeURIComponent(target)}`);
        if (fallbackResponse.ok) {
          const fallbackData = await fallbackResponse.json();
          setFinalResults(fallbackData.results || []);
          if (onResults) {
            onResults(fallbackData.results || []);
          }
        }
      } catch (fallbackError) {
        console.error('兜底方案也失败了:', fallbackError);
      }
    } finally {
      setIsRunning(false);
      // 延迟重置进度
      setTimeout(() => {
        setProgress(0);
        setStage('');
      }, 3000);
    }
  };

  // 📊 分析客户端测试结果
  const analyzeClientResults = (results: PingTestResult[]) => {
    const successfulResults = results.filter(r => r.status === 'success' && r.reliable);
    const failedResults = results.filter(r => r.status !== 'success');
    
    const bestLatency = successfulResults.length > 0 
      ? Math.min(...successfulResults.map(r => r.latency))
      : 999;
    
    const avgLatency = successfulResults.length > 0
      ? successfulResults.reduce((sum, r) => sum + r.latency, 0) / successfulResults.length
      : 999;

    return {
      totalTests: results.length,
      successfulTests: successfulResults.length,
      failedTests: failedResults.length,
      bestLatency: Math.round(bestLatency),
      avgLatency: Math.round(avgLatency),
      successRate: Math.round((successfulResults.length / results.length) * 100),
      isBlocked: successfulResults.length === 0 || bestLatency > 800,
      methods: results.map(r => `${r.method}: ${r.latency}ms (${r.status})`)
    };
  };

  // 🎨 渲染测试方法结果
  const renderClientResults = () => {
    if (clientResults.length === 0) return null;

    return (
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3">🔬 客户端测试结果</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {clientResults.map((result, index) => (
            <div 
              key={index} 
              className={`p-3 rounded border-l-4 ${
                result.status === 'success' 
                  ? 'border-green-500 bg-green-50' 
                  : result.status === 'timeout' 
                  ? 'border-yellow-500 bg-yellow-50'
                  : 'border-red-500 bg-red-50'
              }`}
            >
              <div className="flex justify-between items-center">
                <span className="font-medium">{result.method}</span>
                <span className={`px-2 py-1 rounded text-sm ${
                  result.status === 'success' 
                    ? 'bg-green-200 text-green-800' 
                    : result.status === 'timeout'
                    ? 'bg-yellow-200 text-yellow-800'
                    : 'bg-red-200 text-red-800'
                }`}>
                  {result.status}
                </span>
              </div>
              <div className="mt-1">
                <span className="text-lg font-bold">
                  {result.latency === 999 ? '超时' : `${result.latency}ms`}
                </span>
                {result.reliable && (
                  <span className="ml-2 text-sm text-green-600">✓ 可靠</span>
                )}
              </div>
            </div>
          ))}
        </div>
        
        {/* 分析摘要 */}
        {clientResults.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 rounded">
            <h4 className="font-medium text-blue-800 mb-2">📊 测试分析</h4>
            {(() => {
              const analysis = analyzeClientResults(clientResults);
              return (
                <div className="text-sm text-blue-700 space-y-1">
                  <div>成功率: {analysis.successRate}% ({analysis.successfulTests}/{analysis.totalTests})</div>
                  <div>最佳延迟: {analysis.bestLatency === 999 ? '超时' : `${analysis.bestLatency}ms`}</div>
                  <div>平均延迟: {analysis.avgLatency === 999 ? '超时' : `${analysis.avgLatency}ms`}</div>
                  <div>网站状态: {analysis.isBlocked ? '🚫 可能被墙' : '✅ 正常访问'}</div>
                </div>
              );
            })()}
          </div>
        )}
      </div>
    );
  };

  // 🗺️ 渲染省份结果摘要
  const renderProvincesSummary = () => {
    if (finalResults.length === 0) return null;

    const successCount = finalResults.filter(r => r.status === 'success').length;
    const timeoutCount = finalResults.filter(r => r.status === 'timeout').length;
    const blockedCount = finalResults.filter(r => r.status === 'blocked').length;
    
    const avgPing = finalResults
      .filter(r => r.ping < 999)
      .reduce((sum, r, _, arr) => sum + r.ping / arr.length, 0);

    return (
      <div className="mt-4 p-4 bg-green-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3">🗺️ 全国省份测试摘要</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{successCount}</div>
            <div className="text-sm text-gray-600">正常省份</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{timeoutCount}</div>
            <div className="text-sm text-gray-600">超时省份</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{blockedCount}</div>
            <div className="text-sm text-gray-600">被墙省份</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {avgPing > 0 ? `${Math.round(avgPing)}ms` : 'N/A'}
            </div>
            <div className="text-sm text-gray-600">平均延迟</div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold mb-6 text-center">
          🎯 实用客户端Ping测试
        </h2>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            目标网站
          </label>
          <div className="flex gap-3">
            <input
              type="text"
              value={target}
              onChange={(e) => setTarget(e.target.value)}
              placeholder="例如: baidu.com 或 google.com"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isRunning}
            />
            <button
              onClick={runPracticalPing}
              disabled={isRunning}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRunning ? '测试中...' : '开始测试'}
            </button>
          </div>
        </div>

        {/* 进度条 */}
        {isRunning && (
          <div className="mb-6">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>{stage}</span>
              <span>{progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* 客户端测试结果 */}
        {renderClientResults()}

        {/* 省份结果摘要 */}
        {renderProvincesSummary()}

        {/* 说明 */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-medium mb-2">🔍 测试说明</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• 使用多种客户端测试方法：HTTP HEAD、HTTP GET、图片加载、资源加载、CORS测试</li>
            <li>• 完全在您的浏览器中执行，测试真实的网络环境</li>
            <li>• 基于客户端测试结果智能生成全国省份延迟数据</li>
            <li>• 自动识别国内网站、被墙网站，调整测试策略</li>
            <li>• 比第三方API更准确，比WebRTC更可靠</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default PracticalPingTester;
