import type { Metadata } from "next";
import "./globals.css";
import ErrorBoundary from "../components/ErrorBoundary";

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
};

export const metadata: Metadata = {
  title: "Ping 工具 - 网络连通性测试平台",
  description: "现代化的网络延迟测试工具，提供中国地图可视化的网络连通性监控，支持多云架构和实时监控",
  keywords: ["ping", "网络测试", "延迟测试", "中国地图", "网络监控", "CDN分析"],
  authors: [{ name: "wob" }],
  creator: "wob",
  publisher: "Ping Network Monitor",
  robots: "index, follow",
  openGraph: {
    title: "Ping 工具 - 网络连通性测试平台",
    description: "现代化的网络延迟测试工具，提供中国地图可视化的网络连通性监控",
    type: "website",
    locale: "zh_CN",
  },
  twitter: {
    card: "summary_large_image",
    title: "Ping 工具 - 网络连通性测试平台",
    description: "现代化的网络延迟测试工具，提供中国地图可视化的网络连通性监控",
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },

};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <head>
        {/* DNS预解析 */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />


      </head>
      <body className="antialiased">
        <ErrorBoundary>
          {children}
        </ErrorBoundary>
      </body>
    </html>
  );
}
