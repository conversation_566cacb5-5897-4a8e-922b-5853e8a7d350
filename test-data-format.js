// 测试数据格式修复
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;

    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {}
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(options.body);
    }

    req.end();
  });
}

async function testDataFormat() {
  console.log('🧪 测试数据格式修复...');
  
  try {
    // 测试ITDOG API
    console.log('\n📡 测试ITDOG API...');
    const itdogResponse = await fetch('http://localhost:3002/api/itdog-proxy', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target: 'baidu.com' })
    });
    
    const itdogData = await itdogResponse.json();
    console.log('ITDOG结果数量:', itdogData.results?.length || 0);
    
    if (itdogData.results && itdogData.results.length > 0) {
      const sample = itdogData.results[0];
      console.log('ITDOG样本数据:');
      console.log('- node:', sample.node);
      console.log('- province:', sample.province);
      console.log('- location.province:', sample.location?.province);
      console.log('- ping:', sample.ping);
      console.log('- status:', sample.status);
    }
    
    // 测试增强ping API
    console.log('\n🚀 测试增强ping API...');
    const enhancedResponse = await fetch('http://localhost:3002/api/enhanced-ping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target: 'baidu.com', maxNodes: 20 })
    });
    
    const enhancedData = await enhancedResponse.json();
    console.log('增强API结果数量:', enhancedData.results?.length || 0);
    
    if (enhancedData.results && enhancedData.results.length > 0) {
      console.log('\n📊 省份分布统计:');
      const provinceCount = {};
      enhancedData.results.forEach(result => {
        const province = result.province || result.location?.province || '未知';
        provinceCount[province] = (provinceCount[province] || 0) + 1;
      });
      
      Object.entries(provinceCount)
        .sort((a, b) => b[1] - a[1])
        .forEach(([province, count]) => {
          console.log(`- ${province}: ${count}个节点`);
        });
      
      console.log('\n🔍 前5个结果详情:');
      enhancedData.results.slice(0, 5).forEach((result, index) => {
        console.log(`${index + 1}. ${result.node} - ${result.province || result.location?.province || '未知'} - ${result.ping}ms (${result.apiSource})`);
      });
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testDataFormat();
