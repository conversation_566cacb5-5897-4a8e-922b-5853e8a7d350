import { NextApiRequest, NextApiResponse } from 'next'

// 🌐 真实HTTP延迟测试函数
async function performRealHttpLatencyTest(domain: string): Promise<number> {
  try {
    const testUrls = [
      `https://${domain}`,
      `http://${domain}`,
      `https://www.${domain}`,
      `http://www.${domain}`
    ];

    console.log(`🔍 开始真实延迟测试: ${domain}`);
    
    for (const url of testUrls) {
      try {
        const startTime = Date.now();
        
        // 使用fetch进行HTTP请求测试
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000); // 8秒超时
        
        const response = await fetch(url, {
          method: 'HEAD', // 只获取头部，减少数据传输
          signal: controller.signal,
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; PingBot/1.0)',
            'Accept': '*/*',
            'Cache-Control': 'no-cache'
          }
        });
        
        clearTimeout(timeoutId);
        const endTime = Date.now();
        const latency = endTime - startTime;
        
        // 如果响应成功或者是可接受的错误状态
        if (response.status < 500) {
          console.log(`✅ ${url}: ${latency}ms (状态: ${response.status})`);
          return latency;
        }
        
      } catch (error) {
        console.log(`❌ ${url}: 测试失败 - ${error.message}`);
        continue;
      }
    }
    
    console.log(`🚫 ${domain}: 所有URL测试失败`);
    return -1; // 测试失败
    
  } catch (error) {
    console.log(`🚫 ${domain} 延迟测试异常: ${error}`);
    return -1;
  }
}

const handler = async function(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { target } = req.body

    if (!target) {
      return res.status(400).json({ error: "请提供有效的目标URL" })
    }

    console.log(`🎯 真实延迟测试请求: ${target}`);

    // 提取域名
    let domain = '';
    try {
      domain = new URL(target).hostname.toLowerCase();
    } catch {
      domain = target.replace(/^https?:\/\//, '').replace(/\/.*$/, '').toLowerCase();
    }

    // 进行真实延迟测试
    const realLatency = await performRealHttpLatencyTest(domain);
    
    if (realLatency > 0) {
      return res.status(200).json({
        success: true,
        target,
        domain,
        realLatency,
        message: `真实延迟测试成功: ${realLatency}ms`,
        timestamp: new Date().toISOString()
      });
    } else {
      return res.status(200).json({
        success: false,
        target,
        domain,
        message: '真实延迟测试失败，网站可能无法访问',
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('真实延迟测试错误:', error);
    return res.status(500).json({ 
      error: '服务器内部错误',
      message: error.message 
    });
  }
}

export default function(req: NextApiRequest, res: NextApiResponse) {
  // 设置CORS头部
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  return handler(req, res);
};
