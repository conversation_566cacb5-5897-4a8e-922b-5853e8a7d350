// 测试Google.com的真实延迟情况
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 15000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testGooglePing() {
  console.log('🔍 测试Google.com真实延迟...');
  
  try {
    // 1. 测试我们的增强API对Google的结果
    console.log('\n📡 测试增强API - Google.com...');
    const start = Date.now();
    
    const response = await fetch('http://localhost:3001/api/enhanced-ping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target: 'google.com', maxNodes: 50 }),
      timeout: 30000
    });
    
    const end = Date.now();
    const data = await response.json();
    
    console.log(`✅ API响应时间: ${end - start}ms`);
    console.log(`📊 总节点数: ${data.results?.length || 0}`);
    
    if (data.results && data.results.length > 0) {
      // 按API来源分组
      const apiGroups = {};
      data.results.forEach(result => {
        const source = result.apiSource || '未知';
        if (!apiGroups[source]) {
          apiGroups[source] = [];
        }
        apiGroups[source].push(result);
      });
      
      console.log('\n📊 Google.com - 按API来源分析:');
      Object.entries(apiGroups).forEach(([source, results]) => {
        const successResults = results.filter(r => r.status === 'success');
        if (successResults.length > 0) {
          const pings = successResults.map(r => r.ping);
          const avgPing = Math.round(pings.reduce((a, b) => a + b, 0) / pings.length);
          const minPing = Math.min(...pings);
          const maxPing = Math.max(...pings);
          
          console.log(`\n${source} (${results.length}个节点):`);
          console.log(`  ⚡ 延迟范围: ${minPing}ms - ${maxPing}ms (平均${avgPing}ms)`);
          console.log(`  📈 成功率: ${successResults.length}/${results.length}`);
          
          // 显示前5个结果
          console.log('  📋 详细结果:');
          results.slice(0, 5).forEach((result, index) => {
            const status = result.status === 'success' ? '✅' : '❌';
            console.log(`    ${index + 1}. ${status} ${result.node} - ${result.province} - ${result.ping}ms`);
          });
        }
      });
      
      // 特别分析Globalping数据
      const globalpingResults = data.results.filter(r => r.apiSource === 'Globalping');
      if (globalpingResults.length > 0) {
        console.log('\n🎯 Globalping对Google.com的测试结果:');
        globalpingResults.forEach((result, index) => {
          const status = result.status === 'success' ? '✅' : '❌';
          console.log(`${index + 1}. ${status} ${result.node} - ${result.province} - ${result.ping}ms`);
        });
        
        const successGP = globalpingResults.filter(r => r.status === 'success');
        if (successGP.length > 0) {
          const avgGP = Math.round(successGP.reduce((sum, r) => sum + r.ping, 0) / successGP.length);
          console.log(`📊 Globalping平均延迟: ${avgGP}ms`);
          
          // 检查是否有异常低的延迟
          const lowLatency = successGP.filter(r => r.ping < 50);
          if (lowLatency.length > 0) {
            console.log(`⚠️ 发现${lowLatency.length}个异常低延迟节点 (<50ms):`);
            lowLatency.forEach(result => {
              console.log(`  - ${result.node}: ${result.ping}ms`);
            });
          }
        }
      }
      
      // 分析中国节点的延迟
      const chinaResults = data.results.filter(r => 
        r.province && (
          r.province.includes('中国') || 
          r.province.includes('北京') || 
          r.province.includes('上海') || 
          r.province.includes('广东') ||
          r.province.includes('浙江') ||
          r.province.includes('江苏')
        )
      );
      
      if (chinaResults.length > 0) {
        console.log('\n🇨🇳 中国节点对Google的延迟:');
        chinaResults.forEach(result => {
          const status = result.status === 'success' ? '✅' : '❌';
          console.log(`  ${status} ${result.node} - ${result.province} - ${result.ping}ms`);
        });
        
        const successChina = chinaResults.filter(r => r.status === 'success');
        if (successChina.length > 0) {
          const avgChina = Math.round(successChina.reduce((sum, r) => sum + r.ping, 0) / successChina.length);
          console.log(`📊 中国节点平均延迟: ${avgChina}ms`);
          
          if (avgChina < 100) {
            console.log('🤔 中国访问Google延迟异常低，可能原因:');
            console.log('  1. 测试的是google.com的CDN边缘节点');
            console.log('  2. 网络路由优化');
            console.log('  3. 测试方法可能有问题');
          }
        }
      }
    }
    
    // 2. 对比测试其他被墙网站
    console.log('\n🔄 对比测试 facebook.com...');
    try {
      const fbResponse = await fetch('http://localhost:3001/api/enhanced-ping', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ target: 'facebook.com', maxNodes: 20 }),
        timeout: 30000
      });
      
      const fbData = await fbResponse.json();
      if (fbData.results && fbData.results.length > 0) {
        const fbSuccess = fbData.results.filter(r => r.status === 'success');
        if (fbSuccess.length > 0) {
          const fbAvg = Math.round(fbSuccess.reduce((sum, r) => sum + r.ping, 0) / fbSuccess.length);
          console.log(`📊 Facebook平均延迟: ${fbAvg}ms`);
          
          // 显示前3个结果
          console.log('📋 Facebook前3个结果:');
          fbData.results.slice(0, 3).forEach((result, index) => {
            const status = result.status === 'success' ? '✅' : '❌';
            console.log(`  ${index + 1}. ${status} ${result.node} - ${result.province} - ${result.ping}ms`);
          });
        }
      }
    } catch (error) {
      console.log(`❌ Facebook测试失败: ${error.message}`);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
  
  console.log('\n✅ Google延迟测试完成！');
}

testGooglePing();
