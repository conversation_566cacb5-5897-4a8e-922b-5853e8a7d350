// 测试网站配置文件

export interface TestSite {
  id: string;
  name: string;
  url: string;
  category: 'domestic' | 'foreign' | 'regional';
  subcategory: string;
  description: string;
  icon: string;
  expectedLatency: {
    domestic: number; // 国内预期延迟(ms)
    foreign: number;  // 国外预期延迟(ms)
  };
  popularity: number; // 1-5星级，表示网站流行度
  tags: string[];
  // 新增字段：城市等级和延迟倍数
  cityTier?: 1 | 2 | 3 | 4; // 城市等级：1线、2线、3线、4线
  latencyMultiplier?: {
    tier1: number; // 一线城市延迟倍数
    tier2: number; // 二线城市延迟倍数
    tier3: number; // 三线城市延迟倍数
    tier4: number; // 四线城市延迟倍数
  };
  region?: string; // 地区标识
}

// 国内网站列表
export const DOMESTIC_SITES: TestSite[] = [
  // 搜索引擎
  {
    id: 'baidu',
    name: '百度',
    url: 'https://www.baidu.com',
    category: 'domestic',
    subcategory: '搜索引擎',
    description: '中国最大的搜索引擎',
    icon: '🔍',
    expectedLatency: { domestic: 30, foreign: 200 },
    popularity: 5,
    tags: ['搜索', '门户', '常用']
  },
  {
    id: 'sogou',
    name: '搜狗',
    url: 'https://www.sogou.com',
    category: 'domestic',
    subcategory: '搜索引擎',
    description: '搜狗搜索引擎',
    icon: '🔍',
    expectedLatency: { domestic: 35, foreign: 220 },
    popularity: 3,
    tags: ['搜索']
  },

  // 电商平台
  {
    id: 'taobao',
    name: '淘宝',
    url: 'https://www.taobao.com',
    category: 'domestic',
    subcategory: '电商',
    description: '中国最大的C2C电商平台',
    icon: '🛒',
    expectedLatency: { domestic: 40, foreign: 250 },
    popularity: 5,
    tags: ['电商', '购物', '常用']
  },
  {
    id: 'tmall',
    name: '天猫',
    url: 'https://www.tmall.com',
    category: 'domestic',
    subcategory: '电商',
    description: '天猫商城，B2C电商平台',
    icon: '🛍️',
    expectedLatency: { domestic: 45, foreign: 260 },
    popularity: 5,
    tags: ['电商', '购物', '品牌']
  },
  {
    id: 'jd',
    name: '京东',
    url: 'https://www.jd.com',
    category: 'domestic',
    subcategory: '电商',
    description: '京东商城，自营电商平台',
    icon: '📦',
    expectedLatency: { domestic: 35, foreign: 240 },
    popularity: 5,
    tags: ['电商', '购物', '自营']
  },
  {
    id: 'pdd',
    name: '拼多多',
    url: 'https://www.pinduoduo.com',
    category: 'domestic',
    subcategory: '电商',
    description: '拼团购物平台',
    icon: '🍎',
    expectedLatency: { domestic: 50, foreign: 280 },
    popularity: 4,
    tags: ['电商', '拼团', '优惠']
  },
  {
    id: 'suning',
    name: '苏宁易购',
    url: 'https://www.suning.com',
    category: 'domestic',
    subcategory: '电商',
    description: '苏宁易购商城',
    icon: '🏪',
    expectedLatency: { domestic: 45, foreign: 270 },
    popularity: 4,
    tags: ['电商', '购物', '家电']
  },
  {
    id: 'vip',
    name: '唯品会',
    url: 'https://www.vip.com',
    category: 'domestic',
    subcategory: '电商',
    description: '品牌特卖商城',
    icon: '💎',
    expectedLatency: { domestic: 40, foreign: 250 },
    popularity: 3,
    tags: ['电商', '特卖', '品牌']
  },
  {
    id: 'dangdang',
    name: '当当网',
    url: 'https://www.dangdang.com',
    category: 'domestic',
    subcategory: '电商',
    description: '图书音像购物网站',
    icon: '📚',
    expectedLatency: { domestic: 45, foreign: 260 },
    popularity: 3,
    tags: ['电商', '图书', '文化']
  },

  // 社交媒体
  {
    id: 'weibo',
    name: '微博',
    url: 'https://weibo.com',
    category: 'domestic',
    subcategory: '社交媒体',
    description: '中国最大的微博社交平台',
    icon: '📱',
    expectedLatency: { domestic: 45, foreign: 270 },
    popularity: 4,
    tags: ['社交', '微博', '新闻']
  },
  {
    id: 'zhihu',
    name: '知乎',
    url: 'https://www.zhihu.com',
    category: 'domestic',
    subcategory: '社交媒体',
    description: '知识问答社区',
    icon: '💡',
    expectedLatency: { domestic: 40, foreign: 250 },
    popularity: 4,
    tags: ['问答', '知识', '社区']
  },
  {
    id: 'douban',
    name: '豆瓣',
    url: 'https://www.douban.com',
    category: 'domestic',
    subcategory: '社交媒体',
    description: '文艺青年聚集地',
    icon: '🎭',
    expectedLatency: { domestic: 50, foreign: 280 },
    popularity: 3,
    tags: ['社交', '文艺', '评分']
  },
  {
    id: 'tieba',
    name: '百度贴吧',
    url: 'https://tieba.baidu.com',
    category: 'domestic',
    subcategory: '社交媒体',
    description: '百度贴吧社区',
    icon: '💬',
    expectedLatency: { domestic: 35, foreign: 230 },
    popularity: 4,
    tags: ['社交', '论坛', '兴趣']
  },

  // 新闻门户
  {
    id: 'sina',
    name: '新浪',
    url: 'https://www.sina.com.cn',
    category: 'domestic',
    subcategory: '新闻门户',
    description: '新浪门户网站',
    icon: '📰',
    expectedLatency: { domestic: 35, foreign: 220 },
    popularity: 4,
    tags: ['新闻', '门户', '资讯']
  },
  {
    id: 'sohu',
    name: '搜狐',
    url: 'https://www.sohu.com',
    category: 'domestic',
    subcategory: '新闻门户',
    description: '搜狐门户网站',
    icon: '📺',
    expectedLatency: { domestic: 40, foreign: 240 },
    popularity: 3,
    tags: ['新闻', '门户', '娱乐']
  },
  {
    id: 'netease',
    name: '网易',
    url: 'https://www.163.com',
    category: 'domestic',
    subcategory: '新闻门户',
    description: '网易门户网站',
    icon: '📧',
    expectedLatency: { domestic: 35, foreign: 225 },
    popularity: 4,
    tags: ['新闻', '门户', '邮箱']
  },
  {
    id: 'ifeng',
    name: '凤凰网',
    url: 'https://www.ifeng.com',
    category: 'domestic',
    subcategory: '新闻门户',
    description: '凤凰网新闻门户',
    icon: '🔥',
    expectedLatency: { domestic: 45, foreign: 260 },
    popularity: 3,
    tags: ['新闻', '时事', '评论']
  },

  // 科技公司
  {
    id: 'tencent',
    name: '腾讯',
    url: 'https://www.qq.com',
    category: 'domestic',
    subcategory: '科技公司',
    description: '腾讯公司官网',
    icon: '🐧',
    expectedLatency: { domestic: 30, foreign: 200 },
    popularity: 5,
    tags: ['科技', '互联网', '游戏']
  },
  {
    id: 'alibaba',
    name: '阿里巴巴',
    url: 'https://www.alibaba.com',
    category: 'domestic',
    subcategory: '科技公司',
    description: '阿里巴巴集团',
    icon: '🏢',
    expectedLatency: { domestic: 35, foreign: 220 },
    popularity: 5,
    tags: ['科技', '互联网', '电商']
  },
  {
    id: 'baidu-tech',
    name: '百度公司',
    url: 'https://www.baidu.com',
    category: 'domestic',
    subcategory: '科技公司',
    description: '百度科技公司',
    icon: '🔍',
    expectedLatency: { domestic: 30, foreign: 200 },
    popularity: 5,
    tags: ['科技', 'AI', '搜索']
  },
  {
    id: 'bytedance',
    name: '字节跳动',
    url: 'https://www.bytedance.com',
    category: 'domestic',
    subcategory: '科技公司',
    description: '字节跳动科技公司',
    icon: '🚀',
    expectedLatency: { domestic: 40, foreign: 240 },
    popularity: 5,
    tags: ['科技', '短视频', 'AI']
  },
  {
    id: 'xiaomi',
    name: '小米',
    url: 'https://www.mi.com',
    category: 'domestic',
    subcategory: '科技公司',
    description: '小米科技公司',
    icon: '📱',
    expectedLatency: { domestic: 35, foreign: 220 },
    popularity: 4,
    tags: ['科技', '手机', '智能硬件']
  },
  {
    id: 'huawei',
    name: '华为',
    url: 'https://www.huawei.com',
    category: 'domestic',
    subcategory: '科技公司',
    description: '华为技术有限公司',
    icon: '📡',
    expectedLatency: { domestic: 40, foreign: 250 },
    popularity: 5,
    tags: ['科技', '通信', '5G']
  },

  // 视频娱乐
  {
    id: 'bilibili',
    name: 'B站',
    url: 'https://www.bilibili.com',
    category: 'domestic',
    subcategory: '视频娱乐',
    description: '哔哩哔哩弹幕视频网',
    icon: '📺',
    expectedLatency: { domestic: 50, foreign: 300 },
    popularity: 4,
    tags: ['视频', '弹幕', '二次元']
  },
  {
    id: 'iqiyi',
    name: '爱奇艺',
    url: 'https://www.iqiyi.com',
    category: 'domestic',
    subcategory: '视频娱乐',
    description: '爱奇艺视频平台',
    icon: '🎬',
    expectedLatency: { domestic: 55, foreign: 320 },
    popularity: 4,
    tags: ['视频', '影视', '娱乐']
  },
  {
    id: 'youku',
    name: '优酷',
    url: 'https://www.youku.com',
    category: 'domestic',
    subcategory: '视频娱乐',
    description: '优酷视频平台',
    icon: '🎥',
    expectedLatency: { domestic: 50, foreign: 300 },
    popularity: 4,
    tags: ['视频', '影视', '综艺']
  },
  {
    id: 'tencent-video',
    name: '腾讯视频',
    url: 'https://v.qq.com',
    category: 'domestic',
    subcategory: '视频娱乐',
    description: '腾讯视频平台',
    icon: '🎭',
    expectedLatency: { domestic: 45, foreign: 280 },
    popularity: 4,
    tags: ['视频', '影视', '动漫']
  },
  {
    id: 'douyin',
    name: '抖音',
    url: 'https://www.douyin.com',
    category: 'domestic',
    subcategory: '视频娱乐',
    description: '抖音短视频平台',
    icon: '🎵',
    expectedLatency: { domestic: 40, foreign: 260 },
    popularity: 5,
    tags: ['短视频', '音乐', '娱乐']
  },
  {
    id: 'kuaishou',
    name: '快手',
    url: 'https://www.kuaishou.com',
    category: 'domestic',
    subcategory: '视频娱乐',
    description: '快手短视频平台',
    icon: '⚡',
    expectedLatency: { domestic: 45, foreign: 270 },
    popularity: 4,
    tags: ['短视频', '直播', '社交']
  },

  // 金融服务
  {
    id: 'alipay',
    name: '支付宝',
    url: 'https://www.alipay.com',
    category: 'domestic',
    subcategory: '金融服务',
    description: '支付宝移动支付平台',
    icon: '💰',
    expectedLatency: { domestic: 30, foreign: 200 },
    popularity: 5,
    tags: ['支付', '金融', '生活服务']
  },
  {
    id: 'wechatpay',
    name: '微信支付',
    url: 'https://pay.weixin.qq.com',
    category: 'domestic',
    subcategory: '金融服务',
    description: '微信支付平台',
    icon: '💳',
    expectedLatency: { domestic: 35, foreign: 220 },
    popularity: 5,
    tags: ['支付', '金融', '社交']
  },

  // 在线教育
  {
    id: 'xuetangx',
    name: '学堂在线',
    url: 'https://www.xuetangx.com',
    category: 'domestic',
    subcategory: '在线教育',
    description: '清华大学在线教育平台',
    icon: '🎓',
    expectedLatency: { domestic: 40, foreign: 250 },
    popularity: 3,
    tags: ['教育', '在线课程', '学习']
  },
  {
    id: 'icourse163',
    name: '中国大学MOOC',
    url: 'https://www.icourse163.org',
    category: 'domestic',
    subcategory: '在线教育',
    description: '网易云课堂MOOC平台',
    icon: '📖',
    expectedLatency: { domestic: 45, foreign: 260 },
    popularity: 3,
    tags: ['教育', 'MOOC', '大学课程']
  }
];

// 国外网站列表
export const FOREIGN_SITES: TestSite[] = [
  // 搜索引擎
  {
    id: 'google',
    name: 'Google',
    url: 'https://www.google.com',
    category: 'foreign',
    subcategory: '搜索引擎',
    description: '全球最大的搜索引擎',
    icon: '🔍',
    expectedLatency: { domestic: 300, foreign: 50 },
    popularity: 5,
    tags: ['搜索', '全球', '常用']
  },
  {
    id: 'bing',
    name: 'Bing',
    url: 'https://www.bing.com',
    category: 'foreign',
    subcategory: '搜索引擎',
    description: '微软搜索引擎',
    icon: '🔍',
    expectedLatency: { domestic: 250, foreign: 60 },
    popularity: 3,
    tags: ['搜索', '微软']
  },
  {
    id: 'duckduckgo',
    name: 'DuckDuckGo',
    url: 'https://duckduckgo.com',
    category: 'foreign',
    subcategory: '搜索引擎',
    description: '注重隐私的搜索引擎',
    icon: '🦆',
    expectedLatency: { domestic: 280, foreign: 70 },
    popularity: 3,
    tags: ['搜索', '隐私', '被墙']
  },
  {
    id: 'yahoo',
    name: 'Yahoo',
    url: 'https://www.yahoo.com',
    category: 'foreign',
    subcategory: '搜索引擎',
    description: 'Yahoo搜索门户',
    icon: '🌐',
    expectedLatency: { domestic: 270, foreign: 65 },
    popularity: 3,
    tags: ['搜索', '门户', '新闻']
  },

  // 电商平台
  {
    id: 'amazon',
    name: 'Amazon',
    url: 'https://www.amazon.com',
    category: 'foreign',
    subcategory: '电商',
    description: '全球最大的电商平台',
    icon: '📦',
    expectedLatency: { domestic: 350, foreign: 80 },
    popularity: 5,
    tags: ['电商', '购物', '全球']
  },
  {
    id: 'ebay',
    name: 'eBay',
    url: 'https://www.ebay.com',
    category: 'foreign',
    subcategory: '电商',
    description: '全球在线拍卖及购物网站',
    icon: '🛒',
    expectedLatency: { domestic: 320, foreign: 90 },
    popularity: 4,
    tags: ['电商', '拍卖', '二手']
  },
  {
    id: 'shopify',
    name: 'Shopify',
    url: 'https://www.shopify.com',
    category: 'foreign',
    subcategory: '电商',
    description: '电商建站平台',
    icon: '🏪',
    expectedLatency: { domestic: 300, foreign: 85 },
    popularity: 4,
    tags: ['电商', '建站', 'SaaS']
  },
  {
    id: 'etsy',
    name: 'Etsy',
    url: 'https://www.etsy.com',
    category: 'foreign',
    subcategory: '电商',
    description: '手工艺品电商平台',
    icon: '🎨',
    expectedLatency: { domestic: 330, foreign: 95 },
    popularity: 3,
    tags: ['电商', '手工', '创意']
  },
  {
    id: 'walmart',
    name: 'Walmart',
    url: 'https://www.walmart.com',
    category: 'foreign',
    subcategory: '电商',
    description: '沃尔玛在线商城',
    icon: '🏬',
    expectedLatency: { domestic: 340, foreign: 100 },
    popularity: 4,
    tags: ['电商', '零售', '美国']
  },

  // 社交媒体
  {
    id: 'facebook',
    name: 'Facebook',
    url: 'https://www.facebook.com',
    category: 'foreign',
    subcategory: '社交媒体',
    description: '全球最大的社交网络',
    icon: '👥',
    expectedLatency: { domestic: 400, foreign: 70 },
    popularity: 5,
    tags: ['社交', '全球', '被墙']
  },
  {
    id: 'twitter',
    name: 'Twitter',
    url: 'https://twitter.com',
    category: 'foreign',
    subcategory: '社交媒体',
    description: '微博客社交网络服务',
    icon: '🐦',
    expectedLatency: { domestic: 450, foreign: 80 },
    popularity: 4,
    tags: ['社交', '微博客', '被墙']
  },
  {
    id: 'instagram',
    name: 'Instagram',
    url: 'https://www.instagram.com',
    category: 'foreign',
    subcategory: '社交媒体',
    description: '图片分享社交平台',
    icon: '📸',
    expectedLatency: { domestic: 420, foreign: 75 },
    popularity: 4,
    tags: ['社交', '图片', '被墙']
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    url: 'https://www.linkedin.com',
    category: 'foreign',
    subcategory: '社交媒体',
    description: '职业社交网络平台',
    icon: '💼',
    expectedLatency: { domestic: 350, foreign: 80 },
    popularity: 4,
    tags: ['社交', '职业', '求职']
  },
  {
    id: 'reddit',
    name: 'Reddit',
    url: 'https://www.reddit.com',
    category: 'foreign',
    subcategory: '社交媒体',
    description: '社交新闻聚合网站',
    icon: '🤖',
    expectedLatency: { domestic: 380, foreign: 85 },
    popularity: 4,
    tags: ['社交', '论坛', '被墙']
  },
  {
    id: 'discord',
    name: 'Discord',
    url: 'https://discord.com',
    category: 'foreign',
    subcategory: '社交媒体',
    description: '游戏社交通讯平台',
    icon: '🎮',
    expectedLatency: { domestic: 320, foreign: 70 },
    popularity: 4,
    tags: ['社交', '游戏', '语音']
  },
  {
    id: 'telegram',
    name: 'Telegram',
    url: 'https://telegram.org',
    category: 'foreign',
    subcategory: '社交媒体',
    description: '加密即时通讯软件',
    icon: '✈️',
    expectedLatency: { domestic: 400, foreign: 90 },
    popularity: 3,
    tags: ['社交', '通讯', '被墙']
  },
  {
    id: 'tiktok',
    name: 'TikTok',
    url: 'https://www.tiktok.com',
    category: 'foreign',
    subcategory: '社交媒体',
    description: '国际版抖音短视频',
    icon: '🎵',
    expectedLatency: { domestic: 380, foreign: 75 },
    popularity: 5,
    tags: ['社交', '短视频', '娱乐']
  },

  // 视频娱乐
  {
    id: 'youtube',
    name: 'YouTube',
    url: 'https://www.youtube.com',
    category: 'foreign',
    subcategory: '视频娱乐',
    description: '全球最大的视频分享平台',
    icon: '📺',
    expectedLatency: { domestic: 450, foreign: 80 },
    popularity: 5,
    tags: ['视频', '娱乐', '被墙']
  },
  {
    id: 'netflix',
    name: 'Netflix',
    url: 'https://www.netflix.com',
    category: 'foreign',
    subcategory: '视频娱乐',
    description: '全球流媒体平台',
    icon: '🎬',
    expectedLatency: { domestic: 400, foreign: 90 },
    popularity: 5,
    tags: ['视频', '流媒体', '被墙']
  },
  {
    id: 'twitch',
    name: 'Twitch',
    url: 'https://www.twitch.tv',
    category: 'foreign',
    subcategory: '视频娱乐',
    description: '游戏直播平台',
    icon: '🎮',
    expectedLatency: { domestic: 420, foreign: 95 },
    popularity: 4,
    tags: ['视频', '直播', '游戏']
  },

  // 科技公司
  {
    id: 'apple',
    name: 'Apple',
    url: 'https://www.apple.com',
    category: 'foreign',
    subcategory: '科技公司',
    description: '苹果公司官网',
    icon: '🍎',
    expectedLatency: { domestic: 280, foreign: 60 },
    popularity: 5,
    tags: ['科技', '硬件', '品牌']
  },
  {
    id: 'microsoft',
    name: 'Microsoft',
    url: 'https://www.microsoft.com',
    category: 'foreign',
    subcategory: '科技公司',
    description: '微软公司官网',
    icon: '🪟',
    expectedLatency: { domestic: 250, foreign: 70 },
    popularity: 5,
    tags: ['科技', '软件', '云服务']
  },
  {
    id: 'google-tech',
    name: 'Google',
    url: 'https://about.google',
    category: 'foreign',
    subcategory: '科技公司',
    description: '谷歌公司官网',
    icon: '🔍',
    expectedLatency: { domestic: 300, foreign: 50 },
    popularity: 5,
    tags: ['科技', 'AI', '被墙']
  },
  {
    id: 'meta',
    name: 'Meta',
    url: 'https://about.meta.com',
    category: 'foreign',
    subcategory: '科技公司',
    description: 'Meta公司官网（原Facebook）',
    icon: '🌐',
    expectedLatency: { domestic: 400, foreign: 75 },
    popularity: 5,
    tags: ['科技', '元宇宙', '被墙']
  },
  {
    id: 'tesla',
    name: 'Tesla',
    url: 'https://www.tesla.com',
    category: 'foreign',
    subcategory: '科技公司',
    description: '特斯拉电动汽车公司',
    icon: '🚗',
    expectedLatency: { domestic: 320, foreign: 80 },
    popularity: 4,
    tags: ['科技', '电动车', '创新']
  },
  {
    id: 'nvidia',
    name: 'NVIDIA',
    url: 'https://www.nvidia.com',
    category: 'foreign',
    subcategory: '科技公司',
    description: '英伟达图形处理器公司',
    icon: '🎮',
    expectedLatency: { domestic: 290, foreign: 70 },
    popularity: 4,
    tags: ['科技', 'GPU', 'AI']
  },
  {
    id: 'openai',
    name: 'OpenAI',
    url: 'https://openai.com',
    category: 'foreign',
    subcategory: '科技公司',
    description: 'OpenAI人工智能公司',
    icon: '🤖',
    expectedLatency: { domestic: 350, foreign: 85 },
    popularity: 5,
    tags: ['科技', 'AI', '被墙']
  },

  // 开发者平台
  {
    id: 'github',
    name: 'GitHub',
    url: 'https://github.com',
    category: 'foreign',
    subcategory: '开发者平台',
    description: '全球最大的代码托管平台',
    icon: '💻',
    expectedLatency: { domestic: 300, foreign: 50 },
    popularity: 5,
    tags: ['开发', '代码', '开源']
  },
  {
    id: 'stackoverflow',
    name: 'Stack Overflow',
    url: 'https://stackoverflow.com',
    category: 'foreign',
    subcategory: '开发者平台',
    description: '程序员问答社区',
    icon: '❓',
    expectedLatency: { domestic: 280, foreign: 60 },
    popularity: 4,
    tags: ['开发', '问答', '技术']
  },
  {
    id: 'gitlab',
    name: 'GitLab',
    url: 'https://gitlab.com',
    category: 'foreign',
    subcategory: '开发者平台',
    description: 'DevOps平台和代码托管',
    icon: '🦊',
    expectedLatency: { domestic: 320, foreign: 70 },
    popularity: 4,
    tags: ['开发', 'DevOps', 'CI/CD']
  },
  {
    id: 'npm',
    name: 'NPM',
    url: 'https://www.npmjs.com',
    category: 'foreign',
    subcategory: '开发者平台',
    description: 'Node.js包管理器',
    icon: '📦',
    expectedLatency: { domestic: 300, foreign: 65 },
    popularity: 4,
    tags: ['开发', 'JavaScript', '包管理']
  },
  {
    id: 'docker',
    name: 'Docker Hub',
    url: 'https://hub.docker.com',
    category: 'foreign',
    subcategory: '开发者平台',
    description: 'Docker容器镜像仓库',
    icon: '🐳',
    expectedLatency: { domestic: 350, foreign: 80 },
    popularity: 4,
    tags: ['开发', '容器', '被墙']
  },

  // 云服务
  {
    id: 'aws',
    name: 'AWS',
    url: 'https://aws.amazon.com',
    category: 'foreign',
    subcategory: '云服务',
    description: '亚马逊云服务',
    icon: '☁️',
    expectedLatency: { domestic: 280, foreign: 60 },
    popularity: 5,
    tags: ['云服务', '企业', 'AWS']
  },
  {
    id: 'azure',
    name: 'Microsoft Azure',
    url: 'https://azure.microsoft.com',
    category: 'foreign',
    subcategory: '云服务',
    description: '微软云服务平台',
    icon: '🌩️',
    expectedLatency: { domestic: 250, foreign: 70 },
    popularity: 4,
    tags: ['云服务', '企业', '微软']
  },
  {
    id: 'gcp',
    name: 'Google Cloud',
    url: 'https://cloud.google.com',
    category: 'foreign',
    subcategory: '云服务',
    description: '谷歌云平台',
    icon: '🌤️',
    expectedLatency: { domestic: 320, foreign: 55 },
    popularity: 4,
    tags: ['云服务', '企业', '被墙']
  },

  // 新闻媒体
  {
    id: 'cnn',
    name: 'CNN',
    url: 'https://www.cnn.com',
    category: 'foreign',
    subcategory: '新闻媒体',
    description: 'CNN新闻网',
    icon: '📺',
    expectedLatency: { domestic: 350, foreign: 80 },
    popularity: 4,
    tags: ['新闻', '媒体', '美国']
  },

  // 在线教育
  {
    id: 'coursera',
    name: 'Coursera',
    url: 'https://www.coursera.org',
    category: 'foreign',
    subcategory: '在线教育',
    description: '全球在线课程平台',
    icon: '🎓',
    expectedLatency: { domestic: 300, foreign: 75 },
    popularity: 4,
    tags: ['教育', '在线课程', '大学']
  },
  {
    id: 'edx',
    name: 'edX',
    url: 'https://www.edx.org',
    category: 'foreign',
    subcategory: '在线教育',
    description: 'MIT和哈佛在线教育平台',
    icon: '📚',
    expectedLatency: { domestic: 320, foreign: 80 },
    popularity: 3,
    tags: ['教育', 'MOOC', '名校']
  },

  // 更多新闻媒体
  {
    id: 'bbc',
    name: 'BBC',
    url: 'https://www.bbc.com',
    category: 'foreign',
    subcategory: '新闻媒体',
    description: 'BBC新闻网',
    icon: '📻',
    expectedLatency: { domestic: 300, foreign: 80 },
    popularity: 4,
    tags: ['新闻', '媒体', '英国']
  },
  {
    id: 'reuters',
    name: 'Reuters',
    url: 'https://www.reuters.com',
    category: 'foreign',
    subcategory: '新闻媒体',
    description: '路透社新闻网',
    icon: '📰',
    expectedLatency: { domestic: 310, foreign: 75 },
    popularity: 4,
    tags: ['新闻', '媒体', '国际']
  }
];

// 国内各省市测试站点
export const REGIONAL_SITES: TestSite[] = [
  // 一线城市
  {
    id: 'beijing-gov',
    name: '北京市政府',
    url: 'https://www.beijing.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '北京市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 25, foreign: 180 },
    popularity: 4,
    tags: ['政府', '一线城市', '北京'],
    cityTier: 1,
    latencyMultiplier: { tier1: 1.0, tier2: 1.2, tier3: 1.5, tier4: 2.0 },
    region: '华北'
  },
  {
    id: 'shanghai-gov',
    name: '上海市政府',
    url: 'https://www.shanghai.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '上海市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 28, foreign: 185 },
    popularity: 4,
    tags: ['政府', '一线城市', '上海'],
    cityTier: 1,
    latencyMultiplier: { tier1: 1.0, tier2: 1.2, tier3: 1.5, tier4: 2.0 },
    region: '华东'
  },
  {
    id: 'guangzhou-gov',
    name: '广州市政府',
    url: 'https://www.gz.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '广州市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 30, foreign: 190 },
    popularity: 4,
    tags: ['政府', '一线城市', '广州'],
    cityTier: 1,
    latencyMultiplier: { tier1: 1.0, tier2: 1.2, tier3: 1.5, tier4: 2.0 },
    region: '华南'
  },
  {
    id: 'shenzhen-gov',
    name: '深圳市政府',
    url: 'https://www.sz.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '深圳市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 32, foreign: 195 },
    popularity: 4,
    tags: ['政府', '一线城市', '深圳'],
    cityTier: 1,
    latencyMultiplier: { tier1: 1.0, tier2: 1.2, tier3: 1.5, tier4: 2.0 },
    region: '华南'
  },

  // 二线城市
  {
    id: 'hangzhou-gov',
    name: '杭州市政府',
    url: 'https://www.hangzhou.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '杭州市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 35, foreign: 210 },
    popularity: 3,
    tags: ['政府', '二线城市', '杭州'],
    cityTier: 2,
    latencyMultiplier: { tier1: 0.8, tier2: 1.0, tier3: 1.3, tier4: 1.8 },
    region: '华东'
  },
  {
    id: 'nanjing-gov',
    name: '南京市政府',
    url: 'https://www.nanjing.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '南京市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 38, foreign: 220 },
    popularity: 3,
    tags: ['政府', '二线城市', '南京'],
    cityTier: 2,
    latencyMultiplier: { tier1: 0.8, tier2: 1.0, tier3: 1.3, tier4: 1.8 },
    region: '华东'
  },
  {
    id: 'wuhan-gov',
    name: '武汉市政府',
    url: 'https://www.wuhan.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '武汉市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 40, foreign: 230 },
    popularity: 3,
    tags: ['政府', '二线城市', '武汉'],
    cityTier: 2,
    latencyMultiplier: { tier1: 0.8, tier2: 1.0, tier3: 1.3, tier4: 1.8 },
    region: '华中'
  },
  {
    id: 'chengdu-gov',
    name: '成都市政府',
    url: 'https://www.chengdu.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '成都市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 42, foreign: 240 },
    popularity: 3,
    tags: ['政府', '二线城市', '成都'],
    cityTier: 2,
    latencyMultiplier: { tier1: 0.8, tier2: 1.0, tier3: 1.3, tier4: 1.8 },
    region: '西南'
  },
  {
    id: 'xian-gov',
    name: '西安市政府',
    url: 'https://www.xa.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '西安市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 45, foreign: 250 },
    popularity: 3,
    tags: ['政府', '二线城市', '西安'],
    cityTier: 2,
    latencyMultiplier: { tier1: 0.8, tier2: 1.0, tier3: 1.3, tier4: 1.8 },
    region: '西北'
  },

  // 三线城市
  {
    id: 'hefei-gov',
    name: '合肥市政府',
    url: 'https://www.hefei.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '合肥市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 50, foreign: 280 },
    popularity: 2,
    tags: ['政府', '三线城市', '合肥'],
    cityTier: 3,
    latencyMultiplier: { tier1: 0.7, tier2: 0.8, tier3: 1.0, tier4: 1.5 },
    region: '华东'
  },
  {
    id: 'kunming-gov',
    name: '昆明市政府',
    url: 'https://www.km.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '昆明市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 55, foreign: 300 },
    popularity: 2,
    tags: ['政府', '三线城市', '昆明'],
    cityTier: 3,
    latencyMultiplier: { tier1: 0.7, tier2: 0.8, tier3: 1.0, tier4: 1.5 },
    region: '西南'
  },
  {
    id: 'nanning-gov',
    name: '南宁市政府',
    url: 'https://www.nanning.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '南宁市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 58, foreign: 320 },
    popularity: 2,
    tags: ['政府', '三线城市', '南宁'],
    cityTier: 3,
    latencyMultiplier: { tier1: 0.7, tier2: 0.8, tier3: 1.0, tier4: 1.5 },
    region: '华南'
  },

  // 四线城市
  {
    id: 'yinchuan-gov',
    name: '银川市政府',
    url: 'https://www.yinchuan.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '银川市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 65, foreign: 350 },
    popularity: 2,
    tags: ['政府', '四线城市', '银川'],
    cityTier: 4,
    latencyMultiplier: { tier1: 0.5, tier2: 0.6, tier3: 0.7, tier4: 1.0 },
    region: '西北'
  },
  {
    id: 'lhasa-gov',
    name: '拉萨市政府',
    url: 'https://www.lasa.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '拉萨市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 80, foreign: 400 },
    popularity: 2,
    tags: ['政府', '四线城市', '拉萨'],
    cityTier: 4,
    latencyMultiplier: { tier1: 0.5, tier2: 0.6, tier3: 0.7, tier4: 1.0 },
    region: '西南'
  },

  // 省级门户
  {
    id: 'shandong-gov',
    name: '山东省政府',
    url: 'https://www.shandong.gov.cn',
    category: 'regional',
    subcategory: '省级门户',
    description: '山东省人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 35, foreign: 200 },
    popularity: 3,
    tags: ['政府', '省级', '山东'],
    cityTier: 2,
    latencyMultiplier: { tier1: 0.8, tier2: 1.0, tier3: 1.3, tier4: 1.8 },
    region: '华东'
  },
  {
    id: 'jiangsu-gov',
    name: '江苏省政府',
    url: 'https://www.jiangsu.gov.cn',
    category: 'regional',
    subcategory: '省级门户',
    description: '江苏省人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 32, foreign: 195 },
    popularity: 3,
    tags: ['政府', '省级', '江苏'],
    cityTier: 1,
    latencyMultiplier: { tier1: 1.0, tier2: 1.2, tier3: 1.5, tier4: 2.0 },
    region: '华东'
  },
  {
    id: 'zhejiang-gov',
    name: '浙江省政府',
    url: 'https://www.zj.gov.cn',
    category: 'regional',
    subcategory: '省级门户',
    description: '浙江省人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 30, foreign: 190 },
    popularity: 3,
    tags: ['政府', '省级', '浙江'],
    cityTier: 1,
    latencyMultiplier: { tier1: 1.0, tier2: 1.2, tier3: 1.5, tier4: 2.0 },
    region: '华东'
  },

  // 直辖市
  {
    id: 'tianjin-gov',
    name: '天津市政府',
    url: 'https://www.tj.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '天津市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 28, foreign: 185 },
    popularity: 4,
    tags: ['政府', '直辖市', '天津'],
    cityTier: 1,
    latencyMultiplier: { tier1: 1.0, tier2: 1.2, tier3: 1.5, tier4: 2.0 },
    region: '华北'
  },
  {
    id: 'chongqing-gov',
    name: '重庆市政府',
    url: 'https://www.cq.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '重庆市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 40, foreign: 230 },
    popularity: 4,
    tags: ['政府', '直辖市', '重庆'],
    cityTier: 1,
    latencyMultiplier: { tier1: 1.0, tier2: 1.2, tier3: 1.5, tier4: 2.0 },
    region: '西南'
  },

  // 重要二线城市
  {
    id: 'qingdao-gov',
    name: '青岛市政府',
    url: 'https://www.qingdao.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '青岛市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 38, foreign: 220 },
    popularity: 3,
    tags: ['政府', '二线城市', '青岛'],
    cityTier: 2,
    latencyMultiplier: { tier1: 0.8, tier2: 1.0, tier3: 1.3, tier4: 1.8 },
    region: '华东'
  },
  {
    id: 'dalian-gov',
    name: '大连市政府',
    url: 'https://www.dl.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '大连市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 42, foreign: 240 },
    popularity: 3,
    tags: ['政府', '二线城市', '大连'],
    cityTier: 2,
    latencyMultiplier: { tier1: 0.8, tier2: 1.0, tier3: 1.3, tier4: 1.8 },
    region: '东北'
  },
  {
    id: 'xiamen-gov',
    name: '厦门市政府',
    url: 'https://www.xm.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '厦门市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 40, foreign: 230 },
    popularity: 3,
    tags: ['政府', '二线城市', '厦门'],
    cityTier: 2,
    latencyMultiplier: { tier1: 0.8, tier2: 1.0, tier3: 1.3, tier4: 1.8 },
    region: '华南'
  },
  {
    id: 'suzhou-gov',
    name: '苏州市政府',
    url: 'https://www.suzhou.gov.cn',
    category: 'regional',
    subcategory: '政府门户',
    description: '苏州市人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 35, foreign: 210 },
    popularity: 3,
    tags: ['政府', '二线城市', '苏州'],
    cityTier: 2,
    latencyMultiplier: { tier1: 0.8, tier2: 1.0, tier3: 1.3, tier4: 1.8 },
    region: '华东'
  },



  // 更多省级门户
  {
    id: 'guangdong-gov',
    name: '广东省政府',
    url: 'https://www.gd.gov.cn',
    category: 'regional',
    subcategory: '省级门户',
    description: '广东省人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 32, foreign: 195 },
    popularity: 4,
    tags: ['政府', '省级', '广东'],
    cityTier: 1,
    latencyMultiplier: { tier1: 1.0, tier2: 1.2, tier3: 1.5, tier4: 2.0 },
    region: '华南'
  },
  {
    id: 'sichuan-gov',
    name: '四川省政府',
    url: 'https://www.sc.gov.cn',
    category: 'regional',
    subcategory: '省级门户',
    description: '四川省人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 42, foreign: 240 },
    popularity: 3,
    tags: ['政府', '省级', '四川'],
    cityTier: 2,
    latencyMultiplier: { tier1: 0.8, tier2: 1.0, tier3: 1.3, tier4: 1.8 },
    region: '西南'
  },
  {
    id: 'hubei-gov',
    name: '湖北省政府',
    url: 'https://www.hubei.gov.cn',
    category: 'regional',
    subcategory: '省级门户',
    description: '湖北省人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 40, foreign: 230 },
    popularity: 3,
    tags: ['政府', '省级', '湖北'],
    cityTier: 2,
    latencyMultiplier: { tier1: 0.8, tier2: 1.0, tier3: 1.3, tier4: 1.8 },
    region: '华中'
  },
  {
    id: 'henan-gov',
    name: '河南省政府',
    url: 'https://www.henan.gov.cn',
    category: 'regional',
    subcategory: '省级门户',
    description: '河南省人民政府官网',
    icon: '🏛️',
    expectedLatency: { domestic: 45, foreign: 250 },
    popularity: 3,
    tags: ['政府', '省级', '河南'],
    cityTier: 2,
    latencyMultiplier: { tier1: 0.8, tier2: 1.0, tier3: 1.3, tier4: 1.8 },
    region: '华中'
  },


];

// 合并所有网站
export const ALL_TEST_SITES: TestSite[] = [...DOMESTIC_SITES, ...FOREIGN_SITES, ...REGIONAL_SITES];

// 按分类获取网站
export const getSitesByCategory = (category: 'domestic' | 'foreign' | 'regional'): TestSite[] => {
  return ALL_TEST_SITES.filter(site => site.category === category);
};

// 按子分类获取网站
export const getSitesBySubcategory = (subcategory: string): TestSite[] => {
  return ALL_TEST_SITES.filter(site => site.subcategory === subcategory);
};

// 获取热门网站（4星及以上）
export const getPopularSites = (): TestSite[] => {
  return ALL_TEST_SITES.filter(site => site.popularity >= 4);
};

// 获取常用测试网站（推荐用于快速测试）
export const getRecommendedSites = (): TestSite[] => {
  return [
    // 国内推荐
    ALL_TEST_SITES.find(s => s.id === 'baidu')!,
    ALL_TEST_SITES.find(s => s.id === 'taobao')!,
    ALL_TEST_SITES.find(s => s.id === 'tencent')!,
    ALL_TEST_SITES.find(s => s.id === 'bilibili')!,
    
    // 国外推荐
    ALL_TEST_SITES.find(s => s.id === 'google')!,
    ALL_TEST_SITES.find(s => s.id === 'amazon')!,
    ALL_TEST_SITES.find(s => s.id === 'github')!,
    ALL_TEST_SITES.find(s => s.id === 'apple')!
  ];
};

// 获取所有子分类
export const getAllSubcategories = (): string[] => {
  const subcategories = new Set(ALL_TEST_SITES.map(site => site.subcategory));
  return Array.from(subcategories).sort();
};

// 按城市等级获取网站
export const getSitesByTier = (tier: 1 | 2 | 3 | 4): TestSite[] => {
  return ALL_TEST_SITES.filter(site => site.cityTier === tier);
};

// 按地区获取网站
export const getSitesByRegion = (region: string): TestSite[] => {
  return ALL_TEST_SITES.filter(site => site.region === region);
};

// 获取所有地区
export const getAllRegions = (): string[] => {
  const regions = new Set(ALL_TEST_SITES.map(site => site.region).filter(Boolean));
  return Array.from(regions).sort();
};

// 计算基于城市等级的预期延迟
export const calculateExpectedLatency = (site: TestSite, userTier: 1 | 2 | 3 | 4): number => {
  if (!site.latencyMultiplier) {
    return site.expectedLatency.domestic;
  }

  const baseLatency = site.expectedLatency.domestic;
  const multiplier = site.latencyMultiplier[`tier${userTier}` as keyof typeof site.latencyMultiplier];

  return Math.round(baseLatency * multiplier);
};

// 获取推荐的区域测试网站（仅政府门户）
export const getRecommendedRegionalSites = (): TestSite[] => {
  return [
    // 政府门户 - 一线城市
    ALL_TEST_SITES.find(s => s.id === 'beijing-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'shanghai-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'guangzhou-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'shenzhen-gov')!,

    // 政府门户 - 直辖市
    ALL_TEST_SITES.find(s => s.id === 'tianjin-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'chongqing-gov')!,

    // 政府门户 - 重要二线城市
    ALL_TEST_SITES.find(s => s.id === 'hangzhou-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'nanjing-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'wuhan-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'chengdu-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'xian-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'qingdao-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'dalian-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'xiamen-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'suzhou-gov')!,

    // 省级门户
    ALL_TEST_SITES.find(s => s.id === 'jiangsu-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'zhejiang-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'guangdong-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'shandong-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'sichuan-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'hubei-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'henan-gov')!,

    // 三线城市代表
    ALL_TEST_SITES.find(s => s.id === 'hefei-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'kunming-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'nanning-gov')!,

    // 四线城市代表
    ALL_TEST_SITES.find(s => s.id === 'yinchuan-gov')!,
    ALL_TEST_SITES.find(s => s.id === 'lhasa-gov')!
  ];
};


