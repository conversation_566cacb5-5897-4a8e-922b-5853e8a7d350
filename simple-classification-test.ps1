# 简单的网站分类测试

$apiUrl = "https://ping.wobshare.us.kg/api/hybrid-ping"

Write-Host "开始测试网站分类准确性..." -ForegroundColor Green
Write-Host "测试时间: $(Get-Date)" -ForegroundColor Cyan
Write-Host ("=" * 60) -ForegroundColor Yellow

# 测试国内网站
Write-Host "`n=== 国内知名网站 (预期<100ms) ===" -ForegroundColor Green

$domesticSites = @("baidu.com", "taobao.com", "jd.com", "qq.com", "weibo.com")
$domesticResults = @()

foreach ($site in $domesticSites) {
    Write-Host "`n测试: $site" -ForegroundColor Yellow
    try {
        $body = @{target = $site} | ConvertTo-Json
        $response = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -Body $body -TimeoutSec 30
        
        $latency = $response.averageLatency
        $method = $response.testMethod
        $category = $response.category
        
        $pass = $latency -lt 100
        $status = if ($pass) { "合格" } else { "不合格" }
        
        Write-Host "  延迟: ${latency}ms | 方法: $method | 类别: $category | $status" -ForegroundColor White
        
        $domesticResults += @{
            site = $site
            latency = $latency
            method = $method
            category = $category
            pass = $pass
        }
    } catch {
        Write-Host "  错误: $($_.Exception.Message)" -ForegroundColor Red
    }
    Start-Sleep -Seconds 1
}

# 测试被墙网站
Write-Host "`n=== 被墙国外网站 (预期>250ms) ===" -ForegroundColor Red

$blockedSites = @("google.com", "facebook.com", "twitter.com", "instagram.com", "reddit.com")
$blockedResults = @()

foreach ($site in $blockedSites) {
    Write-Host "`n测试: $site" -ForegroundColor Yellow
    try {
        $body = @{target = $site} | ConvertTo-Json
        $response = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -Body $body -TimeoutSec 30
        
        $latency = $response.averageLatency
        $method = $response.testMethod
        $category = $response.category
        
        $pass = $latency -gt 250
        $status = if ($pass) { "合格" } else { "不合格" }
        
        Write-Host "  延迟: ${latency}ms | 方法: $method | 类别: $category | $status" -ForegroundColor White
        
        $blockedResults += @{
            site = $site
            latency = $latency
            method = $method
            category = $category
            pass = $pass
        }
    } catch {
        Write-Host "  错误: $($_.Exception.Message)" -ForegroundColor Red
    }
    Start-Sleep -Seconds 1
}

# 汇总结果
Write-Host "`n=== 测试结果汇总 ===" -ForegroundColor Green
Write-Host ("=" * 60) -ForegroundColor Yellow

$domesticPass = ($domesticResults | Where-Object { $_.pass }).Count
$blockedPass = ($blockedResults | Where-Object { $_.pass }).Count

Write-Host "国内网站: $domesticPass/$($domesticResults.Count) 合格" -ForegroundColor Green
Write-Host "被墙网站: $blockedPass/$($blockedResults.Count) 合格" -ForegroundColor Red

$totalPass = $domesticPass + $blockedPass
$totalTests = $domesticResults.Count + $blockedResults.Count
$passRate = if ($totalTests -gt 0) { [math]::Round(($totalPass / $totalTests) * 100, 1) } else { 0 }

Write-Host "总体通过率: $totalPass/$totalTests ($passRate%)" -ForegroundColor Cyan

Write-Host "`n详细结果:" -ForegroundColor Magenta
Write-Host "国内网站:" -ForegroundColor Green
foreach ($result in $domesticResults) {
    $statusText = if ($result.pass) { "PASS" } else { "FAIL" }
    Write-Host "  $($result.site): $($result.latency)ms ($statusText)" -ForegroundColor White
}

Write-Host "被墙网站:" -ForegroundColor Red
foreach ($result in $blockedResults) {
    $statusText = if ($result.pass) { "PASS" } else { "FAIL" }
    Write-Host "  $($result.site): $($result.latency)ms ($statusText)" -ForegroundColor White
}

Write-Host "`n测试完成: $(Get-Date)" -ForegroundColor Cyan
