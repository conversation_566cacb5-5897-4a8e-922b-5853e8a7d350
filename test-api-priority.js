// 测试API优先级和数据质量
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {}
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testAPIPriority() {
  console.log('🎯 测试API优先级和数据质量...');
  
  try {
    // 测试wobshare.us.kg（与ITDOG.cn对比）
    console.log('\n📡 测试 wobshare.us.kg（对比ITDOG.cn）...');
    const response = await fetch('http://localhost:3001/api/enhanced-ping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target: 'wobshare.us.kg', maxNodes: 30 })
    });
    
    const data = await response.json();
    console.log('总结果数量:', data.results?.length || 0);
    
    if (data.results && data.results.length > 0) {
      // 按API来源分组统计
      const apiStats = {};
      data.results.forEach(result => {
        const source = result.apiSource || '未知';
        if (!apiStats[source]) {
          apiStats[source] = { count: 0, avgPing: 0, totalPing: 0 };
        }
        apiStats[source].count++;
        apiStats[source].totalPing += result.ping;
      });
      
      // 计算平均延迟
      Object.keys(apiStats).forEach(source => {
        apiStats[source].avgPing = Math.round(apiStats[source].totalPing / apiStats[source].count);
      });
      
      console.log('\n📊 API来源统计:');
      Object.entries(apiStats)
        .sort((a, b) => b[1].count - a[1].count)
        .forEach(([source, stats]) => {
          console.log(`- ${source}: ${stats.count}个节点, 平均${stats.avgPing}ms`);
        });
      
      // 省份覆盖统计
      const provinceStats = {};
      data.results.forEach(result => {
        const province = result.province || result.location?.province || '未知';
        if (!provinceStats[province]) {
          provinceStats[province] = { count: 0, minPing: Infinity, maxPing: 0 };
        }
        provinceStats[province].count++;
        provinceStats[province].minPing = Math.min(provinceStats[province].minPing, result.ping);
        provinceStats[province].maxPing = Math.max(provinceStats[province].maxPing, result.ping);
      });
      
      console.log('\n🗺️ 省份覆盖统计:');
      console.log(`总计覆盖 ${Object.keys(provinceStats).length} 个省份/地区`);
      
      // 显示延迟最低的前10个省份
      console.log('\n⚡ 延迟最低的省份:');
      Object.entries(provinceStats)
        .sort((a, b) => a[1].minPing - b[1].minPing)
        .slice(0, 10)
        .forEach(([province, stats]) => {
          console.log(`- ${province}: ${stats.minPing}ms (${stats.count}个节点)`);
        });
      
      // 显示延迟最高的省份
      console.log('\n🐌 延迟最高的省份:');
      Object.entries(provinceStats)
        .sort((a, b) => b[1].minPing - a[1].minPing)
        .slice(0, 5)
        .forEach(([province, stats]) => {
          console.log(`- ${province}: ${stats.minPing}ms (${stats.count}个节点)`);
        });
      
      // 检查是否有Globalping数据
      const globalpingResults = data.results.filter(r => r.apiSource === 'Globalping.io');
      if (globalpingResults.length > 0) {
        console.log(`\n✅ Globalping主力API正常工作: ${globalpingResults.length}个节点`);
      } else {
        console.log('\n❌ Globalping主力API未返回数据');
      }
      
      // 检查数据质量
      const successCount = data.results.filter(r => r.status === 'success').length;
      const successRate = Math.round((successCount / data.results.length) * 100);
      console.log(`\n📈 数据质量: ${successCount}/${data.results.length} 成功 (${successRate}%)`);
      
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
  
  console.log('\n✅ API优先级测试完成！');
}

testAPIPriority();
