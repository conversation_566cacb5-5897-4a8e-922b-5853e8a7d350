import React, { useState } from 'react';
import Head from 'next/head';

interface APITestResult {
  apiName: string;
  success: boolean;
  results: any[];
  error?: string;
  duration: number;
  status: 'loading' | 'success' | 'error' | 'pending';
}

interface TestResult {
  target: string;
  timestamp: string;
  apiResults: APITestResult[];
}

export default function AdminTestPage() {
  const [testUrl, setTestUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);

  // 预设的测试网站
  const presetUrls = [
    'https://www.baidu.com/',
    'https://www.google.com/',
    'https://www.qq.com/',
    'https://www.facebook.com/',
    'https://www.taobao.com/',
    'https://www.youtube.com/',
    'https://www.jd.com/',
    'https://www.twitter.com/',
    'https://www.sina.com.cn/',
    'https://www.github.com/'
  ];

  const testSingleUrl = async (url: string) => {
    const startTime = Date.now();
    try {
      console.log(`🚀 开始测试: ${url}`);
      
      const response = await fetch('/api/ping-cloudping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          target: url,
          maxNodes: 50,
          fastMode: false
        }),
      });

      const data = await response.json();
      const duration = Date.now() - startTime;

      const result: TestResult = {
        target: url,
        success: data.success || false,
        results: data.results || [],
        error: data.error,
        timestamp: new Date().toISOString(),
        duration
      };

      console.log(`✅ 测试完成: ${url}`, result);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      const result: TestResult = {
        target: url,
        success: false,
        results: [],
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        duration
      };
      
      console.error(`❌ 测试失败: ${url}`, error);
      return result;
    }
  };

  const handleSingleTest = async () => {
    if (!testUrl.trim()) {
      alert('请输入要测试的URL');
      return;
    }

    setIsLoading(true);
    try {
      const result = await testSingleUrl(testUrl.trim());
      setTestResults(prev => [result, ...prev]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBatchTest = async () => {
    setIsLoading(true);
    const results: TestResult[] = [];
    
    try {
      for (const url of presetUrls) {
        console.log(`📊 批量测试进度: ${results.length + 1}/${presetUrls.length}`);
        const result = await testSingleUrl(url);
        results.push(result);
        setTestResults(prev => [result, ...prev]);
        
        // 每次测试之间等待1秒，避免请求过于频繁
        if (results.length < presetUrls.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const exportResults = () => {
    const dataStr = JSON.stringify(testResults, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `ping-test-results-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <>
      <Head>
        <title>API测试后台 - Ping监控工具</title>
        <meta name="description" content="API测试后台管理页面" />
      </Head>

      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h1 className="text-2xl font-bold text-gray-900">🔧 API测试后台</h1>
              <p className="mt-1 text-sm text-gray-600">
                测试真实API延迟数据，无模拟数据
              </p>

              {/* API平台状态 */}
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <h3 className="text-sm font-medium text-blue-900 mb-2">🔗 可用API平台状态</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    <span>ITDOG.CN (主力)</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    <span>17CE.COM (备用)</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    <span>Chinaz.COM (备用)</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    <span>Ping.pe (全球)</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                    <span>Freshping (需配置)</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                    <span>HetrixTools (需配置)</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                    <span>Multi-Platform (降级)</span>
                  </div>
                </div>
                <p className="text-xs text-gray-600 mt-2">
                  🟢 可用 | 🟡 需要API密钥 | 🔵 降级备用
                </p>
              </div>
            </div>

            <div className="p-6">
              {/* 单个URL测试 */}
              <div className="mb-8">
                <h2 className="text-lg font-medium text-gray-900 mb-4">单个URL测试</h2>
                <div className="flex gap-4">
                  <input
                    type="url"
                    value={testUrl}
                    onChange={(e) => setTestUrl(e.target.value)}
                    placeholder="输入要测试的URL，例如: https://www.baidu.com/"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={isLoading}
                  />
                  <button
                    onClick={handleSingleTest}
                    disabled={isLoading}
                    className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? '测试中...' : '开始测试'}
                  </button>
                </div>
              </div>

              {/* 批量测试 */}
              <div className="mb-8">
                <h2 className="text-lg font-medium text-gray-900 mb-4">批量测试预设网站</h2>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-2 mb-4">
                  {presetUrls.map((url, index) => (
                    <div key={index} className="text-xs bg-gray-100 px-2 py-1 rounded">
                      {url.replace('https://www.', '').replace('/', '')}
                    </div>
                  ))}
                </div>
                <div className="flex gap-4">
                  <button
                    onClick={handleBatchTest}
                    disabled={isLoading}
                    className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isLoading ? '批量测试中...' : '开始批量测试'}
                  </button>
                  <button
                    onClick={clearResults}
                    disabled={isLoading}
                    className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    清空结果
                  </button>
                  <button
                    onClick={exportResults}
                    disabled={isLoading || testResults.length === 0}
                    className="px-6 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    导出结果
                  </button>
                </div>
              </div>

              {/* 测试结果 */}
              <div>
                <h2 className="text-lg font-medium text-gray-900 mb-4">
                  测试结果 ({testResults.length})
                </h2>
                
                {testResults.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    暂无测试结果
                  </div>
                ) : (
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {testResults.map((result, index) => (
                      <div
                        key={index}
                        className={`border rounded-lg p-4 ${
                          result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
                        }`}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h3 className="font-medium text-gray-900">{result.target}</h3>
                            <p className="text-sm text-gray-600">
                              {new Date(result.timestamp).toLocaleString()} | 
                              耗时: {result.duration}ms
                            </p>
                          </div>
                          <span
                            className={`px-2 py-1 text-xs rounded ${
                              result.success
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}
                          >
                            {result.success ? '成功' : '失败'}
                          </span>
                        </div>
                        
                        {result.error && (
                          <div className="mb-2">
                            <p className="text-sm text-red-600">错误: {result.error}</p>
                          </div>
                        )}
                        
                        {result.results.length > 0 && (
                          <div>
                            <p className="text-sm text-gray-600 mb-2">
                              获得 {result.results.length} 个节点数据
                            </p>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
                              {result.results.slice(0, 8).map((node, nodeIndex) => (
                                <div key={nodeIndex} className="bg-white px-2 py-1 rounded border">
                                  <div className="font-medium">
                                    {node.node} ({node.province}): {node.ping}ms
                                  </div>
                                  <div className="text-gray-500 text-xs">
                                    🔗 API: {node.apiSource || node.testMethod || '未知'}
                                    {node.priority && ` | 优先级: ${node.priority}`}
                                  </div>
                                </div>
                              ))}
                              {result.results.length > 8 && (
                                <div className="text-gray-500 col-span-full">
                                  ... 还有 {result.results.length - 8} 个节点
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
