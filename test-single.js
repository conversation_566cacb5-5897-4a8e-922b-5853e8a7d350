// 单个网站测试
async function testSingle() {
  try {
    console.log('🔍 测试 sohu.com...');
    
    const response = await fetch('http://localhost:3000/api/enhanced-ping-v2', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ target: 'sohu.com' }),
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const text = await response.text();
    console.log('Response text:', text);
    
    try {
      const result = JSON.parse(text);
      console.log('✅ 解析成功:', result);
    } catch (parseError) {
      console.log('❌ JSON解析失败:', parseError.message);
    }
    
  } catch (error) {
    console.log('🚫 请求异常:', error.message);
  }
}

testSingle();
