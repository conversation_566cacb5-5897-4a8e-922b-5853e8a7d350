// 混合策略测试脚本
const testWebsites = [
  'reddit.com',
  'sohu.com', 
  'www.bbc.com',
  'www.linovel.net',
  'www.pinterest.com',
  'www.xygalaxy.com',
  'baidu.com',
  'google.com',
  'hitpaw.com',
  'feejii.com',
  'annas-archive.org',
  'archive.org'
];

async function testHybridStrategy(domain) {
  try {
    console.log(`\n🔍 测试 ${domain}...`);
    
    const response = await fetch('http://localhost:3000/api/enhanced-ping-v2', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ target: domain }),
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log(`✅ ${domain}: ${result.metadata.averageLatency}ms (${result.results.length}个节点)`);
      return { domain, latency: result.metadata.averageLatency, success: true };
    } else {
      console.log(`❌ ${domain}: 测试失败 - ${result.error}`);
      return { domain, latency: -1, success: false, error: result.error };
    }
    
  } catch (error) {
    console.log(`🚫 ${domain}: 异常 - ${error.message}`);
    return { domain, latency: -1, success: false, error: error.message };
  }
}

async function runHybridTests() {
  console.log('🚀 开始混合策略延迟测试...\n');
  
  const results = [];
  
  for (const domain of testWebsites) {
    const result = await testHybridStrategy(domain);
    results.push(result);
    
    // 等待1秒避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📊 混合策略测试结果汇总:');
  console.log('==========================================');
  
  const successResults = results.filter(r => r.success);
  const failedResults = results.filter(r => !r.success);
  
  console.log('\n✅ 成功测试的网站:');
  successResults.forEach(r => {
    console.log(`   ${r.domain.padEnd(20)} ${r.latency}ms`);
  });
  
  if (failedResults.length > 0) {
    console.log('\n❌ 失败的网站:');
    failedResults.forEach(r => {
      console.log(`   ${r.domain.padEnd(20)} ${r.error || '未知错误'}`);
    });
  }
  
  console.log(`\n📈 统计信息:`);
  console.log(`   总测试数: ${results.length}`);
  console.log(`   成功数: ${successResults.length}`);
  console.log(`   失败数: ${failedResults.length}`);
  console.log(`   成功率: ${((successResults.length / results.length) * 100).toFixed(1)}%`);
  
  if (successResults.length > 0) {
    const latencies = successResults.map(r => r.latency);
    const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
    const minLatency = Math.min(...latencies);
    const maxLatency = Math.max(...latencies);
    
    console.log(`   平均延迟: ${avgLatency.toFixed(1)}ms`);
    console.log(`   最小延迟: ${minLatency}ms`);
    console.log(`   最大延迟: ${maxLatency}ms`);
  }
  
  // 分类分析
  console.log('\n🏷️ 延迟分类分析:');
  const fast = successResults.filter(r => r.latency < 50);
  const medium = successResults.filter(r => r.latency >= 50 && r.latency < 150);
  const slow = successResults.filter(r => r.latency >= 150 && r.latency < 300);
  const verySlow = successResults.filter(r => r.latency >= 300);
  
  console.log(`   快速 (<50ms): ${fast.length}个 - ${fast.map(r => r.domain).join(', ')}`);
  console.log(`   中等 (50-150ms): ${medium.length}个 - ${medium.map(r => r.domain).join(', ')}`);
  console.log(`   较慢 (150-300ms): ${slow.length}个 - ${slow.map(r => r.domain).join(', ')}`);
  console.log(`   很慢 (≥300ms): ${verySlow.length}个 - ${verySlow.map(r => r.domain).join(', ')}`);
}

// 运行测试
runHybridTests().catch(console.error);
