// 智能缓存管理系统

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // 生存时间（毫秒）
  accessCount: number;
  lastAccess: number;
  priority: number; // 优先级 1-10
}

export interface CacheStats {
  totalEntries: number;
  hitRate: number;
  missRate: number;
  totalHits: number;
  totalMisses: number;
  memoryUsage: number;
  oldestEntry: number;
  newestEntry: number;
}

class CacheManager {
  private cache = new Map<string, CacheEntry<any>>();
  private stats = {
    hits: 0,
    misses: 0,
    totalRequests: 0
  };
  private maxSize: number;
  private defaultTTL: number;

  constructor(maxSize: number = 1000, defaultTTL: number = 300000) { // 默认5分钟TTL
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;

    // 智能清理调度
    this.scheduleIntelligentCleanup();
  }

  // 设置缓存
  set<T>(key: string, data: T, ttl?: number, priority: number = 5): void {
    const now = Date.now();
    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      ttl: ttl || this.defaultTTL,
      accessCount: 0,
      lastAccess: now,
      priority
    };

    // 如果缓存已满，清理低优先级或过期的条目
    if (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }

    this.cache.set(key, entry);
  }

  // 获取缓存
  get<T>(key: string): T | null {
    this.stats.totalRequests++;
    
    const entry = this.cache.get(key);
    if (!entry) {
      this.stats.misses++;
      return null;
    }

    const now = Date.now();
    
    // 检查是否过期
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    // 更新访问统计
    entry.accessCount++;
    entry.lastAccess = now;
    this.stats.hits++;

    return entry.data as T;
  }

  // 检查缓存是否存在且有效
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  // 删除缓存
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  // 清空所有缓存
  clear(): void {
    this.cache.clear();
    this.stats = { hits: 0, misses: 0, totalRequests: 0 };
  }

  // 获取缓存统计
  getStats(): CacheStats {
    const entries = Array.from(this.cache.values());
    const now = Date.now();
    
    return {
      totalEntries: this.cache.size,
      hitRate: this.stats.totalRequests > 0 ? (this.stats.hits / this.stats.totalRequests) * 100 : 0,
      missRate: this.stats.totalRequests > 0 ? (this.stats.misses / this.stats.totalRequests) * 100 : 0,
      totalHits: this.stats.hits,
      totalMisses: this.stats.misses,
      memoryUsage: this.estimateMemoryUsage(),
      oldestEntry: entries.length > 0 ? Math.min(...entries.map(e => e.timestamp)) : 0,
      newestEntry: entries.length > 0 ? Math.max(...entries.map(e => e.timestamp)) : 0
    };
  }

  // 智能清理调度
  private scheduleIntelligentCleanup(): void {
    const cleanup = () => {
      this.intelligentCleanup();
      // 根据缓存使用情况动态调整清理间隔
      const interval = this.calculateOptimalCleanupInterval();
      setTimeout(cleanup, interval);
    };

    // 初始延迟1分钟后开始清理
    setTimeout(cleanup, 60000);
  }

  // 智能清理策略
  private intelligentCleanup(): void {
    const memoryUsage = this.estimateMemoryUsage();
    const memoryThreshold = 50 * 1024 * 1024; // 50MB阈值

    // 如果内存使用超过阈值，进行积极清理
    if (memoryUsage > memoryThreshold) {
      this.aggressiveCleanup();
    } else {
      this.normalCleanup();
    }
  }

  // 正常清理：只清理过期项
  private normalCleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
  }

  // 积极清理：清理过期项和低优先级项
  private aggressiveCleanup(): void {
    const now = Date.now();
    const entries = Array.from(this.cache.entries());

    // 按优先级和访问时间排序
    entries.sort(([, a], [, b]) => {
      const scoreA = a.priority * 1000 + (now - a.lastAccess);
      const scoreB = b.priority * 1000 + (now - b.lastAccess);
      return scoreA - scoreB;
    });

    // 清理最低优先级的25%项目
    const itemsToRemove = Math.floor(entries.length * 0.25);
    for (let i = 0; i < itemsToRemove; i++) {
      this.cache.delete(entries[i][0]);
    }
  }

  // 计算最优清理间隔
  private calculateOptimalCleanupInterval(): number {
    const cacheSize = this.cache.size;
    const utilizationRate = cacheSize / this.maxSize;

    // 根据缓存利用率调整清理频率
    if (utilizationRate > 0.8) {
      return 30000; // 30秒
    } else if (utilizationRate > 0.5) {
      return 60000; // 1分钟
    } else {
      return 120000; // 2分钟
    }
  }

  // 清理过期缓存（保留原方法以兼容）
  private cleanup(): void {
    this.normalCleanup();
  }

  // LRU淘汰策略
  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();
    let lowestPriority = 10;

    // 首先尝试删除低优先级的条目
    for (const [key, entry] of this.cache.entries()) {
      if (entry.priority < lowestPriority || 
          (entry.priority === lowestPriority && entry.lastAccess < oldestTime)) {
        oldestKey = key;
        oldestTime = entry.lastAccess;
        lowestPriority = entry.priority;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  // 估算内存使用量
  private estimateMemoryUsage(): number {
    let size = 0;
    for (const [key, entry] of this.cache.entries()) {
      size += key.length * 2; // 字符串按UTF-16计算
      size += JSON.stringify(entry.data).length * 2;
      size += 64; // 估算对象开销
    }
    return size;
  }

  // 获取热点数据
  getHotData(limit: number = 10): Array<{ key: string, accessCount: number, lastAccess: number }> {
    return Array.from(this.cache.entries())
      .map(([key, entry]) => ({
        key,
        accessCount: entry.accessCount,
        lastAccess: entry.lastAccess
      }))
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, limit);
  }

  // 预热缓存
  async warmup(keys: string[], dataLoader: (key: string) => Promise<any>): Promise<void> {
    const promises = keys.map(async (key) => {
      if (!this.has(key)) {
        try {
          const data = await dataLoader(key);
          this.set(key, data, undefined, 8); // 高优先级
        } catch (error) {
          // console.error removed
        }
      }
    });

    await Promise.all(promises);
  }
}

// 专门用于网络测试的缓存管理器
class NetworkTestCache extends CacheManager {
  constructor() {
    super(500, 180000); // 最多500条记录，3分钟TTL
  }

  // 生成测试结果的缓存键
  generateTestKey(target: string, testType: string): string {
    return `test:${testType}:${target}`;
  }

  // 生成节点测试的缓存键
  generateNodeKey(target: string, node: string): string {
    return `node:${target}:${node}`;
  }

  // 缓存测试结果
  cacheTestResult(target: string, testType: string, result: any, ttl?: number): void {
    const key = this.generateTestKey(target, testType);
    this.set(key, result, ttl, 7); // 高优先级
  }

  // 获取缓存的测试结果
  getCachedTestResult<T>(target: string, testType: string): T | null {
    const key = this.generateTestKey(target, testType);
    return this.get<T>(key);
  }

  // 缓存节点测试结果
  cacheNodeResult(target: string, node: string, result: any, ttl?: number): void {
    const key = this.generateNodeKey(target, node);
    this.set(key, result, ttl, 6); // 中等优先级
  }

  // 获取缓存的节点结果
  getCachedNodeResult<T>(target: string, node: string): T | null {
    const key = this.generateNodeKey(target, node);
    return this.get<T>(key);
  }

  // 批量获取节点结果
  getBatchNodeResults<T>(target: string, nodes: string[]): Map<string, T> {
    const results = new Map<string, T>();
    
    nodes.forEach(node => {
      const result = this.getCachedNodeResult<T>(target, node);
      if (result) {
        results.set(node, result);
      }
    });

    return results;
  }

  // 智能缓存策略：根据目标类型调整TTL
  smartCache(target: string, testType: string, result: any): void {
    let ttl = this.defaultTTL;
    let priority = 5;

    // 根据目标类型调整缓存策略
    if (target.includes('cdn') || target.includes('cloudflare')) {
      ttl = 600000; // CDN结果缓存10分钟
      priority = 8;
    } else if (target.includes('google') || target.includes('youtube')) {
      ttl = 60000; // 被墙网站缓存1分钟
      priority = 3;
    } else if (target.includes('baidu') || target.includes('qq')) {
      ttl = 300000; // 国内网站缓存5分钟
      priority = 7;
    }

    // 根据测试类型调整
    if (testType === 'webrtc') {
      ttl = Math.min(ttl, 120000); // WebRTC结果最多缓存2分钟
    } else if (testType === 'enhanced') {
      priority += 1; // 增强测试结果优先级更高
    }

    const key = this.generateTestKey(target, testType);
    this.set(key, result, ttl, priority);
  }
}

// 单例实例
export const networkTestCache = new NetworkTestCache();
export const globalCache = new CacheManager();

export default CacheManager;
