# 简化的本地ping测试脚本

$testWebsites = @(
    "baidu.com",
    "sohu.com", 
    "www.linovel.net",
    "www.xygalaxy.com",
    "freedidi.com",
    "hitpaw.com",
    "feejii.com",
    "google.com",
    "reddit.com",
    "bbc.com",
    "pinterest.com",
    "annas-archive.org",
    "archive.org"
)

Write-Host "开始本地ping测试..." -ForegroundColor Green
Write-Host "测试时间: $(Get-Date)" -ForegroundColor Cyan
Write-Host ("=" * 60) -ForegroundColor Yellow

$results = @()

foreach ($website in $testWebsites) {
    Write-Host "`nPing测试网站: $website" -ForegroundColor Yellow
    Write-Host ("-" * 40) -ForegroundColor Gray
    
    try {
        # 执行ping命令，发送4个包
        $pingOutput = ping $website -n 4 2>&1 | Out-String
        
        # 解析ping结果
        $success = $false
        $avgLatency = 0
        
        # 尝试匹配中文输出
        if ($pingOutput -match "平均 = (\d+)ms") {
            $avgLatency = [int]$matches[1]
            $success = $true
        }
        # 尝试匹配英文输出
        elseif ($pingOutput -match "Average = (\d+)ms") {
            $avgLatency = [int]$matches[1]
            $success = $true
        }
        
        if ($success) {
            Write-Host "Ping成功 - 平均延迟: ${avgLatency}ms" -ForegroundColor Green
            
            $evaluation = "正常"
            if ($avgLatency -lt 50) {
                $evaluation = "非常快"
            } elseif ($avgLatency -lt 100) {
                $evaluation = "正常"
            } elseif ($avgLatency -lt 200) {
                $evaluation = "较慢"
            } else {
                $evaluation = "很慢"
            }
            
            $results += @{
                website = $website
                success = $true
                avgLatency = $avgLatency
                evaluation = $evaluation
            }
        } else {
            Write-Host "Ping失败或超时" -ForegroundColor Red
            $results += @{
                website = $website
                success = $false
                avgLatency = 0
                evaluation = "无法连接"
            }
        }
        
    } catch {
        Write-Host "测试异常: $($_.Exception.Message)" -ForegroundColor Red
        $results += @{
            website = $website
            success = $false
            avgLatency = 0
            evaluation = "测试异常"
        }
    }
    
    Start-Sleep -Seconds 1
}

# 生成汇总报告
Write-Host "`n`n本地Ping测试汇总报告" -ForegroundColor Green
Write-Host ("=" * 60) -ForegroundColor Yellow

$successfulTests = $results | Where-Object { $_.success -eq $true }
$failedTests = $results | Where-Object { $_.success -eq $false }

Write-Host "成功测试: $($successfulTests.Count)/$($results.Count)" -ForegroundColor Green
Write-Host "失败测试: $($failedTests.Count)/$($results.Count)" -ForegroundColor Red

if ($successfulTests.Count -gt 0) {
    $avgLatency = [math]::Round(($successfulTests | Measure-Object -Property avgLatency -Average).Average)
    Write-Host "平均延迟: ${avgLatency}ms" -ForegroundColor Cyan
    
    Write-Host "`n详细结果:" -ForegroundColor Magenta
    foreach ($result in $results) {
        if ($result.success) {
            Write-Host "  $($result.website): $($result.avgLatency)ms ($($result.evaluation))" -ForegroundColor White
        } else {
            Write-Host "  $($result.website): $($result.evaluation)" -ForegroundColor Red
        }
    }
}

Write-Host "`n测试完成时间: $(Get-Date)" -ForegroundColor Cyan
