<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>节点分类测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .node-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .node-section h4 {
            margin-top: 0;
            color: #333;
        }
        .node-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 8px;
            margin-top: 10px;
        }
        .node-item {
            background: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
        }
        .globalping-node {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .china-node {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .global-node {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .stat-item {
            background: #e9ecef;
            padding: 10px 15px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 节点分类测试</h1>
        <p>测试Globalping节点是否正确分类到全球模式，而不是中国模式</p>
        
        <div>
            <button class="test-button" onclick="testNodeClassification()">
                开始测试节点分类
            </button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        async function testNodeClassification() {
            const resultsEl = document.getElementById('results');
            resultsEl.innerHTML = '<div style="text-align: center; padding: 20px;">🔄 正在测试节点分类...</div>';
            
            try {
                // 获取所有ping结果
                const response = await fetch('/api/ping-cloudping', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        target: 'https://www.google.com',
                        maxNodes: 80
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                analyzeNodeClassification(data.results || []);
                
            } catch (error) {
                resultsEl.innerHTML = `
                    <div class="results">
                        <h3>❌ 测试失败</h3>
                        <p>错误信息: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        function analyzeNodeClassification(allResults) {
            const resultsEl = document.getElementById('results');
            
            // 模拟前端的过滤逻辑
            const chinaNodes = allResults.filter(result => {
                const nodeName = (result.node || '').toString();
                
                // 排除Globalping节点
                if (nodeName.includes('-GP') || result.apiSource === 'Globalping') {
                    return false;
                }
                
                // 基于API来源判断中国节点
                if (result.apiSource === 'ITDOG.CN') {
                    return true;
                }
                
                // 基于province字段判断中国节点（排除国外省份）
                const province = result.location?.province || '';
                const foreignProvinces = ['美国', '英国', '德国', '法国', '日本', '韩国', '新加坡', '澳大利亚', '加拿大', '巴西', '阿根廷', '印度', '泰国', '马来西亚', '越南', '印尼', '菲律宾', '新西兰', '南非', '埃及', '土耳其', '以色列', '阿联酋', '沙特', '俄罗斯'];
                if (province && !foreignProvinces.includes(province)) {
                    return true;
                }
                
                // 基于location信息判断中国节点
                if (result.location?.country && ['China', 'CN', '中国'].includes(result.location.country)) {
                    return true;
                }
                
                return false;
            });
            
            // 全球节点（非中国节点）
            const globalNodes = allResults.filter(result => {
                const nodeName = (result.node || '').toString();
                
                // 包含Globalping节点
                if (nodeName.includes('-GP') || result.apiSource === 'Globalping') {
                    return true;
                }
                
                // 其他全球节点逻辑...
                return !chinaNodes.includes(result);
            });
            
            // Globalping节点
            const globalpingNodes = allResults.filter(result => {
                const nodeName = (result.node || '').toString();
                return nodeName.includes('-GP') || result.apiSource === 'Globalping';
            });
            
            // 检查是否有Globalping节点被错误分类到中国节点
            const misclassifiedNodes = chinaNodes.filter(result => {
                const nodeName = (result.node || '').toString();
                return nodeName.includes('-GP') || result.apiSource === 'Globalping';
            });
            
            const html = `
                <div class="results">
                    <h3>📊 节点分类测试结果</h3>
                    
                    <div class="stats">
                        <div class="stat-item">
                            <div class="stat-number">${allResults.length}</div>
                            <div class="stat-label">总节点数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${chinaNodes.length}</div>
                            <div class="stat-label">中国节点</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${globalNodes.length}</div>
                            <div class="stat-label">全球节点</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${globalpingNodes.length}</div>
                            <div class="stat-label">Globalping节点</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${misclassifiedNodes.length}</div>
                            <div class="stat-label">错误分类</div>
                        </div>
                    </div>
                    
                    ${misclassifiedNodes.length > 0 ? `
                        <div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; border-radius: 4px; margin: 10px 0;">
                            <strong>⚠️ 发现错误分类的节点:</strong>
                            <div class="node-list">
                                ${misclassifiedNodes.map(node => `
                                    <div class="node-item globalping-node">
                                        ${node.node}<br>
                                        <small>${node.apiSource}</small>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : `
                        <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; border-radius: 4px; margin: 10px 0;">
                            <strong>✅ 节点分类正确！所有Globalping节点都被正确分类到全球模式。</strong>
                        </div>
                    `}
                    
                    <div class="node-section">
                        <h4>🇨🇳 中国节点 (${chinaNodes.length}个)</h4>
                        <div class="node-list">
                            ${chinaNodes.slice(0, 20).map(node => `
                                <div class="node-item china-node">
                                    ${node.node}<br>
                                    <small>${node.apiSource || 'N/A'}</small>
                                </div>
                            `).join('')}
                            ${chinaNodes.length > 20 ? `<div class="node-item">...还有${chinaNodes.length - 20}个</div>` : ''}
                        </div>
                    </div>
                    
                    <div class="node-section">
                        <h4>🌍 Globalping节点 (${globalpingNodes.length}个)</h4>
                        <div class="node-list">
                            ${globalpingNodes.map(node => `
                                <div class="node-item globalping-node">
                                    ${node.node}<br>
                                    <small>${node.ping}ms - ${node.location?.country || 'N/A'}</small>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    
                    <div class="node-section">
                        <h4>🌐 其他全球节点 (${globalNodes.filter(n => !globalpingNodes.includes(n)).length}个)</h4>
                        <div class="node-list">
                            ${globalNodes.filter(n => !globalpingNodes.includes(n)).slice(0, 15).map(node => `
                                <div class="node-item global-node">
                                    ${node.node}<br>
                                    <small>${node.location?.country || 'N/A'}</small>
                                </div>
                            `).join('')}
                            ${globalNodes.filter(n => !globalpingNodes.includes(n)).length > 15 ? `<div class="node-item">...还有更多</div>` : ''}
                        </div>
                    </div>
                </div>
            `;
            
            resultsEl.innerHTML = html;
        }
    </script>
</body>
</html>
