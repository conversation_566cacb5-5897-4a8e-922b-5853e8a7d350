// 被GFW完全封锁的网站列表配置文件
// 按类别整理，便于维护和更新

export interface BlockedSiteCategory {
  name: string;
  icon: string;
  domains: string[];
}

// 🚫 被GFW完全封锁的网站列表（按类别整理）
export const BLOCKED_SITES_CATEGORIES: BlockedSiteCategory[] = [
  {
    name: '搜索引擎',
    icon: '🔍',
    domains: [
      'google.com',
      'duckduckgo.com',
      'startpage.com',
      'wikipedia.org'
    ]
  },
  {
    name: '社交媒体',
    icon: '📱',
    domains: [
      'facebook.com',
      'instagram.com',
      'twitter.com',
      'x.com',
      'threads.net',
      'reddit.com',
      'pinterest.com',
      'snapchat.com',
      'clubhouse.com',
      'tumblr.com',
      'quora.com'
    ]
  },
  {
    name: '视频／流媒体平台',
    icon: '🎞️',
    domains: [
      'youtube.com',
      'vimeo.com',
      'dailymotion.com',
      'twitch.tv',
      'netflix.com',
      'spotify.com'
    ]
  },
  {
    name: '即时通讯服务',
    icon: '💬',
    domains: [
      'telegram.org',
      'signal.org'
    ]
  },
  {
    name: '邮箱／云服务／办公工具',
    icon: '📧',
    domains: [
      'gmail.com',
      'mail.google.com',
      'drive.google.com',
      'dropbox.com',
      'protonmail.com',
      'tutanota.com'
    ]
  },
  {
    name: '开发者平台／代码托管',
    icon: '👨‍💻',
    domains: [
      'copilot.github.com',
      'sourceforge.net',
      'hub.docker.com'
    ]
  },
  {
    name: '学术与知识库',
    icon: '📚',
    domains: [
      'archive.org',
      'web.archive.org',
      'sci-hub.*',
      'jstor.org',
      'wikileaks.org'
    ]
  },
  {
    name: '新闻／政治／NGO',
    icon: '📰',
    domains: [
      'nytimes.com',
      'bbc.com',
      'rfa.org',
      'voachinese.com',
      'epochtimes.com',
      'amnesty.org',
      'renminbao.com',
      'hongkongfp.com',
      'pincong.rocks'
    ]
  },
  {
    name: '匿名与隐私工具',
    icon: '🕵️',
    domains: [
      'torproject.org',
      'psiphon.ca',
      'getlantern.org',
      'geti2p.net',
      'ultrasurf.us'
    ]
  },
  {
    name: 'AI 与技术服务',
    icon: '🤖',
    domains: [
      'openai.com',
      'claude.ai',
      'perplexity.ai',
      'huggingface.co'
    ]
  }
];

// 扁平化的被墙网站域名列表（用于快速检查）
export const BLOCKED_DOMAINS: string[] = BLOCKED_SITES_CATEGORIES
  .flatMap(category => category.domains);

// 检查域名是否被墙的函数
export function isBlockedDomain(domain: string): boolean {
  const domainLower = domain.toLowerCase();
  return BLOCKED_DOMAINS.some(blockedDomain => {
    // 处理通配符匹配（如 sci-hub.*）
    if (blockedDomain.includes('*')) {
      const pattern = blockedDomain.replace(/\*/g, '.*');
      const regex = new RegExp(pattern, 'i');
      return regex.test(domainLower);
    }
    // 普通域名匹配
    return domainLower.includes(blockedDomain) || domainLower.endsWith(blockedDomain);
  });
}

// 获取被墙网站的分类信息
export function getBlockedSiteCategory(domain: string): BlockedSiteCategory | null {
  const domainLower = domain.toLowerCase();
  
  for (const category of BLOCKED_SITES_CATEGORIES) {
    const isInCategory = category.domains.some(blockedDomain => {
      if (blockedDomain.includes('*')) {
        const pattern = blockedDomain.replace(/\*/g, '.*');
        const regex = new RegExp(pattern, 'i');
        return regex.test(domainLower);
      }
      return domainLower.includes(blockedDomain) || domainLower.endsWith(blockedDomain);
    });
    
    if (isInCategory) {
      return category;
    }
  }
  
  return null;
}

// 导出总数统计
export const BLOCKED_SITES_STATS = {
  totalCategories: BLOCKED_SITES_CATEGORIES.length,
  totalDomains: BLOCKED_DOMAINS.length,
  categoriesWithCounts: BLOCKED_SITES_CATEGORIES.map(cat => ({
    name: cat.name,
    icon: cat.icon,
    count: cat.domains.length
  }))
};
