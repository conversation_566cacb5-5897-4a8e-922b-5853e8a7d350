// 测试所有网站的延迟情况
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 30000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testWebsite(domain, category) {
  console.log(`\n🔍 测试 ${domain} (${category})...`);
  
  try {
    const start = Date.now();
    const response = await fetch('http://localhost:3002/api/enhanced-ping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target: domain }),
      timeout: 45000
    });
    
    const end = Date.now();
    const data = await response.json();
    
    if (!data.success) {
      console.log(`❌ ${domain} - API错误: ${data.error}`);
      return null;
    }
    
    const results = data.results || [];
    const successResults = results.filter(r => r.status === 'success');
    
    if (successResults.length === 0) {
      console.log(`❌ ${domain} - 无成功结果`);
      return null;
    }
    
    // 分析结果
    const pings = successResults.map(r => r.ping);
    const avgPing = Math.round(pings.reduce((a, b) => a + b, 0) / pings.length);
    const minPing = Math.min(...pings);
    const maxPing = Math.max(...pings);
    
    // 按API来源分组
    const apiGroups = {};
    successResults.forEach(result => {
      const source = result.apiSource || '未知';
      if (!apiGroups[source]) {
        apiGroups[source] = [];
      }
      apiGroups[source].push(result);
    });
    
    console.log(`✅ ${domain} - 响应时间: ${end - start}ms`);
    console.log(`📊 节点统计: ${successResults.length}/${results.length} 成功`);
    console.log(`⚡ 延迟范围: ${minPing}ms - ${maxPing}ms (平均${avgPing}ms)`);
    
    // 显示API来源分布
    console.log(`📡 API来源分布:`);
    Object.entries(apiGroups).forEach(([source, sourceResults]) => {
      const sourcePings = sourceResults.map(r => r.ping);
      const sourceAvg = Math.round(sourcePings.reduce((a, b) => a + b, 0) / sourcePings.length);
      console.log(`  - ${source}: ${sourceResults.length}个节点, 平均${sourceAvg}ms`);
    });
    
    // 检查异常情况
    const lowLatency = successResults.filter(r => r.ping < 30);
    const highLatency = successResults.filter(r => r.ping > 500);
    
    if (lowLatency.length > 0) {
      console.log(`⚠️ 低延迟节点 (<30ms): ${lowLatency.length}个`);
      lowLatency.slice(0, 3).forEach(r => {
        console.log(`    ${r.node}: ${r.ping}ms (${r.apiSource})`);
      });
    }
    
    if (highLatency.length > 0) {
      console.log(`🐌 高延迟节点 (>500ms): ${highLatency.length}个`);
    }
    
    // 检查CDN标识
    const cdnResults = successResults.filter(r => 
      r.testMethod && r.testMethod.includes('CDN') || 
      r.note && r.note.includes('CDN')
    );
    
    if (cdnResults.length > 0) {
      console.log(`🌐 CDN节点: ${cdnResults.length}个 (已正确标识)`);
    }
    
    return {
      domain,
      category,
      avgPing,
      minPing,
      maxPing,
      successCount: successResults.length,
      totalCount: results.length,
      apiSources: Object.keys(apiGroups),
      cdnDetected: cdnResults.length > 0,
      responseTime: end - start
    };
    
  } catch (error) {
    console.log(`❌ ${domain} - 测试失败: ${error.message}`);
    return null;
  }
}

async function testAllWebsites() {
  console.log('🚀 开始测试所有网站...\n');
  
  // 定义测试网站
  const websites = [
    // 国内网站
    { domain: 'baidu.com', name: '百度', category: '国内' },
    { domain: 'taobao.com', name: '淘宝', category: '国内' },
    { domain: 'qq.com', name: '腾讯网', category: '国内' },
    { domain: 'weibo.com', name: '新浪微博', category: '国内' },
    { domain: 'zhihu.com', name: '知乎', category: '国内' },
    { domain: 'bilibili.com', name: 'Bilibili', category: '国内' },
    
    // 国外网站
    { domain: 'google.com', name: 'Google', category: '国外' },
    { domain: 'facebook.com', name: 'Facebook', category: '国外' },
    { domain: 'twitter.com', name: 'Twitter(X)', category: '国外' },
    { domain: 'instagram.com', name: 'Instagram', category: '国外' },
    { domain: 'whatsapp.com', name: 'WhatsApp', category: '国外' },
    { domain: 'telegram.org', name: 'Telegram', category: '国外' }
  ];
  
  const results = [];
  
  // 逐个测试网站
  for (const site of websites) {
    const result = await testWebsite(site.domain, site.category);
    if (result) {
      results.push({ ...result, name: site.name });
    }
    
    // 等待1秒避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 生成总结报告
  console.log('\n' + '='.repeat(60));
  console.log('📊 测试总结报告');
  console.log('='.repeat(60));
  
  const domesticSites = results.filter(r => r.category === '国内');
  const foreignSites = results.filter(r => r.category === '国外');
  
  console.log(`\n🇨🇳 国内网站 (${domesticSites.length}个):`);
  domesticSites.forEach(site => {
    const status = site.avgPing < 100 ? '✅' : site.avgPing < 300 ? '⚠️' : '❌';
    console.log(`${status} ${site.name} (${site.domain}): ${site.avgPing}ms`);
  });
  
  console.log(`\n🌍 国外网站 (${foreignSites.length}个):`);
  foreignSites.forEach(site => {
    const status = site.avgPing > 200 ? '✅' : '⚠️';
    console.log(`${status} ${site.name} (${site.domain}): ${site.avgPing}ms`);
  });
  
  // 数据质量分析
  console.log(`\n📈 数据质量分析:`);
  const allResults = results.filter(r => r.avgPing > 0);
  if (allResults.length > 0) {
    const domesticAvg = domesticSites.length > 0 ? 
      Math.round(domesticSites.reduce((sum, r) => sum + r.avgPing, 0) / domesticSites.length) : 0;
    const foreignAvg = foreignSites.length > 0 ? 
      Math.round(foreignSites.reduce((sum, r) => sum + r.avgPing, 0) / foreignSites.length) : 0;
    
    console.log(`- 国内网站平均延迟: ${domesticAvg}ms`);
    console.log(`- 国外网站平均延迟: ${foreignAvg}ms`);
    console.log(`- 延迟差异: ${foreignAvg - domesticAvg}ms`);
    
    if (foreignAvg > domesticAvg * 2) {
      console.log(`✅ 延迟差异合理，数据质量良好`);
    } else {
      console.log(`⚠️ 延迟差异较小，可能需要调整`);
    }
  }
  
  console.log(`\n✅ 测试完成！共测试${results.length}个网站`);
}

testAllWebsites();
