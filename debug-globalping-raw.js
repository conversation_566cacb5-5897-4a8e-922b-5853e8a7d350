// 调试Globalping API的原始响应数据
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 15000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data)),
          text: () => Promise.resolve(data)
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function debugGlobalpingRaw() {
  console.log('🔍 调试Globalping API原始数据...');
  
  try {
    // 1. 直接调用Globalping API
    console.log('\n📡 直接调用Globalping API...');
    
    // 创建测试请求
    const createResponse = await fetch('https://api.globalping.io/v1/measurements', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.GLOBALPING_API_TOKEN || 'k5k76pvk6lj7omghnfqlk4naib5kbwcd'}`,
        'User-Agent': 'PingTool/1.0'
      },
      body: JSON.stringify({
        type: 'ping',
        target: 'google.com',
        locations: [
          { magic: 'China' },
          { magic: 'Hong Kong' },
          { magic: 'Taiwan' },
          { magic: 'Singapore' },
          { magic: 'Japan' },
          { magic: 'South Korea' },
          { magic: 'United States' },
          { magic: 'Europe' },
          { magic: 'Australia' }
        ],
        measurementOptions: {
          packets: 4
        }
      }),
      timeout: 10000
    });
    
    if (!createResponse.ok) {
      throw new Error(`Globalping API错误: ${createResponse.status}`);
    }
    
    const createData = await createResponse.json();
    const measurementId = createData.id;
    console.log(`✅ 测试创建成功，ID: ${measurementId}`);
    
    // 等待测试完成
    console.log('⏳ 等待测试完成...');
    await new Promise(resolve => setTimeout(resolve, 8000));
    
    // 获取结果
    const resultResponse = await fetch(`https://api.globalping.io/v1/measurements/${measurementId}`, {
      headers: {
        'Authorization': `Bearer ${process.env.GLOBALPING_API_TOKEN || 'k5k76pvk6lj7omghnfqlk4naib5kbwcd'}`,
        'User-Agent': 'PingTool/1.0'
      },
      timeout: 5000
    });
    
    if (!resultResponse.ok) {
      throw new Error(`获取Globalping结果失败: ${resultResponse.status}`);
    }
    
    const resultData = await resultResponse.json();
    console.log(`📊 获取到 ${resultData.results?.length || 0} 个结果`);
    
    // 详细分析原始数据
    if (resultData.results && resultData.results.length > 0) {
      console.log('\n📋 Globalping原始数据详细分析:');
      
      resultData.results.forEach((result, index) => {
        console.log(`\n${index + 1}. 节点: ${result.probe.city}, ${result.probe.country}`);
        console.log(`   网络: ${result.probe.network}`);
        console.log(`   状态: ${result.result.status}`);
        
        if (result.result.status === 'finished') {
          console.log(`   原始输出:`);
          console.log(`   ${result.result.rawOutput}`);
          
          // 尝试解析ping结果
          const rawOutput = result.result.rawOutput;
          if (rawOutput) {
            // 查找ping时间
            const pingMatches = rawOutput.match(/time[=<](\d+(?:\.\d+)?)\s*ms/gi);
            if (pingMatches) {
              console.log(`   🎯 发现ping时间: ${pingMatches.join(', ')}`);
              
              // 提取数值
              const times = pingMatches.map(match => {
                const timeMatch = match.match(/(\d+(?:\.\d+)?)/);
                return timeMatch ? parseFloat(timeMatch[1]) : null;
              }).filter(t => t !== null);
              
              if (times.length > 0) {
                const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
                console.log(`   ⚡ 计算平均延迟: ${avgTime.toFixed(1)}ms`);
                
                if (avgTime < 10) {
                  console.log(`   ⚠️ 异常低延迟！可能的问题:`);
                  console.log(`      - 测试的是本地缓存`);
                  console.log(`      - CDN边缘节点`);
                  console.log(`      - 网络配置问题`);
                }
              }
            } else {
              console.log(`   ❌ 无法解析ping时间`);
            }
            
            // 检查是否有错误信息
            if (rawOutput.includes('timeout') || rawOutput.includes('unreachable')) {
              console.log(`   ⚠️ 发现网络问题: timeout或unreachable`);
            }
          }
        } else {
          console.log(`   ❌ 测试失败: ${result.result.status}`);
          if (result.result.rawOutput) {
            console.log(`   错误信息: ${result.result.rawOutput}`);
          }
        }
      });
      
      // 统计分析
      const finishedResults = resultData.results.filter(r => r.result.status === 'finished');
      console.log(`\n📊 统计信息:`);
      console.log(`   总节点数: ${resultData.results.length}`);
      console.log(`   成功节点数: ${finishedResults.length}`);
      console.log(`   成功率: ${Math.round((finishedResults.length / resultData.results.length) * 100)}%`);
      
      // 检查是否所有结果都异常
      const suspiciousResults = finishedResults.filter(result => {
        const rawOutput = result.result.rawOutput;
        if (!rawOutput) return false;
        
        const pingMatches = rawOutput.match(/time[=<](\d+(?:\.\d+)?)\s*ms/gi);
        if (!pingMatches) return false;
        
        const times = pingMatches.map(match => {
          const timeMatch = match.match(/(\d+(?:\.\d+)?)/);
          return timeMatch ? parseFloat(timeMatch[1]) : null;
        }).filter(t => t !== null);
        
        if (times.length === 0) return false;
        
        const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
        return avgTime < 10; // 小于10ms认为可疑
      });
      
      if (suspiciousResults.length > 0) {
        console.log(`\n⚠️ 发现 ${suspiciousResults.length} 个可疑的低延迟结果`);
        console.log(`   这可能表明Globalping的测试方法有问题`);
      }
    }
    
  } catch (error) {
    console.error('❌ 调试失败:', error.message);
  }
  
  console.log('\n✅ Globalping原始数据调试完成！');
}

debugGlobalpingRaw();
