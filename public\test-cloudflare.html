<!DOCTYPE html>
<html>
<head>
    <title>测试 Cloudflare Workers API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { padding: 10px 20px; font-size: 16px; }
        #result { margin-top: 20px; padding: 10px; border: 1px solid #ccc; }
        pre { background: #f5f5f5; padding: 10px; overflow: auto; }
    </style>
</head>
<body>
    <h1>测试 Cloudflare Workers API</h1>
    <button onclick="testAPI()">测试 API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '测试中...';
            
            try {
                console.log('🔥 开始测试 Cloudflare Workers API');
                
                const response = await fetch('/api/ping-cloudflare-worker', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ target: 'baidu.com' })
                });
                
                console.log('📡 API 响应状态:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('✅ API 返回数据:', data);
                    
                    resultDiv.innerHTML = `
                        <h3>API 调用成功！</h3>
                        <p><strong>成功:</strong> ${data.success}</p>
                        <p><strong>节点数量:</strong> ${data.results?.length || 0}</p>
                        ${data.results && data.results.length > 0 ? `
                            <p><strong>第一个节点:</strong></p>
                            <ul>
                                <li>城市: ${data.results[0].city}</li>
                                <li>延迟: ${data.results[0].ping}ms</li>
                                <li>状态: ${data.results[0].status}</li>
                                <li>数据源: ${data.results[0].apiSource}</li>
                                <li>数据中心: ${data.results[0].datacenter}</li>
                            </ul>
                        ` : ''}
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    const errorData = await response.json();
                    console.error('❌ API 错误:', errorData);
                    resultDiv.innerHTML = `<h3>API 调用失败</h3><pre>${JSON.stringify(errorData, null, 2)}</pre>`;
                }
            } catch (error) {
                console.error('❌ 请求异常:', error);
                resultDiv.innerHTML = `<h3>请求异常</h3><p>${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
