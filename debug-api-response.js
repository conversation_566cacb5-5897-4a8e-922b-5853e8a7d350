// 调试API响应
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 15000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data)),
          text: () => Promise.resolve(data)
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function debugAPI() {
  console.log('🔍 调试API响应...');
  
  try {
    const response = await fetch('http://localhost:3002/api/enhanced-ping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target: 'google.com' }),
      timeout: 30000
    });
    
    console.log(`📡 响应状态: ${response.status}`);
    
    const text = await response.text();
    console.log(`📄 响应内容: ${text}`);
    
    try {
      const data = JSON.parse(text);
      console.log('📊 解析后的JSON:');
      console.log(JSON.stringify(data, null, 2));
      
      if (data.error) {
        console.log(`❌ API错误: ${data.error}`);
      }
      
      if (data.results) {
        console.log(`📊 结果数量: ${data.results.length}`);
      }
      
    } catch (e) {
      console.log('❌ 无法解析JSON响应');
    }
    
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
}

debugAPI();
