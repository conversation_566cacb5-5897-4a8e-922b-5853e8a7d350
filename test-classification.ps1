# 测试网站分类准确性

$apiUrl = "https://ping.wobshare.us.kg/api/hybrid-ping"

# 国内知名网站 (应该预测 <100ms)
$domesticWebsites = @(
    "baidu.com",
    "taobao.com", 
    "jd.com",
    "qq.com",
    "weibo.com"
)

# 被墙的国外网站 (应该预测 >250ms)
$blockedWebsites = @(
    "google.com",
    "facebook.com",
    "twitter.com",
    "instagram.com",
    "reddit.com"
)

Write-Host "开始测试网站分类准确性..." -ForegroundColor Green
Write-Host "API地址: $apiUrl" -ForegroundColor Cyan
Write-Host "测试时间: $(Get-Date)" -ForegroundColor Cyan
Write-Host ("=" * 60) -ForegroundColor Yellow

$results = @()

# 测试国内网站
Write-Host "`n=== 测试国内知名网站 (预期<100ms) ===" -ForegroundColor Green
foreach ($website in $domesticWebsites) {
    Write-Host "`n测试: $website" -ForegroundColor Yellow
    
    try {
        $body = @{target = $website} | ConvertTo-Json
        $response = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -Body $body -TimeoutSec 30
        
        $latency = $response.averageLatency
        $method = $response.testMethod
        $category = $response.category
        $confidence = $response.confidence
        
        $status = if ($latency -lt 100) { "合格" } else { "不合格" }
        
        Write-Host "  延迟: ${latency}ms | 方法: $method | 类别: $category | 置信度: $confidence% | $status" -ForegroundColor White
        
        $results += @{
            website = $website
            type = "domestic"
            latency = $latency
            method = $method
            category = $category
            confidence = $confidence
            expected = "<100ms"
            actual = "${latency}ms"
            pass = ($latency -lt 100)
        }
        
    } catch {
        Write-Host "  错误: $($_.Exception.Message)" -ForegroundColor Red
        $results += @{
            website = $website
            type = "domestic"
            latency = 0
            method = "error"
            category = "error"
            confidence = 0
            expected = "<100ms"
            actual = "错误"
            pass = $false
        }
    }
    
    Start-Sleep -Seconds 1
}

# 测试被墙网站
Write-Host "`n=== 测试被墙国外网站 (预期>250ms) ===" -ForegroundColor Red
foreach ($website in $blockedWebsites) {
    Write-Host "`n测试: $website" -ForegroundColor Yellow
    
    try {
        $body = @{target = $website} | ConvertTo-Json
        $response = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -Body $body -TimeoutSec 30
        
        $latency = $response.averageLatency
        $method = $response.testMethod
        $category = $response.category
        $confidence = $response.confidence
        
        $status = if ($latency -gt 250) { "合格" } else { "不合格" }
        
        Write-Host "  延迟: ${latency}ms | 方法: $method | 类别: $category | 置信度: $confidence% | $status" -ForegroundColor White
        
        $results += @{
            website = $website
            type = "blocked"
            latency = $latency
            method = $method
            category = $category
            confidence = $confidence
            expected = ">250ms"
            actual = "${latency}ms"
            pass = ($latency -gt 250)
        }
        
    } catch {
        Write-Host "  错误: $($_.Exception.Message)" -ForegroundColor Red
        $results += @{
            website = $website
            type = "blocked"
            latency = 0
            method = "error"
            category = "error"
            confidence = 0
            expected = ">250ms"
            actual = "错误"
            pass = $false
        }
    }
    
    Start-Sleep -Seconds 1
}

# 生成汇总报告
Write-Host "`n`n=== 测试结果汇总 ===" -ForegroundColor Green
Write-Host ("=" * 60) -ForegroundColor Yellow

$domesticResults = $results | Where-Object { $_.type -eq "domestic" }
$blockedResults = $results | Where-Object { $_.type -eq "blocked" }

$domesticPass = ($domesticResults | Where-Object { $_.pass -eq $true }).Count
$blockedPass = ($blockedResults | Where-Object { $_.pass -eq $true }).Count

Write-Host "国内网站测试: $domesticPass/$($domesticResults.Count) 合格" -ForegroundColor $(if ($domesticPass -eq $domesticResults.Count) { "Green" } else { "Red" })
Write-Host "被墙网站测试: $blockedPass/$($blockedResults.Count) 合格" -ForegroundColor $(if ($blockedPass -eq $blockedResults.Count) { "Green" } else { "Red" })

$totalPass = $domesticPass + $blockedPass
$totalTests = $domesticResults.Count + $blockedResults.Count
$passRate = [math]::Round(($totalPass / $totalTests) * 100, 1)

Write-Host "总体通过率: $totalPass/$totalTests ($passRate%)" -ForegroundColor $(if ($passRate -ge 80) { "Green" } else { "Red" })

Write-Host "`n详细结果:" -ForegroundColor Magenta
foreach ($result in $results) {
    $statusColor = if ($result.pass) { "Green" } else { "Red" }
    $statusText = if ($result.pass) { "PASS" } else { "FAIL" }
    Write-Host "  $($result.website): $($result.actual) (预期$($result.expected)) $statusText" -ForegroundColor $statusColor
}

Write-Host "`n测试完成时间: $(Get-Date)" -ForegroundColor Cyan
