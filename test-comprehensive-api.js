// 测试全平台API调用脚本
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testComprehensiveAPI() {
  console.log('🌐 开始测试全平台API调用...\n');

  const testUrls = [
    'https://www.baidu.com/',
    'https://www.google.com/'
  ];

  for (const url of testUrls) {
    console.log(`📊 测试网站: ${url}`);
    
    try {
      const startTime = Date.now();
      
      const response = await fetch('http://localhost:3001/api/ping-comprehensive', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          target: url
        }),
      });

      const data = await response.json();
      const duration = Date.now() - startTime;
      
      console.log(`✅ 请求成功: ${data.success}`);
      console.log(`⏱️ 总耗时: ${duration}ms`);
      console.log(`📈 总平台数: ${data.totalPlatforms || 0}`);
      console.log(`🎯 成功平台数: ${data.successfulPlatforms || 0}`);
      console.log(`📍 总节点数: ${data.totalNodes || 0}`);
      
      if (data.apiBreakdown) {
        console.log('\n🔍 各平台详细结果:');
        Object.entries(data.apiBreakdown).forEach(([apiName, results]) => {
          const resultArray = Array.isArray(results) ? results : [];
          const status = resultArray.length > 0 ? '✅ 成功' : '❌ 失败';
          const nodeCount = resultArray.length;
          
          if (nodeCount > 0) {
            const avgPing = Math.round(
              resultArray.filter(r => r.ping > 0).reduce((sum, r) => sum + r.ping, 0) / 
              resultArray.filter(r => r.ping > 0).length
            );
            console.log(`  📡 ${apiName}: ${status} | ${nodeCount}个节点 | 平均${avgPing}ms`);
            
            // 显示前3个节点
            resultArray.slice(0, 3).forEach((node, index) => {
              console.log(`    ${index + 1}. ${node.node} (${node.province}): ${node.ping}ms`);
            });
          } else {
            console.log(`  📡 ${apiName}: ${status} | 无数据`);
          }
        });
      }
      
      if (data.error) {
        console.log(`❌ 错误: ${data.error}`);
      }
      
    } catch (error) {
      console.log(`❌ 请求失败: ${error.message}`);
    }
    
    console.log('---\n');
  }
}

// 运行测试
testComprehensiveAPI().catch(console.error);
