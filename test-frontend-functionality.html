<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🧪 前端功能测试</h1>
    
    <div class="test-section">
        <h2>🎯 混合策略API测试</h2>
        <input type="text" id="testUrl" placeholder="输入域名，如：qq.com" value="qq.com">
        <button onclick="testHybridAPI()">测试混合策略</button>
        <div id="hybridResult"></div>
    </div>

    <div class="test-section">
        <h2>🌐 预设网站测试</h2>
        <button onclick="testWebsite('qq.com')">测试 qq.com</button>
        <button onclick="testWebsite('baidu.com')">测试 baidu.com</button>
        <button onclick="testWebsite('google.com')">测试 google.com</button>
        <button onclick="testWebsite('facebook.com')">测试 facebook.com</button>
        <div id="presetResult"></div>
    </div>

    <div class="test-section">
        <h2>📊 批量测试</h2>
        <button onclick="runBatchTest()">运行批量测试</button>
        <div id="batchResult"></div>
    </div>

    <script>
        async function testHybridAPI() {
            const url = document.getElementById('testUrl').value;
            const resultDiv = document.getElementById('hybridResult');
            
            if (!url.trim()) {
                resultDiv.innerHTML = '<div class="result error">请输入有效的域名</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="result">测试中...</div>';
            
            try {
                const response = await fetch('https://ping.wobshare.us.kg/api/hybrid-ping', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ target: url }),
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ 测试成功: ${data.target}</h3>
                            <p><strong>测试方法:</strong> ${data.metadata.testMethod}</p>
                            <p><strong>网站类别:</strong> ${data.metadata.category}</p>
                            <p><strong>平均延迟:</strong> ${data.metadata.averageLatency}ms</p>
                            <p><strong>置信度:</strong> ${(data.metadata.confidence * 100).toFixed(1)}%</p>
                            <p><strong>节点数量:</strong> ${data.metadata.totalNodes}</p>
                            <p><strong>Vercel区域:</strong> ${data.metadata.vercelRegion}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ 测试失败: ${data.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        async function testWebsite(website) {
            document.getElementById('testUrl').value = website;
            const resultDiv = document.getElementById('presetResult');
            resultDiv.innerHTML = `<div class="result">正在测试 ${website}...</div>`;
            
            try {
                const response = await fetch('https://ping.wobshare.us.kg/api/hybrid-ping', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ target: website }),
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const categoryColor = data.metadata.category.includes('blocked') ? '#dc3545' : 
                                        data.metadata.category.includes('domestic') ? '#28a745' : '#007bff';
                    
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ ${website}</h3>
                            <p><strong>延迟:</strong> ${data.metadata.averageLatency}ms</p>
                            <p><strong>类别:</strong> <span style="color: ${categoryColor}">${data.metadata.category}</span></p>
                            <p><strong>方法:</strong> ${data.metadata.testMethod}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ ${website} 测试失败</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ ${website} 网络错误</div>`;
            }
        }
        
        async function runBatchTest() {
            const websites = ['qq.com', 'baidu.com', 'google.com', 'facebook.com', 'twitter.com'];
            const resultDiv = document.getElementById('batchResult');
            resultDiv.innerHTML = '<div class="result">开始批量测试...</div>';
            
            let results = [];
            
            for (const website of websites) {
                try {
                    const response = await fetch('https://ping.wobshare.us.kg/api/hybrid-ping', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ target: website }),
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        results.push({
                            website,
                            latency: data.metadata.averageLatency,
                            category: data.metadata.category,
                            method: data.metadata.testMethod,
                            success: true
                        });
                    } else {
                        results.push({
                            website,
                            success: false,
                            error: data.error
                        });
                    }
                } catch (error) {
                    results.push({
                        website,
                        success: false,
                        error: error.message
                    });
                }
                
                // 更新进度
                resultDiv.innerHTML = `<div class="result">已测试 ${results.length}/${websites.length} 个网站...</div>`;
                
                // 避免请求过快
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            // 显示最终结果
            let html = '<div class="result success"><h3>📊 批量测试完成</h3>';
            results.forEach(result => {
                if (result.success) {
                    const categoryColor = result.category.includes('blocked') ? '#dc3545' : 
                                        result.category.includes('domestic') ? '#28a745' : '#007bff';
                    html += `<p><strong>${result.website}:</strong> ${result.latency}ms <span style="color: ${categoryColor}">(${result.category})</span></p>`;
                } else {
                    html += `<p><strong>${result.website}:</strong> <span style="color: #dc3545">失败 - ${result.error}</span></p>`;
                }
            });
            html += '</div>';
            
            resultDiv.innerHTML = html;
        }
    </script>
</body>
</html>
