// 测试地图数据显示
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {}
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testMapData() {
  console.log('🗺️ 测试地图数据显示...');
  
  try {
    // 测试百度（国内网站）
    console.log('\n📡 测试百度网站...');
    const baiduResponse = await fetch('http://localhost:3002/api/enhanced-ping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target: 'baidu.com', maxNodes: 30 })
    });
    
    const baiduData = await baiduResponse.json();
    console.log('百度结果数量:', baiduData.results?.length || 0);
    
    if (baiduData.results && baiduData.results.length > 0) {
      console.log('\n📊 百度 - 省份覆盖统计:');
      const provinceCount = {};
      baiduData.results.forEach(result => {
        const province = result.province || result.location?.province || '未知';
        provinceCount[province] = (provinceCount[province] || 0) + 1;
      });
      
      console.log(`总计覆盖 ${Object.keys(provinceCount).length} 个省份/地区`);
      Object.entries(provinceCount)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
        .forEach(([province, count]) => {
          console.log(`- ${province}: ${count}个节点`);
        });
    }
    
    // 测试Google（国外网站）
    console.log('\n📡 测试Google网站...');
    const googleResponse = await fetch('http://localhost:3002/api/enhanced-ping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target: 'google.com', maxNodes: 30 })
    });
    
    const googleData = await googleResponse.json();
    console.log('Google结果数量:', googleData.results?.length || 0);
    
    if (googleData.results && googleData.results.length > 0) {
      console.log('\n📊 Google - 延迟分布:');
      const latencyRanges = {
        '0-50ms': 0,
        '51-100ms': 0,
        '101-200ms': 0,
        '201-300ms': 0,
        '300ms+': 0
      };
      
      googleData.results.forEach(result => {
        const ping = result.ping;
        if (ping <= 50) latencyRanges['0-50ms']++;
        else if (ping <= 100) latencyRanges['51-100ms']++;
        else if (ping <= 200) latencyRanges['101-200ms']++;
        else if (ping <= 300) latencyRanges['201-300ms']++;
        else latencyRanges['300ms+']++;
      });
      
      Object.entries(latencyRanges).forEach(([range, count]) => {
        console.log(`- ${range}: ${count}个节点`);
      });
    }
    
    console.log('\n✅ 地图数据测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testMapData();
