<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强多API测试 - BOCE + Globalping + 17CE</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .content {
            padding: 30px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 5px solid #667eea;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 10px 10px 0;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .status {
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            background: #e9ecef;
            border-left: 4px solid #28a745;
            font-weight: 500;
        }
        .status.loading {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .status.error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        .results {
            margin-top: 20px;
        }
        .api-section {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }
        .api-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .api-content {
            padding: 15px;
        }
        .node-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
        }
        .node-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            border-left: 4px solid #28a745;
            font-size: 14px;
        }
        .node-item.slow {
            border-left-color: #ffc107;
        }
        .node-item.timeout {
            border-left-color: #dc3545;
        }
        .ping-value {
            font-weight: bold;
            font-size: 16px;
        }
        .ping-good { color: #28a745; }
        .ping-ok { color: #17a2b8; }
        .ping-fair { color: #ffc107; }
        .ping-slow { color: #fd7e14; }
        .ping-bad { color: #dc3545; }
        .summary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .summary h3 {
            margin: 0 0 10px 0;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-item {
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 6px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }
        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 增强多API并发测试</h1>
            <p>BOCE + Globalping + 17CE 智能组合</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h2>🎯 测试目标</h2>
                <p>测试我们的增强多API系统，验证BOCE、Globalping和17CE的并发调用效果。</p>
                
                <button class="test-button" onclick="testWebsite('https://www.baidu.com/')">测试百度 (国内网站)</button>
                <button class="test-button" onclick="testWebsite('https://www.google.com/')">测试Google (被墙网站)</button>
                <button class="test-button" onclick="testWebsite('https://www.github.com/')">测试GitHub (国外网站)</button>
                <button class="test-button" onclick="testWebsite('https://www.qq.com/')">测试QQ (国内网站)</button>
                
                <div id="status" class="status">准备测试增强多API系统...</div>
            </div>

            <div id="results"></div>
        </div>
    </div>

    <script>
        let isTestingInProgress = false;

        async function testWebsite(url) {
            if (isTestingInProgress) {
                alert('测试正在进行中，请稍候...');
                return;
            }

            isTestingInProgress = true;
            const statusEl = document.getElementById('status');
            const resultsEl = document.getElementById('results');
            const buttons = document.querySelectorAll('.test-button');
            
            // 禁用所有按钮
            buttons.forEach(btn => btn.disabled = true);
            
            statusEl.className = 'status loading';
            statusEl.innerHTML = `🚀 正在测试 ${url}...<br>调用 BOCE + Globalping + 17CE 并发API`;
            resultsEl.innerHTML = '';
            
            try {
                const response = await fetch('/api/itdog-proxy', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ target: url })
                });
                
                const data = await response.json();
                
                if (data.success && data.results) {
                    displayResults(url, data.results);
                    statusEl.className = 'status';
                    statusEl.innerHTML = `✅ 测试完成！共获得 ${data.results.length} 个节点数据`;
                } else {
                    statusEl.className = 'status error';
                    statusEl.innerHTML = `❌ 测试失败: ${data.error || '未知错误'}`;
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.innerHTML = `❌ 网络错误: ${error.message}`;
            } finally {
                isTestingInProgress = false;
                // 重新启用所有按钮
                buttons.forEach(btn => btn.disabled = false);
            }
        }
        
        function displayResults(url, results) {
            const resultsEl = document.getElementById('results');
            
            // 按API来源分组
            const groupedResults = {};
            results.forEach(result => {
                const source = result.apiSource || '未知来源';
                if (!groupedResults[source]) {
                    groupedResults[source] = [];
                }
                groupedResults[source].push(result);
            });
            
            // 计算统计信息
            const totalNodes = results.length;
            const avgPing = Math.round(results.reduce((sum, r) => sum + r.ping, 0) / totalNodes);
            const minPing = Math.min(...results.map(r => r.ping));
            const maxPing = Math.max(...results.map(r => r.ping));
            const successRate = Math.round((results.filter(r => r.status === 'success').length / totalNodes) * 100);
            
            let html = `
                <div class="summary">
                    <h3>📊 ${url} 测试结果汇总</h3>
                    <div class="summary-stats">
                        <div class="stat-item">
                            <span class="stat-value">${totalNodes}</span>
                            <span class="stat-label">总节点数</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${avgPing}ms</span>
                            <span class="stat-label">平均延迟</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${minPing}ms</span>
                            <span class="stat-label">最低延迟</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${maxPing}ms</span>
                            <span class="stat-label">最高延迟</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">${successRate}%</span>
                            <span class="stat-label">成功率</span>
                        </div>
                    </div>
                </div>
            `;
            
            // 显示各API来源的结果
            Object.keys(groupedResults).forEach(source => {
                const sourceResults = groupedResults[source];
                sourceResults.sort((a, b) => a.ping - b.ping);
                
                html += `
                    <div class="api-section">
                        <div class="api-header">
                            <span>${getSourceIcon(source)} ${source}</span>
                            <span>${sourceResults.length} 个节点</span>
                        </div>
                        <div class="api-content">
                            <div class="node-grid">
                                ${sourceResults.map(node => `
                                    <div class="node-item ${getPingClass(node.ping)}">
                                        <div style="font-weight: bold;">${node.node || node.province}</div>
                                        <div class="ping-value ${getPingColorClass(node.ping)}">${node.ping}ms</div>
                                        <div style="font-size: 12px; color: #666; margin-top: 5px;">
                                            ${node.location?.province || node.province || ''}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;
            });
            
            resultsEl.innerHTML = html;
        }
        
        function getSourceIcon(source) {
            const icons = {
                'BOCE': '🎯',
                'Globalping': '🌐', 
                '17CE': '🔧',
                'ITDOG': '🐕',
                '智能合成': '🧠'
            };
            return icons[source] || '📡';
        }
        
        function getPingClass(ping) {
            if (ping >= 500) return 'timeout';
            if (ping >= 200) return 'slow';
            return '';
        }
        
        function getPingColorClass(ping) {
            if (ping <= 50) return 'ping-good';
            if (ping <= 100) return 'ping-ok';
            if (ping <= 200) return 'ping-fair';
            if (ping <= 300) return 'ping-slow';
            return 'ping-bad';
        }
    </script>
</body>
</html>
