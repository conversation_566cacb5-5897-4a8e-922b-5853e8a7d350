// 输入验证工具类
export interface ValidationResult {
  isValid: boolean;
  error?: string;
  sanitized?: string;
}

export class ValidationUtils {
  // 私有IP地址范围
  private static readonly PRIVATE_IP_RANGES = [
    /^10\./,
    /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
    /^192\.168\./,
    /^127\./,
    /^169\.254\./,
    /^::1$/,
    /^fc00:/,
    /^fe80:/
  ];

  // 验证URL
  static validateURL(input: string): ValidationResult {
    if (!input || typeof input !== 'string') {
      return { isValid: false, error: '请输入有效的URL' };
    }

    // 清理输入
    const sanitized = input.trim().toLowerCase();
    
    // 检查长度
    if (sanitized.length > 2048) {
      return { isValid: false, error: 'URL长度不能超过2048个字符' };
    }

    // 如果没有协议，自动添加https://
    let urlToValidate = sanitized;
    if (!urlToValidate.startsWith('http://') && !urlToValidate.startsWith('https://')) {
      urlToValidate = `https://${urlToValidate}`;
    }

    try {
      const parsed = new URL(urlToValidate);
      
      // 检查协议
      if (!['http:', 'https:'].includes(parsed.protocol)) {
        return { isValid: false, error: '只支持HTTP和HTTPS协议' };
      }
      
      // 检查主机名
      if (!parsed.hostname) {
        return { isValid: false, error: '无效的主机名' };
      }

      // 检查是否为IP地址
      if (this.isIPAddress(parsed.hostname)) {
        // 检查是否为私有IP
        if (this.isPrivateIP(parsed.hostname)) {
          return { isValid: false, error: '不允许测试内网地址' };
        }
      } else {
        // 检查域名格式
        if (!this.isValidDomain(parsed.hostname)) {
          return { isValid: false, error: '无效的域名格式' };
        }
      }

      // 检查端口范围
      if (parsed.port && (parseInt(parsed.port) < 1 || parseInt(parsed.port) > 65535)) {
        return { isValid: false, error: '端口号必须在1-65535范围内' };
      }

      return { isValid: true, sanitized: urlToValidate };
    } catch (error) {
      return { isValid: false, error: '无效的URL格式' };
    }
  }

  // 检查是否为IP地址
  private static isIPAddress(hostname: string): boolean {
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
    const ipv6Regex = /^([0-9a-f]{0,4}:){2,7}[0-9a-f]{0,4}$/i;
    return ipv4Regex.test(hostname) || ipv6Regex.test(hostname);
  }

  // 检查是否为私有IP
  private static isPrivateIP(ip: string): boolean {
    return this.PRIVATE_IP_RANGES.some(range => range.test(ip));
  }

  // 检查域名格式
  private static isValidDomain(domain: string): boolean {
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    return domainRegex.test(domain) && domain.length <= 253;
  }

  // 验证测试参数
  static validateTestParams(params: {
    maxNodes?: number;
    timeout?: number;
    retryAttempts?: number;
  }): ValidationResult {
    const { maxNodes = 15, timeout = 5000, retryAttempts = 2 } = params;

    if (maxNodes < 1 || maxNodes > 50) {
      return { isValid: false, error: '节点数量必须在1-50之间' };
    }

    if (timeout < 1000 || timeout > 30000) {
      return { isValid: false, error: '超时时间必须在1-30秒之间' };
    }

    if (retryAttempts < 0 || retryAttempts > 5) {
      return { isValid: false, error: '重试次数必须在0-5次之间' };
    }

    return { isValid: true };
  }

  // 清理和转义字符串
  static sanitizeString(input: string): string {
    if (!input || typeof input !== 'string') return '';
    
    return input
      .trim()
      .replace(/[<>\"'&]/g, (char) => {
        const entities: Record<string, string> = {
          '<': '&lt;',
          '>': '&gt;',
          '"': '&quot;',
          "'": '&#x27;',
          '&': '&amp;'
        };
        return entities[char] || char;
      });
  }

  // 验证JSON数据
  static validateJSON(input: string): ValidationResult {
    try {
      const parsed = JSON.parse(input);
      return { isValid: true, sanitized: JSON.stringify(parsed) };
    } catch (error) {
      return { isValid: false, error: '无效的JSON格式' };
    }
  }
}

// 速率限制工具
export class RateLimiter {
  private static instances = new Map<string, RateLimiter>();
  private requests = new Map<string, { count: number; resetTime: number }>();
  
  constructor(
    private maxRequests: number = 10,
    private windowMs: number = 60000 // 1分钟
  ) {}

  static getInstance(key: string, maxRequests = 10, windowMs = 60000): RateLimiter {
    if (!this.instances.has(key)) {
      this.instances.set(key, new RateLimiter(maxRequests, windowMs));
    }
    return this.instances.get(key)!;
  }

  checkLimit(identifier: string): { allowed: boolean; remaining: number; resetTime: number } {
    const now = Date.now();
    const record = this.requests.get(identifier);

    if (!record || now > record.resetTime) {
      // 新窗口或过期，重置计数
      this.requests.set(identifier, { count: 1, resetTime: now + this.windowMs });
      return { allowed: true, remaining: this.maxRequests - 1, resetTime: now + this.windowMs };
    }

    if (record.count >= this.maxRequests) {
      // 超出限制
      return { allowed: false, remaining: 0, resetTime: record.resetTime };
    }

    // 增加计数
    record.count++;
    return { 
      allowed: true, 
      remaining: this.maxRequests - record.count, 
      resetTime: record.resetTime 
    };
  }

  // 清理过期记录
  cleanup(): void {
    const now = Date.now();
    for (const [key, record] of this.requests.entries()) {
      if (now > record.resetTime) {
        this.requests.delete(key);
      }
    }
  }
}

export default ValidationUtils;
