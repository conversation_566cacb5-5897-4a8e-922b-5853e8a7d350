// 测试修复后的API（移除Globalping）
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 15000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testFixedAPI() {
  console.log('🔍 测试修复后的API（已移除Globalping）...');
  
  try {
    // 测试Google.com
    console.log('\n📡 测试Google.com...');
    const start = Date.now();
    
    const response = await fetch('http://localhost:3002/api/enhanced-ping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target: 'google.com', maxNodes: 50 }),
      timeout: 30000
    });
    
    const end = Date.now();
    const data = await response.json();
    
    console.log(`✅ API响应时间: ${end - start}ms`);
    console.log(`📊 总节点数: ${data.results?.length || 0}`);
    
    if (data.results && data.results.length > 0) {
      // 按API来源分组
      const apiGroups = {};
      data.results.forEach(result => {
        const source = result.apiSource || '未知';
        if (!apiGroups[source]) {
          apiGroups[source] = [];
        }
        apiGroups[source].push(result);
      });
      
      console.log('\n📊 Google.com - 修复后API来源分析:');
      Object.entries(apiGroups).forEach(([source, results]) => {
        const successResults = results.filter(r => r.status === 'success');
        if (successResults.length > 0) {
          const pings = successResults.map(r => r.ping);
          const avgPing = Math.round(pings.reduce((a, b) => a + b, 0) / pings.length);
          const minPing = Math.min(...pings);
          const maxPing = Math.max(...pings);
          
          console.log(`\n${source} (${results.length}个节点):`);
          console.log(`  ⚡ 延迟范围: ${minPing}ms - ${maxPing}ms (平均${avgPing}ms)`);
          console.log(`  📈 成功率: ${successResults.length}/${results.length}`);
          
          // 显示前5个结果
          console.log('  📋 详细结果:');
          results.slice(0, 5).forEach((result, index) => {
            const status = result.status === 'success' ? '✅' : '❌';
            console.log(`    ${index + 1}. ${status} ${result.node} - ${result.province} - ${result.ping}ms`);
          });
          
          // 检查是否还有异常低延迟
          const lowLatency = successResults.filter(r => r.ping < 50);
          if (lowLatency.length > 0) {
            console.log(`  ⚠️ 仍有${lowLatency.length}个低延迟节点 (<50ms):`);
            lowLatency.forEach(result => {
              console.log(`    - ${result.node}: ${result.ping}ms (${result.apiSource})`);
            });
          } else {
            console.log(`  ✅ 延迟数据正常，无异常低延迟`);
          }
        }
      });
      
      // 检查是否还有Globalping数据
      const globalpingResults = data.results.filter(r => 
        r.apiSource === 'Globalping' || 
        r.apiSource === 'Globalping.io' ||
        (r.testMethod && r.testMethod.includes('Globalping'))
      );
      
      if (globalpingResults.length > 0) {
        console.log(`\n⚠️ 警告：仍然发现${globalpingResults.length}个Globalping结果！`);
        globalpingResults.forEach(result => {
          console.log(`  - ${result.node}: ${result.ping}ms (${result.apiSource})`);
        });
      } else {
        console.log(`\n✅ 确认：已成功移除所有Globalping数据`);
      }
      
      // 分析整体数据质量
      const allSuccess = data.results.filter(r => r.status === 'success');
      if (allSuccess.length > 0) {
        const allPings = allSuccess.map(r => r.ping);
        const overallAvg = Math.round(allPings.reduce((a, b) => a + b, 0) / allPings.length);
        const suspiciousLow = allSuccess.filter(r => r.ping < 30).length;
        
        console.log(`\n📊 整体数据质量分析:`);
        console.log(`  总平均延迟: ${overallAvg}ms`);
        console.log(`  可疑低延迟节点: ${suspiciousLow}个`);
        
        if (suspiciousLow === 0) {
          console.log(`  ✅ 数据质量优秀！无异常低延迟`);
        } else if (suspiciousLow < 3) {
          console.log(`  ⚠️ 数据质量良好，少量异常`);
        } else {
          console.log(`  ❌ 数据质量仍有问题，需要进一步调查`);
        }
      }
    }
    
    // 对比测试Facebook
    console.log('\n🔄 对比测试Facebook.com...');
    try {
      const fbResponse = await fetch('http://localhost:3002/api/enhanced-ping', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ target: 'facebook.com', maxNodes: 20 }),
        timeout: 30000
      });
      
      const fbData = await fbResponse.json();
      if (fbData.results && fbData.results.length > 0) {
        const fbSuccess = fbData.results.filter(r => r.status === 'success');
        if (fbSuccess.length > 0) {
          const fbAvg = Math.round(fbSuccess.reduce((sum, r) => sum + r.ping, 0) / fbSuccess.length);
          const fbLow = fbSuccess.filter(r => r.ping < 30).length;
          
          console.log(`📊 Facebook平均延迟: ${fbAvg}ms`);
          console.log(`⚠️ Facebook低延迟节点: ${fbLow}个`);
          
          if (fbLow === 0) {
            console.log(`✅ Facebook数据正常`);
          } else {
            console.log(`❌ Facebook仍有异常低延迟`);
          }
        }
      }
    } catch (error) {
      console.log(`❌ Facebook测试失败: ${error.message}`);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
  
  console.log('\n✅ 修复后API测试完成！');
}

testFixedAPI();
