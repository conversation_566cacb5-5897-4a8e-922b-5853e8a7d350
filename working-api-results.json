{"results": {"wobshare.us.kg": {"GlobalPing": {"status": "success", "type": "async", "measurementId": "RBVSHwCQzp12lJb7", "message": "测试已提交，需要异步获取结果"}, "ITDOG-Direct": {"status": "success", "type": "web", "message": "可以通过网页抓取获取数据"}, "Simple-HTTP-Test": {"status": "success", "type": "http", "latency": 490, "httpStatus": 200, "message": "HTTP连接成功，延迟490ms"}, "TCP-Ping": {"status": "success", "type": "tcp", "latency": 58, "message": "TCP连接成功，延迟58ms"}, "DNS-Lookup": {"status": "success", "type": "dns", "latency": 5, "addresses": ["***********"], "message": "DNS解析成功，延迟5ms，IP: ***********"}}, "baidu.com": {"GlobalPing": {"status": "success", "type": "async", "measurementId": "yBEMFDRmKSa2vt2e", "message": "测试已提交，需要异步获取结果"}, "ITDOG-Direct": {"status": "success", "type": "web", "message": "可以通过网页抓取获取数据"}, "Simple-HTTP-Test": {"status": "success", "type": "http", "latency": 1290, "httpStatus": 200, "message": "HTTP连接成功，延迟1290ms"}, "TCP-Ping": {"status": "success", "type": "tcp", "latency": 77, "message": "TCP连接成功，延迟77ms"}, "DNS-Lookup": {"status": "success", "type": "dns", "latency": 5, "addresses": ["**************", "**************"], "message": "DNS解析成功，延迟5ms，IP: **************"}}, "taobao.com": {"GlobalPing": {"status": "success", "type": "async", "measurementId": "v04SEM4U5RCl0Ymb", "message": "测试已提交，需要异步获取结果"}, "ITDOG-Direct": {"status": "success", "type": "web", "message": "可以通过网页抓取获取数据"}, "Simple-HTTP-Test": {"status": "success", "type": "http", "latency": 448, "httpStatus": 200, "message": "HTTP连接成功，延迟448ms"}, "TCP-Ping": {"status": "success", "type": "tcp", "latency": 43, "message": "TCP连接成功，延迟43ms"}, "DNS-Lookup": {"status": "success", "type": "dns", "latency": 61, "addresses": ["************", "************", "*************", "************", "*************", "*************", "*************", "************"], "message": "DNS解析成功，延迟61ms，IP: ************"}}, "qq.com": {"GlobalPing": {"status": "success", "type": "async", "measurementId": "mA7UHCsrunPvOntB", "message": "测试已提交，需要异步获取结果"}, "ITDOG-Direct": {"status": "success", "type": "web", "message": "可以通过网页抓取获取数据"}, "Simple-HTTP-Test": {"status": "success", "type": "http", "latency": 742, "httpStatus": 200, "message": "HTTP连接成功，延迟742ms"}, "TCP-Ping": {"status": "success", "type": "tcp", "latency": 328, "message": "TCP连接成功，延迟328ms"}, "DNS-Lookup": {"status": "success", "type": "dns", "latency": 10, "addresses": ["**************", "**************", "***************"], "message": "DNS解析成功，延迟10ms，IP: **************"}}, "weibo.com": {"GlobalPing": {"status": "success", "type": "async", "measurementId": "ZnyoaISDVKwMPlXE", "message": "测试已提交，需要异步获取结果"}, "ITDOG-Direct": {"status": "success", "type": "web", "message": "可以通过网页抓取获取数据"}, "Simple-HTTP-Test": {"status": "success", "type": "http", "latency": 698, "httpStatus": 200, "message": "HTTP连接成功，延迟698ms"}, "TCP-Ping": {"status": "success", "type": "tcp", "latency": 51, "message": "TCP连接成功，延迟51ms"}, "DNS-Lookup": {"status": "success", "type": "dns", "latency": 24, "addresses": ["*************", "**************", "*************", "**************"], "message": "DNS解析成功，延迟24ms，IP: *************"}}, "zhihu.com": {"GlobalPing": {"status": "success", "type": "async", "measurementId": "7kBeJlh3VGnyLxdy", "message": "测试已提交，需要异步获取结果"}, "ITDOG-Direct": {"status": "success", "type": "web", "message": "可以通过网页抓取获取数据"}, "Simple-HTTP-Test": {"status": "success", "type": "http", "latency": 423, "httpStatus": 405, "message": "HTTP连接成功，延迟423ms"}, "TCP-Ping": {"status": "success", "type": "tcp", "latency": 52, "message": "TCP连接成功，延迟52ms"}, "DNS-Lookup": {"status": "success", "type": "dns", "latency": 4, "addresses": ["**************"], "message": "DNS解析成功，延迟4ms，IP: **************"}}, "bilibili.com": {"GlobalPing": {"status": "success", "type": "async", "measurementId": "5LMIzvGSFN23APzG", "message": "测试已提交，需要异步获取结果"}, "ITDOG-Direct": {"status": "success", "type": "web", "message": "可以通过网页抓取获取数据"}, "Simple-HTTP-Test": {"status": "success", "type": "http", "latency": 247, "httpStatus": 200, "message": "HTTP连接成功，延迟247ms"}, "TCP-Ping": {"status": "success", "type": "tcp", "latency": 46, "message": "TCP连接成功，延迟46ms"}, "DNS-Lookup": {"status": "success", "type": "dns", "latency": 5, "addresses": ["***********", "**************", "************", "*************"], "message": "DNS解析成功，延迟5ms，IP: ***********"}}, "google.com": {"GlobalPing": {"status": "success", "type": "async", "measurementId": "mzIgIqlncrS467k3", "message": "测试已提交，需要异步获取结果"}, "ITDOG-Direct": {"status": "success", "type": "web", "message": "可以通过网页抓取获取数据"}, "Simple-HTTP-Test": {"status": "error", "error": "fetch failed"}, "TCP-Ping": {"status": "timeout", "error": "TCP连接超时"}, "DNS-Lookup": {"status": "success", "type": "dns", "latency": 7, "addresses": ["*************"], "message": "DNS解析成功，延迟7ms，IP: *************"}}, "facebook.com": {"GlobalPing": {"status": "success", "type": "async", "measurementId": "NTtlPYeWqAIEBccr", "message": "测试已提交，需要异步获取结果"}, "ITDOG-Direct": {"status": "success", "type": "web", "message": "可以通过网页抓取获取数据"}, "Simple-HTTP-Test": {"status": "error", "error": "fetch failed"}, "TCP-Ping": {"status": "timeout", "error": "TCP连接超时"}, "DNS-Lookup": {"status": "success", "type": "dns", "latency": 4, "addresses": ["**************"], "message": "DNS解析成功，延迟4ms，IP: **************"}}, "twitter.com": {"GlobalPing": {"status": "success", "type": "async", "measurementId": "rCGZ6H9fEUUZtk3F", "message": "测试已提交，需要异步获取结果"}, "ITDOG-Direct": {"status": "success", "type": "web", "message": "可以通过网页抓取获取数据"}, "Simple-HTTP-Test": {"status": "error", "error": "fetch failed"}, "TCP-Ping": {"status": "timeout", "error": "TCP连接超时"}, "DNS-Lookup": {"status": "success", "type": "dns", "latency": 11, "addresses": ["**************"], "message": "DNS解析成功，延迟11ms，IP: **************"}}, "instagram.com": {"GlobalPing": {"status": "success", "type": "async", "measurementId": "w18GfEv12jgQfjna", "message": "测试已提交，需要异步获取结果"}, "ITDOG-Direct": {"status": "success", "type": "web", "message": "可以通过网页抓取获取数据"}, "Simple-HTTP-Test": {"status": "error", "error": "fetch failed"}, "TCP-Ping": {"status": "timeout", "error": "TCP连接超时"}, "DNS-Lookup": {"status": "success", "type": "dns", "latency": 4, "addresses": ["*************"], "message": "DNS解析成功，延迟4ms，IP: *************"}}, "whatsapp.com": {"GlobalPing": {"status": "success", "type": "async", "measurementId": "GHfRPElEMQAyA88P", "message": "测试已提交，需要异步获取结果"}, "ITDOG-Direct": {"status": "success", "type": "web", "message": "可以通过网页抓取获取数据"}, "Simple-HTTP-Test": {"status": "error", "error": "fetch failed"}, "TCP-Ping": {"status": "timeout", "error": "TCP连接超时"}, "DNS-Lookup": {"status": "success", "type": "dns", "latency": 6, "addresses": ["***********"], "message": "DNS解析成功，延迟6ms，IP: ***********"}}, "telegram.org": {"GlobalPing": {"status": "success", "type": "async", "measurementId": "Cv4jzANDEjjzNCv5", "message": "测试已提交，需要异步获取结果"}, "ITDOG-Direct": {"status": "success", "type": "web", "message": "可以通过网页抓取获取数据"}, "Simple-HTTP-Test": {"status": "error", "error": "fetch failed"}, "TCP-Ping": {"status": "timeout", "error": "TCP连接超时"}, "DNS-Lookup": {"status": "success", "type": "dns", "latency": 5, "addresses": ["***********"], "message": "DNS解析成功，延迟5ms，IP: ***********"}}}, "apiScores": {"GlobalPing": {"totalTests": 13, "successfulTests": 13, "avgLatency": 0, "latencies": [], "errors": [], "description": "国际化ping测试平台，支持全球节点", "successRate": 100}, "ITDOG-Direct": {"totalTests": 13, "successfulTests": 13, "avgLatency": 0, "latencies": [], "errors": [], "description": "ITDOG直接访问测试", "successRate": 100}, "Simple-HTTP-Test": {"totalTests": 13, "successfulTests": 7, "avgLatency": 619.7142857142857, "latencies": [490, 1290, 448, 742, 698, 423, 247], "errors": ["fetch failed", "fetch failed", "fetch failed", "fetch failed", "fetch failed", "fetch failed"], "description": "简单HTTP连接测试", "successRate": 53.84615384615385}, "TCP-Ping": {"totalTests": 13, "successfulTests": 7, "avgLatency": 93.57142857142857, "latencies": [58, 77, 43, 328, 51, 52, 46], "errors": ["TCP连接超时", "TCP连接超时", "TCP连接超时", "TCP连接超时", "TCP连接超时", "TCP连接超时"], "description": "TCP端口连接测试", "successRate": 53.84615384615385}, "DNS-Lookup": {"totalTests": 13, "successfulTests": 13, "avgLatency": 11.615384615384615, "latencies": [5, 5, 61, 10, 24, 4, 5, 7, 4, 11, 4, 6, 5], "errors": [], "description": "DNS解析测试", "successRate": 100}}, "recommendations": {"primary": "DNS-Lookup", "backups": ["TCP-Ping", "Simple-HTTP-Test", "GlobalPing"], "rankings": [{"name": "DNS-Lookup", "totalTests": 13, "successfulTests": 13, "avgLatency": 11.615384615384615, "latencies": [5, 5, 61, 10, 24, 4, 5, 7, 4, 11, 4, 6, 5], "errors": [], "description": "DNS解析测试", "successRate": 100, "overallScore": 99.90707692307693}, {"name": "TCP-Ping", "totalTests": 13, "successfulTests": 7, "avgLatency": 93.57142857142857, "latencies": [58, 77, 43, 328, 51, 52, 46], "errors": ["TCP连接超时", "TCP连接超时", "TCP连接超时", "TCP连接超时", "TCP连接超时", "TCP连接超时"], "description": "TCP端口连接测试", "successRate": 53.84615384615385, "overallScore": 71.55912087912088}, {"name": "Simple-HTTP-Test", "totalTests": 13, "successfulTests": 7, "avgLatency": 619.7142857142857, "latencies": [490, 1290, 448, 742, 698, 423, 247], "errors": ["fetch failed", "fetch failed", "fetch failed", "fetch failed", "fetch failed", "fetch failed"], "description": "简单HTTP连接测试", "successRate": 53.84615384615385, "overallScore": 67.34997802197802}, {"name": "GlobalPing", "totalTests": 13, "successfulTests": 13, "avgLatency": 0, "latencies": [], "errors": [], "description": "国际化ping测试平台，支持全球节点", "successRate": 100, "overallScore": 60}, {"name": "ITDOG-Direct", "totalTests": 13, "successfulTests": 13, "avgLatency": 0, "latencies": [], "errors": [], "description": "ITDOG直接访问测试", "successRate": 100, "overallScore": 60}]}, "timestamp": "2025-07-28T12:58:48.101Z"}