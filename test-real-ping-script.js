// 真实HTTP延迟测试脚本
const testWebsites = [
  'reddit.com',
  'sohu.com', 
  'www.bbc.com',
  'www.linovel.net',
  'www.pinterest.com',
  'www.xygalaxy.com',
  'baidu.com',
  'google.com'
];

async function testRealPing(domain) {
  try {
    console.log(`\n🔍 测试 ${domain}...`);
    
    const response = await fetch('http://localhost:3000/api/test-real-ping', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ target: domain }),
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log(`✅ ${domain}: ${result.realLatency}ms`);
      return { domain, latency: result.realLatency, success: true };
    } else {
      console.log(`❌ ${domain}: 测试失败 - ${result.message}`);
      return { domain, latency: -1, success: false, message: result.message };
    }
    
  } catch (error) {
    console.log(`🚫 ${domain}: 异常 - ${error.message}`);
    return { domain, latency: -1, success: false, error: error.message };
  }
}

async function runAllTests() {
  console.log('🚀 开始真实HTTP延迟测试...\n');
  
  const results = [];
  
  for (const domain of testWebsites) {
    const result = await testRealPing(domain);
    results.push(result);
    
    // 等待1秒避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📊 测试结果汇总:');
  console.log('==========================================');
  
  const successResults = results.filter(r => r.success);
  const failedResults = results.filter(r => !r.success);
  
  console.log('\n✅ 成功测试的网站:');
  successResults.forEach(r => {
    console.log(`   ${r.domain.padEnd(20)} ${r.latency}ms`);
  });
  
  console.log('\n❌ 失败的网站:');
  failedResults.forEach(r => {
    console.log(`   ${r.domain.padEnd(20)} ${r.message || r.error || '未知错误'}`);
  });
  
  console.log(`\n📈 统计信息:`);
  console.log(`   总测试数: ${results.length}`);
  console.log(`   成功数: ${successResults.length}`);
  console.log(`   失败数: ${failedResults.length}`);
  console.log(`   成功率: ${((successResults.length / results.length) * 100).toFixed(1)}%`);
  
  if (successResults.length > 0) {
    const latencies = successResults.map(r => r.latency);
    const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
    const minLatency = Math.min(...latencies);
    const maxLatency = Math.max(...latencies);
    
    console.log(`   平均延迟: ${avgLatency.toFixed(1)}ms`);
    console.log(`   最小延迟: ${minLatency}ms`);
    console.log(`   最大延迟: ${maxLatency}ms`);
  }
}

// 运行测试
runAllTests().catch(console.error);
