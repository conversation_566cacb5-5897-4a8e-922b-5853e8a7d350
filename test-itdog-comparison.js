// 对比我们的结果与ITDOG.cn网站
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {}
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function compareWithITDOG() {
  console.log('🔍 对比我们的结果与ITDOG.cn...');
  
  try {
    // 1. 测试我们的ITDOG API
    console.log('\n📡 测试我们的ITDOG API...');
    const itdogResponse = await fetch('http://localhost:3001/api/itdog-proxy', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target: 'wobshare.us.kg' })
    });
    
    const itdogData = await itdogResponse.json();
    console.log('ITDOG API结果数量:', itdogData.results?.length || 0);
    
    if (itdogData.results && itdogData.results.length > 0) {
      console.log('\n📊 ITDOG API - 前10个结果:');
      itdogData.results.slice(0, 10).forEach((result, index) => {
        console.log(`${index + 1}. ${result.node} - ${result.province} - ${result.ping}ms`);
      });
      
      // 统计省份分布
      const provinces = {};
      itdogData.results.forEach(result => {
        const province = result.province || '未知';
        provinces[province] = (provinces[province] || 0) + 1;
      });
      
      console.log('\n🗺️ ITDOG API - 省份分布:');
      Object.entries(provinces)
        .sort((a, b) => b[1] - a[1])
        .forEach(([province, count]) => {
          console.log(`- ${province}: ${count}个节点`);
        });
    }
    
    // 2. 测试我们的增强API
    console.log('\n📡 测试我们的增强API...');
    const enhancedResponse = await fetch('http://localhost:3001/api/enhanced-ping', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ target: 'wobshare.us.kg', maxNodes: 50 })
    });
    
    const enhancedData = await enhancedResponse.json();
    console.log('增强API结果数量:', enhancedData.results?.length || 0);
    
    if (enhancedData.results && enhancedData.results.length > 0) {
      // 按API来源分组
      const apiSources = {};
      enhancedData.results.forEach(result => {
        const source = result.apiSource || '未知';
        if (!apiSources[source]) {
          apiSources[source] = [];
        }
        apiSources[source].push(result);
      });
      
      console.log('\n📊 增强API - 按来源分组:');
      Object.entries(apiSources).forEach(([source, results]) => {
        console.log(`\n${source} (${results.length}个节点):`);
        results.slice(0, 5).forEach(result => {
          console.log(`  - ${result.node} - ${result.province} - ${result.ping}ms`);
        });
      });
      
      // 找出真实API数据（非智能检测）
      const realData = enhancedData.results.filter(r => r.apiSource !== '智能检测');
      console.log(`\n🎯 真实API数据: ${realData.length}个节点`);
      
      if (realData.length > 0) {
        console.log('真实数据来源统计:');
        const realSources = {};
        realData.forEach(result => {
          const source = result.apiSource || '未知';
          realSources[source] = (realSources[source] || 0) + 1;
        });
        
        Object.entries(realSources).forEach(([source, count]) => {
          console.log(`- ${source}: ${count}个节点`);
        });
      }
    }
    
    // 3. 分析数据质量
    console.log('\n📈 数据质量分析:');
    if (itdogData.results && enhancedData.results) {
      const itdogCount = itdogData.results.length;
      const enhancedCount = enhancedData.results.length;
      const realCount = enhancedData.results.filter(r => r.apiSource !== '智能检测').length;
      
      console.log(`- ITDOG原始数据: ${itdogCount}个节点`);
      console.log(`- 增强API总数据: ${enhancedCount}个节点`);
      console.log(`- 增强API真实数据: ${realCount}个节点`);
      console.log(`- 模拟数据比例: ${Math.round((enhancedCount - realCount) / enhancedCount * 100)}%`);
      
      if (realCount >= itdogCount * 0.8) {
        console.log('✅ 真实数据覆盖率良好');
      } else {
        console.log('⚠️ 真实数据覆盖率偏低，可能需要优化API调用');
      }
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
  
  console.log('\n✅ ITDOG对比测试完成！');
}

compareWithITDOG();
