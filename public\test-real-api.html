<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .info { border-left: 4px solid #17a2b8; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .stat-card {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .latency-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .latency-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #28a745;
        }
        .latency-item.high { border-left-color: #ffc107; }
        .latency-item.very-high { border-left-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 真实API数据测试</h1>
        <p>测试我们的ping系统是否返回真实的低延迟数据</p>

        <div class="test-section">
            <h3>📊 测试目标</h3>
            <input type="text" id="testUrl" value="webshare.us.kg" placeholder="输入测试网站" style="width: 300px; padding: 8px;">
            <button onclick="testRealAPI()">🎯 测试真实API</button>
            <button onclick="testAllAPIs()">🌐 测试所有API</button>
            <button onclick="clearResults()">🗑️ 清空结果</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        async function testRealAPI() {
            const url = document.getElementById('testUrl').value.trim();
            if (!url) {
                alert('请输入测试网站');
                return;
            }

            addResult('info', `🎯 开始测试: ${url}`);
            
            try {
                const response = await fetch('/api/itdog-proxy', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ target: url })
                });

                const data = await response.json();
                
                if (data.success && data.results) {
                    addResult('success', `✅ 成功获取 ${data.results.length} 个节点数据`);
                    
                    // 统计分析
                    const latencies = data.results.map(r => r.ping).filter(p => p > 0);
                    const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
                    const minLatency = Math.min(...latencies);
                    const maxLatency = Math.max(...latencies);
                    const lowLatencyCount = latencies.filter(l => l < 50).length;
                    
                    // 显示统计信息
                    const statsHtml = `
                        <div class="stats">
                            <div class="stat-card">
                                <div class="stat-value">${latencies.length}</div>
                                <div>有效节点</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${avgLatency.toFixed(1)}ms</div>
                                <div>平均延迟</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${minLatency}ms</div>
                                <div>最低延迟</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${maxLatency}ms</div>
                                <div>最高延迟</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value">${lowLatencyCount}</div>
                                <div>低延迟节点(&lt;50ms)</div>
                            </div>
                        </div>
                    `;
                    
                    addResult('info', statsHtml);
                    
                    // 显示详细延迟列表
                    const latencyListHtml = `
                        <div class="latency-list">
                            ${data.results.map(result => {
                                const latencyClass = result.ping < 50 ? '' : result.ping < 100 ? 'high' : 'very-high';
                                return `
                                    <div class="latency-item ${latencyClass}">
                                        <strong>${result.node}</strong><br>
                                        <span style="font-size: 18px; color: #007bff;">${result.ping}ms</span><br>
                                        <small>${result.location?.city || '未知'} - ${result.apiSource}</small>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    `;
                    
                    addResult('success', latencyListHtml);
                    
                    // 判断数据质量
                    if (avgLatency < 100 && lowLatencyCount > 5) {
                        addResult('success', '🎉 数据质量优秀！延迟数据真实可信');
                    } else if (avgLatency > 200) {
                        addResult('error', '⚠️ 数据质量可疑，延迟偏高，可能是模拟数据');
                    } else {
                        addResult('info', '📊 数据质量中等，部分延迟偏高');
                    }
                    
                } else {
                    addResult('error', `❌ API调用失败: ${JSON.stringify(data)}`);
                }
                
            } catch (error) {
                addResult('error', `❌ 请求失败: ${error.message}`);
            }
        }

        async function testAllAPIs() {
            const url = document.getElementById('testUrl').value.trim();
            if (!url) {
                alert('请输入测试网站');
                return;
            }

            addResult('info', `🌐 开始测试所有API: ${url}`);
            
            const apis = [
                { name: 'ITDOG', endpoint: '/api/itdog-proxy' },
                { name: 'BOCE', endpoint: '/api/boce-proxy' },
                { name: '17CE', endpoint: '/api/17ce-proxy' },
                { name: 'Globalping', endpoint: '/api/globalping-proxy' }
            ];

            for (const api of apis) {
                try {
                    addResult('info', `📡 测试 ${api.name} API...`);
                    
                    const response = await fetch(api.endpoint, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ url: url, target: url })
                    });

                    const data = await response.json();
                    
                    if (data.results && data.results.length > 0) {
                        const latencies = data.results.map(r => r.ping).filter(p => p > 0);
                        const avgLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
                        const minLatency = Math.min(...latencies);
                        
                        addResult('success', 
                            `✅ ${api.name}: ${data.results.length}个节点, ` +
                            `平均${avgLatency.toFixed(1)}ms, 最低${minLatency}ms, ` +
                            `来源: ${data.source || 'Unknown'}`
                        );
                    } else {
                        addResult('error', `❌ ${api.name}: 无有效数据`);
                    }
                    
                } catch (error) {
                    addResult('error', `❌ ${api.name}: ${error.message}`);
                }
            }
        }

        function addResult(type, content) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `[${new Date().toLocaleTimeString()}] ${content}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // 页面加载时自动测试
        window.onload = function() {
            addResult('info', '🚀 真实API测试页面已加载');
            addResult('info', '点击"测试真实API"按钮开始测试');
        };
    </script>
</body>
</html>
