'use client';

import React, { useState, useEffect } from 'react';

interface APITestResult {
  platform: string;
  endpoint: string;
  status: 'pending' | 'success' | 'error' | 'timeout';
  responseTime: number;
  dataQuality: number;
  nodeCount: number;
  accuracy: number;
  coverage: string[];
  error?: string;
  timestamp: number;
  sampleData?: any;
}

interface TestConfig {
  testTargets: string[];
  enabledAPIs: string[];
  timeout: number;
  maxConcurrent: number;
}

const APITestDashboard: React.FC<{ isDarkMode: boolean }> = ({ isDarkMode }) => {
  const [testResults, setTestResults] = useState<APITestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [testProgress, setTestProgress] = useState(0);
  const [testConfig, setTestConfig] = useState<TestConfig>({
    testTargets: ['google.com', 'baidu.com', 'wobshare.us.kg', 'github.com'],
    enabledAPIs: [],
    timeout: 10000,
    maxConcurrent: 3
  });

  // 导入平台列表
  const availableAPIs = [
    // 🇨🇳 中国平台
    {
      id: '17ce',
      name: '17CE.COM',
      description: '国内专业网站监测',
      endpoint: 'https://www.17ce.com/site/ping',
      type: 'china',
      free: true,
      rating: 5
    },
    {
      id: 'chinaz',
      name: 'Chinaz站长工具',
      description: '站长工具ping测试',
      endpoint: 'https://ping.chinaz.com',
      type: 'china',
      free: true,
      rating: 4
    },
    {
      id: 'itdog',
      name: 'ITDOG.CN',
      description: 'IT狗网络工具',
      endpoint: 'https://www.itdog.cn/ping',
      type: 'china',
      free: true,
      rating: 4
    },
    {
      id: 'boce',
      name: 'BOCE.COM',
      description: '博测网络测试',
      endpoint: 'https://www.boce.com/ping',
      type: 'china',
      free: true,
      rating: 3
    },
    {
      id: 'alibaba-boce',
      name: '阿里云BOCE',
      description: '阿里云网络测试',
      endpoint: 'https://boce.aliyun.com',
      type: 'china',
      free: true,
      rating: 3
    },
    // 🌍 全球平台
    {
      id: 'globalping',
      name: 'Globalping.io',
      description: '全球分布式ping网络',
      endpoint: 'https://api.globalping.io/v1/measurements',
      type: 'global',
      free: true,
      rating: 4
    },
    {
      id: 'ping-pe',
      name: 'Ping.pe',
      description: '全球ping测试工具',
      endpoint: 'https://ping.pe',
      type: 'global',
      free: true,
      rating: 4
    },
    // ⚡ 边缘计算
    {
      id: 'cloudflare-worker',
      name: 'Cloudflare Workers',
      description: '边缘计算ping测试',
      endpoint: 'https://ping-api.wobys.dpdns.org',
      type: 'edge',
      free: true,
      rating: 4
    },
    {
      id: 'vercel-edge',
      name: 'Vercel Edge Functions',
      description: 'Vercel边缘函数',
      endpoint: '/api/ping-vercel-edge',
      type: 'edge',
      free: true,
      rating: 4
    },
    // 🔄 聚合API
    {
      id: 'multi-platform',
      name: 'Multi-Platform API',
      description: '多平台聚合API',
      endpoint: '/api/ping-multiplatform',
      type: 'aggregated',
      free: true,
      rating: 5
    }
  ];

  useEffect(() => {
    // 默认启用所有免费API
    setTestConfig(prev => ({
      ...prev,
      enabledAPIs: availableAPIs.filter(api => api.free).map(api => api.id)
    }));
  }, []);

  const startBatchTest = async () => {
    setIsRunning(true);
    setTestProgress(0);
    setTestResults([]);

    const enabledAPIs = availableAPIs.filter(api => testConfig.enabledAPIs.includes(api.id));
    const totalTests = enabledAPIs.length * testConfig.testTargets.length;
    let completedTests = 0;

    for (const target of testConfig.testTargets) {
      const testPromises = enabledAPIs.map(async (api) => {
        const result: APITestResult = {
          platform: api.name,
          endpoint: api.endpoint,
          status: 'pending',
          responseTime: 0,
          dataQuality: 0,
          nodeCount: 0,
          accuracy: 0,
          coverage: [],
          timestamp: Date.now()
        };

        try {
          const startTime = Date.now();
          
          // 调用测试API
          const response = await fetch('/api/test-ping-platforms', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              platform: api.id,
              target: target,
              timeout: testConfig.timeout
            }),
            signal: AbortSignal.timeout(testConfig.timeout)
          });

          const endTime = Date.now();
          result.responseTime = endTime - startTime;

          if (response.ok) {
            const data = await response.json();
            result.status = 'success';
            result.nodeCount = data.nodeCount || 0;
            result.dataQuality = data.dataQuality || 0;
            result.accuracy = data.accuracy || 0;
            result.coverage = data.coverage || [];
            result.sampleData = data.sampleData;
          } else {
            result.status = 'error';
            result.error = `HTTP ${response.status}`;
          }
        } catch (error) {
          result.status = error instanceof Error && error.name === 'TimeoutError' ? 'timeout' : 'error';
          result.error = error instanceof Error ? error.message : '未知错误';
        }

        completedTests++;
        setTestProgress((completedTests / totalTests) * 100);
        
        return result;
      });

      // 限制并发数
      const results = await Promise.all(testPromises);
      setTestResults(prev => [...prev, ...results]);
    }

    setIsRunning(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'timeout': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'timeout': return '⏰';
      default: return '⏳';
    }
  };

  const getRatingStars = (rating: number) => {
    return '⭐'.repeat(rating) + '☆'.repeat(5 - rating);
  };

  return (
    <div className={`min-h-screen p-8 transition-colors duration-300 ${
      isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'
    }`}>
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className={`text-4xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            🧪 API测试控制台
          </h1>
          <p className={`text-lg mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            批量测试各种ping平台API，评估性能和准确性，选择最适合的服务商
          </p>

          {/* 平台统计 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-red-900/20 border border-red-800' : 'bg-red-50 border border-red-200'}`}>
              <div className="text-2xl font-bold text-red-600">
                {availableAPIs.filter(api => api.type === 'china').length}
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                🇨🇳 中国平台
              </div>
            </div>
            <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-blue-900/20 border border-blue-800' : 'bg-blue-50 border border-blue-200'}`}>
              <div className="text-2xl font-bold text-blue-600">
                {availableAPIs.filter(api => api.type === 'global').length}
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                🌍 全球平台
              </div>
            </div>
            <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-green-900/20 border border-green-800' : 'bg-green-50 border border-green-200'}`}>
              <div className="text-2xl font-bold text-green-600">
                {availableAPIs.filter(api => api.type === 'edge').length}
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                ⚡ 边缘计算
              </div>
            </div>
            <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-purple-900/20 border border-purple-800' : 'bg-purple-50 border border-purple-200'}`}>
              <div className="text-2xl font-bold text-purple-600">
                {availableAPIs.filter(api => api.type === 'aggregated').length}
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                🔄 聚合API
              </div>
            </div>
          </div>
        </div>

        {/* 测试配置 */}
        <div className={`rounded-lg p-6 mb-8 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
          <h2 className={`text-2xl font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            🔧 测试配置
          </h2>
          
          {/* 测试目标 */}
          <div className="mb-6">
            <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              测试目标网站
            </label>
            <div className="flex flex-wrap gap-2">
              {testConfig.testTargets.map((target, index) => (
                <span key={index} className={`px-3 py-1 rounded-full text-sm ${
                  isDarkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-800'
                }`}>
                  {target}
                </span>
              ))}
            </div>
          </div>

          {/* API选择 */}
          <div className="mb-6">
            <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              选择要测试的API平台
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {availableAPIs.map((api) => (
                <div key={api.id} className={`p-4 rounded-lg border ${
                  isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-gray-50'
                }`}>
                  <div className="flex items-center justify-between mb-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={testConfig.enabledAPIs.includes(api.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setTestConfig(prev => ({
                              ...prev,
                              enabledAPIs: [...prev.enabledAPIs, api.id]
                            }));
                          } else {
                            setTestConfig(prev => ({
                              ...prev,
                              enabledAPIs: prev.enabledAPIs.filter(id => id !== api.id)
                            }));
                          }
                        }}
                        className="mr-2"
                      />
                      <span className="font-medium">{api.name}</span>
                    </label>
                    <span className="text-sm">{getRatingStars(api.rating)}</span>
                  </div>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {api.description}
                  </p>
                  <div className="flex items-center justify-between mt-2">
                    <span className={`text-xs px-2 py-1 rounded ${
                      api.type === 'china' ? 'bg-red-100 text-red-800' :
                      api.type === 'global' ? 'bg-blue-100 text-blue-800' :
                      api.type === 'edge' ? 'bg-green-100 text-green-800' :
                      'bg-purple-100 text-purple-800'
                    }`}>
                      {api.type}
                    </span>
                    <span className={`text-xs ${api.free ? 'text-green-600' : 'text-orange-600'}`}>
                      {api.free ? '免费' : '付费'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 开始测试按钮 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                已选择 {testConfig.enabledAPIs.length} 个API平台
              </span>
              <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                共 {testConfig.enabledAPIs.length * testConfig.testTargets.length} 个测试
              </span>
            </div>
            <button
              onClick={startBatchTest}
              disabled={isRunning || testConfig.enabledAPIs.length === 0}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                isRunning || testConfig.enabledAPIs.length === 0
                  ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isRunning ? '测试中...' : '开始批量测试'}
            </button>
          </div>

          {/* 进度条 */}
          {isRunning && (
            <div className="mt-4">
              <div className={`w-full bg-gray-200 rounded-full h-2 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${testProgress}%` }}
                ></div>
              </div>
              <p className={`text-sm mt-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                测试进度: {Math.round(testProgress)}%
              </p>
            </div>
          )}
        </div>

        {/* 测试结果 */}
        {testResults.length > 0 && (
          <div className={`rounded-lg p-6 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
            <h2 className={`text-2xl font-semibold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              📊 测试结果分析
            </h2>

            {/* 统计概览 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
              <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <div className="text-2xl font-bold text-green-600">
                  {testResults.filter(r => r.status === 'success').length}
                </div>
                <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  成功测试
                </div>
              </div>
              <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <div className="text-2xl font-bold text-red-600">
                  {testResults.filter(r => r.status === 'error').length}
                </div>
                <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  失败测试
                </div>
              </div>
              <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <div className="text-2xl font-bold text-yellow-600">
                  {testResults.filter(r => r.status === 'timeout').length}
                </div>
                <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  超时测试
                </div>
              </div>
              <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <div className="text-2xl font-bold text-blue-600">
                  {Math.round(testResults.reduce((sum, r) => sum + r.responseTime, 0) / testResults.length) || 0}ms
                </div>
                <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  平均响应时间
                </div>
              </div>
            </div>

            {/* 详细结果表格 */}
            <div className="overflow-x-auto">
              <table className={`w-full text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                <thead>
                  <tr className={`border-b ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                    <th className="text-left py-3 px-4">平台</th>
                    <th className="text-left py-3 px-4">状态</th>
                    <th className="text-left py-3 px-4">响应时间</th>
                    <th className="text-left py-3 px-4">节点数量</th>
                    <th className="text-left py-3 px-4">数据质量</th>
                    <th className="text-left py-3 px-4">准确性</th>
                    <th className="text-left py-3 px-4">覆盖范围</th>
                    <th className="text-left py-3 px-4">错误信息</th>
                  </tr>
                </thead>
                <tbody>
                  {testResults.map((result, index) => (
                    <tr key={index} className={`border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-100'} hover:${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                      <td className="py-3 px-4 font-medium">{result.platform}</td>
                      <td className="py-3 px-4">
                        <span className={`flex items-center ${getStatusColor(result.status)}`}>
                          {getStatusIcon(result.status)} {result.status}
                        </span>
                      </td>
                      <td className="py-3 px-4">{result.responseTime}ms</td>
                      <td className="py-3 px-4">{result.nodeCount}</td>
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          <div className={`w-16 h-2 rounded-full mr-2 ${isDarkMode ? 'bg-gray-600' : 'bg-gray-200'}`}>
                            <div
                              className="h-2 rounded-full bg-blue-600"
                              style={{ width: `${result.dataQuality}%` }}
                            ></div>
                          </div>
                          {result.dataQuality}%
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center">
                          <div className={`w-16 h-2 rounded-full mr-2 ${isDarkMode ? 'bg-gray-600' : 'bg-gray-200'}`}>
                            <div
                              className="h-2 rounded-full bg-green-600"
                              style={{ width: `${result.accuracy}%` }}
                            ></div>
                          </div>
                          {result.accuracy}%
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex flex-wrap gap-1">
                          {result.coverage.slice(0, 3).map((region, i) => (
                            <span key={i} className={`text-xs px-2 py-1 rounded ${
                              isDarkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-800'
                            }`}>
                              {region}
                            </span>
                          ))}
                          {result.coverage.length > 3 && (
                            <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                              +{result.coverage.length - 3}
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        {result.error && (
                          <span className="text-red-600 text-xs">{result.error}</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* 推荐分析 */}
            <div className="mt-8">
              <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                🎯 推荐分析
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 最佳性能 */}
                <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-green-900/20 border border-green-800' : 'bg-green-50 border border-green-200'}`}>
                  <h4 className="font-medium text-green-600 mb-2">🚀 最佳性能</h4>
                  {(() => {
                    const successResults = testResults.filter(r => r.status === 'success');
                    const fastest = successResults.sort((a, b) => a.responseTime - b.responseTime)[0];
                    return fastest ? (
                      <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        {fastest.platform} - {fastest.responseTime}ms
                      </p>
                    ) : (
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        暂无成功的测试结果
                      </p>
                    );
                  })()}
                </div>

                {/* 最高准确性 */}
                <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-blue-900/20 border border-blue-800' : 'bg-blue-50 border border-blue-200'}`}>
                  <h4 className="font-medium text-blue-600 mb-2">🎯 最高准确性</h4>
                  {(() => {
                    const successResults = testResults.filter(r => r.status === 'success');
                    const mostAccurate = successResults.sort((a, b) => b.accuracy - a.accuracy)[0];
                    return mostAccurate ? (
                      <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        {mostAccurate.platform} - {mostAccurate.accuracy}%
                      </p>
                    ) : (
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        暂无成功的测试结果
                      </p>
                    );
                  })()}
                </div>

                {/* 最多节点 */}
                <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-purple-900/20 border border-purple-800' : 'bg-purple-50 border border-purple-200'}`}>
                  <h4 className="font-medium text-purple-600 mb-2">🌐 最多节点</h4>
                  {(() => {
                    const successResults = testResults.filter(r => r.status === 'success');
                    const mostNodes = successResults.sort((a, b) => b.nodeCount - a.nodeCount)[0];
                    return mostNodes ? (
                      <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        {mostNodes.platform} - {mostNodes.nodeCount} 个节点
                      </p>
                    ) : (
                      <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        暂无成功的测试结果
                      </p>
                    );
                  })()}
                </div>

                {/* 最佳稳定性 */}
                <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-orange-900/20 border border-orange-800' : 'bg-orange-50 border border-orange-200'}`}>
                  <h4 className="font-medium text-orange-600 mb-2">⚡ 最佳稳定性</h4>
                  {(() => {
                    const successRate = testResults.length > 0 ?
                      (testResults.filter(r => r.status === 'success').length / testResults.length * 100).toFixed(1) : '0';
                    return (
                      <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        整体成功率: {successRate}%
                      </p>
                    );
                  })()}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default APITestDashboard;
