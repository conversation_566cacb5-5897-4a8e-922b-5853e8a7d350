#!/usr/bin/env node

/**
 * 🌐 多云部署测试脚本
 * 验证 Vercel Edge Functions 和 Cloudflare Workers 是否正常工作
 */

const https = require('https');
const http = require('http');

// 测试配置
const TEST_CONFIG = {
  // 测试目标网站
  testTargets: [
    'https://baidu.com',
    'https://google.com',
    'https://github.com'
  ],
  
  // 云服务端点
  services: {
    vercel: {
      name: 'Vercel Edge Functions',
      // 本地测试时使用 localhost，部署后使用实际域名
      baseUrl: process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` : 'http://localhost:3000',
      endpoint: '/api/ping-vercel-edge'
    },
    cloudflare: {
      name: 'Cloudflare Workers',
      baseUrl: process.env.CLOUDFLARE_WORKER_URL || 'https://ping-api.wobys.dpdns.org',
      endpoint: ''
    }
  },
  
  // 测试超时时间
  timeout: 10000
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// HTTP请求封装
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https://');
    const client = isHttps ? https : http;
    
    const req = client.request(url, {
      method: 'GET',
      timeout: TEST_CONFIG.timeout,
      ...options
    }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: jsonData,
            headers: res.headers
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: data,
            headers: res.headers,
            parseError: e.message
          });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// 测试单个服务
async function testService(serviceName, serviceConfig, target) {
  const startTime = Date.now();
  
  try {
    let testUrl;
    if (serviceName === 'cloudflare') {
      // Cloudflare Workers 直接调用
      testUrl = `${serviceConfig.baseUrl}?target=${encodeURIComponent(target)}`;
    } else {
      // Vercel Edge Functions 通过API路由调用
      testUrl = `${serviceConfig.baseUrl}${serviceConfig.endpoint}?target=${encodeURIComponent(target)}`;
    }
    
    colorLog('cyan', `  🔍 测试 ${serviceConfig.name}: ${target}`);
    colorLog('blue', `     URL: ${testUrl}`);
    
    const response = await makeRequest(testUrl);
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    if (response.status === 200 && response.data) {
      const data = response.data;
      
      if (data.success) {
        colorLog('green', `  ✅ 成功 - 延迟: ${data.latency}ms, 总耗时: ${totalTime}ms`);
        
        // 显示详细信息
        if (data.edge) {
          colorLog('blue', `     边缘节点: ${data.edge.region || 'Unknown'} (${data.edge.country || 'Unknown'})`);
        }
        if (data.cloudflare) {
          colorLog('blue', `     CF数据中心: ${data.cloudflare.datacenter || 'Unknown'} (${data.cloudflare.colo || 'Unknown'})`);
        }
        
        return {
          success: true,
          latency: data.latency,
          totalTime,
          service: serviceConfig.name,
          details: data
        };
      } else {
        colorLog('red', `  ❌ 测试失败: ${data.error || '未知错误'}`);
        return {
          success: false,
          error: data.error,
          service: serviceConfig.name
        };
      }
    } else {
      colorLog('red', `  ❌ HTTP错误: ${response.status}`);
      if (response.parseError) {
        colorLog('yellow', `     解析错误: ${response.parseError}`);
      }
      return {
        success: false,
        error: `HTTP ${response.status}`,
        service: serviceConfig.name
      };
    }
    
  } catch (error) {
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    colorLog('red', `  ❌ 请求失败: ${error.message} (${totalTime}ms)`);
    return {
      success: false,
      error: error.message,
      service: serviceConfig.name
    };
  }
}

// 主测试函数
async function runTests() {
  colorLog('bright', '🌐 多云部署测试开始');
  colorLog('cyan', '='.repeat(60));
  
  const results = {
    vercel: [],
    cloudflare: [],
    summary: {
      total: 0,
      success: 0,
      failed: 0
    }
  };
  
  // 测试每个服务
  for (const [serviceName, serviceConfig] of Object.entries(TEST_CONFIG.services)) {
    colorLog('magenta', `\n📡 测试 ${serviceConfig.name}`);
    colorLog('cyan', '-'.repeat(40));
    
    for (const target of TEST_CONFIG.testTargets) {
      const result = await testService(serviceName, serviceConfig, target);
      results[serviceName].push(result);
      results.summary.total++;
      
      if (result.success) {
        results.summary.success++;
      } else {
        results.summary.failed++;
      }
      
      // 测试间隔
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  // 显示测试总结
  colorLog('bright', '\n📊 测试总结');
  colorLog('cyan', '='.repeat(60));
  
  colorLog('blue', `总测试数: ${results.summary.total}`);
  colorLog('green', `成功: ${results.summary.success}`);
  colorLog('red', `失败: ${results.summary.failed}`);
  colorLog('yellow', `成功率: ${((results.summary.success / results.summary.total) * 100).toFixed(1)}%`);
  
  // 详细结果
  for (const [serviceName, serviceResults] of Object.entries(results)) {
    if (serviceName === 'summary') continue;
    
    const successCount = serviceResults.filter(r => r.success).length;
    const avgLatency = serviceResults
      .filter(r => r.success && r.latency)
      .reduce((sum, r, _, arr) => sum + r.latency / arr.length, 0);
    
    colorLog('magenta', `\n${TEST_CONFIG.services[serviceName].name}:`);
    colorLog('blue', `  成功率: ${successCount}/${serviceResults.length} (${((successCount / serviceResults.length) * 100).toFixed(1)}%)`);
    if (avgLatency > 0) {
      colorLog('blue', `  平均延迟: ${avgLatency.toFixed(1)}ms`);
    }
  }
  
  // 部署建议
  colorLog('bright', '\n💡 部署建议');
  colorLog('cyan', '='.repeat(60));
  
  const vercelSuccess = results.vercel.filter(r => r.success).length;
  const cloudflareSuccess = results.cloudflare.filter(r => r.success).length;
  
  if (vercelSuccess === 0 && cloudflareSuccess === 0) {
    colorLog('red', '❌ 所有服务都无法访问，请检查部署状态');
  } else if (vercelSuccess > 0 && cloudflareSuccess > 0) {
    colorLog('green', '✅ 多云部署成功！两个服务都正常工作');
  } else if (vercelSuccess > 0) {
    colorLog('yellow', '⚠️  只有 Vercel Edge Functions 正常工作');
    colorLog('blue', '   请检查 Cloudflare Workers 部署状态');
  } else if (cloudflareSuccess > 0) {
    colorLog('yellow', '⚠️  只有 Cloudflare Workers 正常工作');
    colorLog('blue', '   请检查 Vercel Edge Functions 部署状态');
  }
  
  colorLog('bright', '\n🎯 下一步操作');
  colorLog('cyan', '='.repeat(60));
  colorLog('blue', '1. 如果测试失败，请检查环境变量配置');
  colorLog('blue', '2. 确认 Cloudflare Workers 已正确部署');
  colorLog('blue', '3. 在 Vercel 项目设置中选择香港区域');
  colorLog('blue', '4. 访问主页面测试多云功能');
  
  return results;
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    colorLog('red', `测试脚本错误: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runTests, TEST_CONFIG };
