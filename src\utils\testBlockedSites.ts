// 测试被墙网站配置的工具函数

import { 
  BLOCKED_SITES_CATEGORIES, 
  BLOCKED_DOMAINS, 
  isBlockedDomain, 
  getBlockedSiteCategory,
  BLOCKED_SITES_STATS 
} from '../config/blockedSites';

// 测试函数：验证被墙网站检测功能
export function testBlockedSitesDetection() {
  console.log('🚫 被墙网站配置测试开始...\n');
  
  // 显示统计信息
  console.log('📊 统计信息:');
  console.log(`- 总分类数: ${BLOCKED_SITES_STATS.totalCategories}`);
  console.log(`- 总域名数: ${BLOCKED_SITES_STATS.totalDomains}`);
  console.log('- 各分类域名数量:');
  BLOCKED_SITES_STATS.categoriesWithCounts.forEach(cat => {
    console.log(`  ${cat.icon} ${cat.name}: ${cat.count} 个域名`);
  });
  console.log('');

  // 测试一些已知被墙的网站
  const testCases = [
    // 应该被检测为被墙的网站
    { domain: 'google.com', expected: true, description: 'Google搜索' },
    { domain: 'facebook.com', expected: true, description: 'Facebook社交' },
    { domain: 'youtube.com', expected: true, description: 'YouTube视频' },
    { domain: 'twitter.com', expected: true, description: 'Twitter社交' },
    { domain: 'instagram.com', expected: true, description: 'Instagram社交' },
    { domain: 'github.com', expected: false, description: 'GitHub代码托管' }, // GitHub在某些地区可访问
    { domain: 'openai.com', expected: true, description: 'OpenAI' },
    { domain: 'claude.ai', expected: true, description: 'Claude AI' },
    { domain: 'wikipedia.org', expected: true, description: 'Wikipedia' },
    { domain: 'telegram.org', expected: true, description: 'Telegram' },
    
    // 应该不被检测为被墙的网站
    { domain: 'baidu.com', expected: false, description: '百度搜索' },
    { domain: 'qq.com', expected: false, description: 'QQ' },
    { domain: 'taobao.com', expected: false, description: '淘宝' },
    { domain: 'bilibili.com', expected: false, description: 'B站' },
    { domain: 'zhihu.com', expected: false, description: '知乎' },
  ];

  console.log('🧪 测试用例结果:');
  let passedTests = 0;
  let totalTests = testCases.length;

  testCases.forEach(testCase => {
    const result = isBlockedDomain(testCase.domain);
    const passed = result === testCase.expected;
    const status = passed ? '✅' : '❌';
    const category = getBlockedSiteCategory(testCase.domain);
    
    console.log(`${status} ${testCase.domain} (${testCase.description})`);
    console.log(`   预期: ${testCase.expected ? '被墙' : '未被墙'}, 实际: ${result ? '被墙' : '未被墙'}`);
    if (category) {
      console.log(`   分类: ${category.icon} ${category.name}`);
    }
    console.log('');
    
    if (passed) passedTests++;
  });



  return {
    passed: passedTests,
    total: totalTests,
    success: passedTests === totalTests
  };
}

// 显示所有被墙网站分类
export function showAllBlockedCategories() {
  // 返回分类信息而不是打印到控制台
  return BLOCKED_SITES_CATEGORIES.map((category, index) => ({
    index: index + 1,
    icon: category.icon,
    name: category.name,
    domainCount: category.domains.length,
    domains: category.domains
  }));
}

// 搜索特定域名的分类信息
export function searchDomainInfo(domain: string) {
  const isBlocked = isBlockedDomain(domain);
  const category = getBlockedSiteCategory(domain);
  
  console.log(`🔍 域名信息: ${domain}`);
  console.log(`状态: ${isBlocked ? '🚫 被墙' : '✅ 未被墙'}`);
  
  if (category) {
    console.log(`分类: ${category.icon} ${category.name}`);
    console.log(`该分类包含的其他域名:`);
    category.domains.forEach(d => {
      if (d !== domain) {
        console.log(`  - ${d}`);
      }
    });
  } else if (isBlocked) {
    console.log('⚠️ 域名被标记为被墙，但未找到具体分类');
  }
  
  return { isBlocked, category };
}
