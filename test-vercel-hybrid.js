// 使用Node.js内置的fetch (Node.js 18+)

// 🌐 你之前给我的所有测试网站
const testWebsites = [
  'baidu.com',
  'sohu.com', 
  'www.linovel.net',
  'www.xygalaxy.com',
  'freedidi.com',
  'hitpaw.com',
  'feejii.com',
  'google.com',
  'reddit.com',
  'bbc.com',
  'pinterest.com',
  'annas-archive.org',
  'archive.org'
];

// Vercel生产环境API地址
const VERCEL_API_URL = 'https://ping-rmd2gk155-wob-21s-projects.vercel.app/api/hybrid-ping';

async function testSingleWebsite(website) {
  console.log(`\n🔍 测试网站: ${website}`);
  console.log('='.repeat(50));
  
  try {
    const startTime = Date.now();
    
    const response = await fetch(VERCEL_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ target: website }),
    });
    
    const endTime = Date.now();
    const apiLatency = endTime - startTime;
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    console.log(`✅ API响应时间: ${apiLatency}ms`);
    console.log(`🎯 测试方法: ${data.metadata?.testMethod || 'unknown'}`);
    console.log(`📂 网站类别: ${data.metadata?.category || 'unknown'}`);
    console.log(`🎯 置信度: ${((data.metadata?.confidence || 0) * 100).toFixed(1)}%`);
    console.log(`⚡ 平均延迟: ${data.metadata?.averageLatency || 'N/A'}ms`);
    console.log(`🌍 Vercel区域: ${data.metadata?.vercelRegion || 'unknown'}`);
    
    if (data.results && data.results.length > 0) {
      console.log(`\n📊 各省份延迟样本 (前5个):`);
      data.results.slice(0, 5).forEach(result => {
        console.log(`  ${result.node}: ${result.ping}ms`);
      });
    }
    
    if (data.recommendations && data.recommendations.length > 0) {
      console.log(`\n💡 建议:`);
      data.recommendations.forEach(rec => {
        console.log(`  - ${rec}`);
      });
    }
    
    return {
      website,
      success: true,
      apiLatency,
      averageLatency: data.metadata?.averageLatency || 0,
      testMethod: data.metadata?.testMethod || 'unknown',
      category: data.metadata?.category || 'unknown',
      confidence: data.metadata?.confidence || 0,
      vercelRegion: data.metadata?.vercelRegion || 'unknown'
    };
    
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`);
    return {
      website,
      success: false,
      error: error.message,
      apiLatency: 0,
      averageLatency: 0
    };
  }
}

async function testAllWebsites() {
  console.log('🚀 开始测试所有网站...');
  console.log(`📡 API地址: ${VERCEL_API_URL}`);
  console.log(`📅 测试时间: ${new Date().toISOString()}`);
  console.log('='.repeat(80));
  
  const results = [];
  
  for (let i = 0; i < testWebsites.length; i++) {
    const website = testWebsites[i];
    const result = await testSingleWebsite(website);
    results.push(result);
    
    // 等待2秒避免过于频繁的请求
    if (i < testWebsites.length - 1) {
      console.log('\n⏳ 等待2秒...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  // 生成汇总报告
  console.log('\n\n📋 测试汇总报告');
  console.log('='.repeat(80));
  
  const successfulTests = results.filter(r => r.success);
  const failedTests = results.filter(r => !r.success);
  
  console.log(`✅ 成功测试: ${successfulTests.length}/${results.length}`);
  console.log(`❌ 失败测试: ${failedTests.length}/${results.length}`);
  
  if (successfulTests.length > 0) {
    const avgApiLatency = Math.round(successfulTests.reduce((sum, r) => sum + r.apiLatency, 0) / successfulTests.length);
    const avgPingLatency = Math.round(successfulTests.reduce((sum, r) => sum + r.averageLatency, 0) / successfulTests.length);
    
    console.log(`⚡ 平均API响应时间: ${avgApiLatency}ms`);
    console.log(`🌐 平均ping延迟: ${avgPingLatency}ms`);
    
    // 按测试方法分组
    const methodGroups = {};
    successfulTests.forEach(r => {
      if (!methodGroups[r.testMethod]) {
        methodGroups[r.testMethod] = [];
      }
      methodGroups[r.testMethod].push(r);
    });
    
    console.log(`\n🎯 测试方法分布:`);
    Object.keys(methodGroups).forEach(method => {
      const count = methodGroups[method].length;
      const avgLatency = Math.round(methodGroups[method].reduce((sum, r) => sum + r.averageLatency, 0) / count);
      console.log(`  ${method}: ${count}个网站, 平均延迟: ${avgLatency}ms`);
    });
    
    // 按网站类别分组
    const categoryGroups = {};
    successfulTests.forEach(r => {
      if (!categoryGroups[r.category]) {
        categoryGroups[r.category] = [];
      }
      categoryGroups[r.category].push(r);
    });
    
    console.log(`\n📂 网站类别分布:`);
    Object.keys(categoryGroups).forEach(category => {
      const count = categoryGroups[category].length;
      const avgLatency = Math.round(categoryGroups[category].reduce((sum, r) => sum + r.averageLatency, 0) / count);
      console.log(`  ${category}: ${count}个网站, 平均延迟: ${avgLatency}ms`);
    });
  }
  
  if (failedTests.length > 0) {
    console.log(`\n❌ 失败的测试:`);
    failedTests.forEach(r => {
      console.log(`  ${r.website}: ${r.error}`);
    });
  }
  
  console.log(`\n🌍 Vercel部署区域: ${successfulTests[0]?.vercelRegion || 'unknown'}`);
  console.log(`📅 测试完成时间: ${new Date().toISOString()}`);
}

// 运行测试
testAllWebsites().catch(console.error);
