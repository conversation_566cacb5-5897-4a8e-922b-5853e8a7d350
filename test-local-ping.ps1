# 本地ping测试脚本

$testWebsites = @(
    "baidu.com",
    "sohu.com", 
    "www.linovel.net",
    "www.xygalaxy.com",
    "freedidi.com",
    "hitpaw.com",
    "feejii.com",
    "google.com",
    "reddit.com",
    "bbc.com",
    "pinterest.com",
    "annas-archive.org",
    "archive.org"
)

Write-Host "🚀 开始本地ping测试..." -ForegroundColor Green
Write-Host "📅 测试时间: $(Get-Date -Format 'yyyy-MM-ddTHH:mm:ss.fffZ')" -ForegroundColor Cyan
Write-Host ("=" * 80) -ForegroundColor Yellow

$results = @()

foreach ($website in $testWebsites) {
    Write-Host "`n🔍 Ping测试网站: $website" -ForegroundColor Yellow
    Write-Host ("=" * 50) -ForegroundColor Gray
    
    try {
        $startTime = Get-Date
        
        # 执行ping命令，发送4个包
        $pingResult = ping $website -n 4 2>&1
        
        $endTime = Get-Date
        $testDuration = ($endTime - $startTime).TotalMilliseconds
        
        # 解析ping结果
        $success = $false
        $avgLatency = 0
        $minLatency = 0
        $maxLatency = 0
        $packetLoss = 100
        
        if ($pingResult -match "平均 = (\d+)ms") {
            $avgLatency = [int]$matches[1]
            $success = $true
        } elseif ($pingResult -match "Average = (\d+)ms") {
            $avgLatency = [int]$matches[1]
            $success = $true
        }
        
        if ($pingResult -match "最短 = (\d+)ms，最长 = (\d+)ms") {
            $minLatency = [int]$matches[1]
            $maxLatency = [int]$matches[2]
        } elseif ($pingResult -match "Minimum = (\d+)ms, Maximum = (\d+)ms") {
            $minLatency = [int]$matches[1]
            $maxLatency = [int]$matches[2]
        }
        
        if ($pingResult -match "丢失 = (\d+) \((\d+)% 丢失\)") {
            $packetLoss = [int]$matches[2]
        } elseif ($pingResult -match "Lost = (\d+) \((\d+)% loss\)") {
            $packetLoss = [int]$matches[2]
        }
        
        if ($success -and $packetLoss -lt 100) {
            Write-Host "✅ Ping成功" -ForegroundColor Green
            Write-Host "⚡ 平均延迟: ${avgLatency}ms" -ForegroundColor Cyan
            Write-Host "📊 最小延迟: ${minLatency}ms" -ForegroundColor Cyan
            Write-Host "📊 最大延迟: ${maxLatency}ms" -ForegroundColor Cyan
            Write-Host "📦 丢包率: ${packetLoss}%" -ForegroundColor Cyan
            Write-Host "⏱️ 测试耗时: $([math]::Round($testDuration))ms" -ForegroundColor Gray
            
            $status = "success"
            if ($avgLatency -lt 50) {
                $evaluation = "非常快"
            } elseif ($avgLatency -lt 100) {
                $evaluation = "正常"
            } elseif ($avgLatency -lt 200) {
                $evaluation = "较慢"
            } else {
                $evaluation = "很慢"
            }
            
        } else {
            Write-Host "❌ Ping失败或超时" -ForegroundColor Red
            Write-Host "📦 丢包率: ${packetLoss}%" -ForegroundColor Red
            Write-Host "⏱️ 测试耗时: $([math]::Round($testDuration))ms" -ForegroundColor Gray
            
            $status = "failed"
            $evaluation = "无法连接"
        }
        
        $results += @{
            website = $website
            success = $success
            avgLatency = $avgLatency
            minLatency = $minLatency
            maxLatency = $maxLatency
            packetLoss = $packetLoss
            testDuration = [math]::Round($testDuration)
            status = $status
            evaluation = $evaluation
        }
        
    } catch {
        Write-Host "❌ 测试异常: $($_.Exception.Message)" -ForegroundColor Red
        $results += @{
            website = $website
            success = $false
            avgLatency = 0
            minLatency = 0
            maxLatency = 0
            packetLoss = 100
            testDuration = 0
            status = "error"
            evaluation = "测试异常"
            error = $_.Exception.Message
        }
    }
    
    # 等待1秒避免过于频繁的请求
    if ($website -ne $testWebsites[-1]) {
        Write-Host "`n⏳ 等待1秒..." -ForegroundColor Gray
        Start-Sleep -Seconds 1
    }
}

# 生成汇总报告
Write-Host "`n`n📋 本地Ping测试汇总报告" -ForegroundColor Green
Write-Host ("=" * 80) -ForegroundColor Yellow

$successfulTests = $results | Where-Object { $_.success -eq $true }
$failedTests = $results | Where-Object { $_.success -eq $false }

Write-Host "✅ 成功测试: $($successfulTests.Count)/$($results.Count)" -ForegroundColor Green
Write-Host "❌ 失败测试: $($failedTests.Count)/$($results.Count)" -ForegroundColor Red

if ($successfulTests.Count -gt 0) {
    $avgLatency = [math]::Round(($successfulTests | Measure-Object -Property avgLatency -Average).Average)
    $minLatency = ($successfulTests | Measure-Object -Property avgLatency -Minimum).Minimum
    $maxLatency = ($successfulTests | Measure-Object -Property avgLatency -Maximum).Maximum
    
    Write-Host "⚡ 平均延迟: ${avgLatency}ms" -ForegroundColor Cyan
    Write-Host "📊 最快延迟: ${minLatency}ms" -ForegroundColor Cyan
    Write-Host "📊 最慢延迟: ${maxLatency}ms" -ForegroundColor Cyan
    
    Write-Host "`n📊 详细结果:" -ForegroundColor Magenta
    foreach ($result in $results) {
        if ($result.success) {
            Write-Host "  $($result.website): $($result.avgLatency)ms $($result.evaluation)" -ForegroundColor White
        } else {
            Write-Host "  $($result.website): $($result.evaluation)" -ForegroundColor Red
        }
    }
}

if ($failedTests.Count -gt 0) {
    Write-Host "`n❌ 失败的测试:" -ForegroundColor Red
    foreach ($failed in $failedTests) {
        if ($failed.error) {
            Write-Host "  $($failed.website): $($failed.error)" -ForegroundColor White
        } else {
            Write-Host "  $($failed.website): 丢包率 $($failed.packetLoss)%" -ForegroundColor White
        }
    }
}

Write-Host "`n📅 测试完成时间: $(Get-Date -Format 'yyyy-MM-ddTHH:mm:ss.fffZ')" -ForegroundColor Cyan

# 导出结果到JSON文件以便对比
$results | ConvertTo-Json -Depth 3 | Out-File -FilePath "local-ping-results.json" -Encoding UTF8
Write-Host "💾 结果已保存到 local-ping-results.json" -ForegroundColor Green
