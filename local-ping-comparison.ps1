# 本地ping测试对比

$testWebsites = @(
    "baidu.com",
    "taobao.com", 
    "jd.com",
    "qq.com",
    "weibo.com",
    "google.com",
    "facebook.com",
    "twitter.com",
    "instagram.com",
    "reddit.com"
)

Write-Host "开始本地ping测试..." -ForegroundColor Green
Write-Host ("=" * 60) -ForegroundColor Yellow

$results = @()

foreach ($website in $testWebsites) {
    Write-Host "`n测试网站: $website" -ForegroundColor Yellow
    
    try {
        $pingOutput = ping $website -n 4 2>&1 | Out-String
        
        $success = $false
        $avgLatency = 0
        
        if ($pingOutput -match "平均 = (\d+)ms") {
            $avgLatency = [int]$matches[1]
            $success = $true
        } elseif ($pingOutput -match "Average = (\d+)ms") {
            $avgLatency = [int]$matches[1]
            $success = $true
        }
        
        if ($success) {
            Write-Host "Ping成功 - 平均延迟: ${avgLatency}ms" -ForegroundColor Green
            $results += @{
                website = $website
                success = $true
                avgLatency = $avgLatency
            }
        } else {
            Write-Host "Ping失败或超时" -ForegroundColor Red
            $results += @{
                website = $website
                success = $false
                avgLatency = 0
            }
        }
        
    } catch {
        Write-Host "测试异常: $($_.Exception.Message)" -ForegroundColor Red
        $results += @{
            website = $website
            success = $false
            avgLatency = 0
        }
    }
    
    Start-Sleep -Seconds 1
}

Write-Host "`n本地Ping测试结果汇总:" -ForegroundColor Green
foreach ($result in $results) {
    if ($result.success) {
        Write-Host "$($result.website): $($result.avgLatency)ms" -ForegroundColor White
    } else {
        Write-Host "$($result.website): 超时/失败" -ForegroundColor Red
    }
}

Write-Host "`n测试完成" -ForegroundColor Green
