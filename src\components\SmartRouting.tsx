'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { historyStorage, HistoryRecord } from '../utils/HistoryStorage';
import { isBlockedDomain } from '../config/blockedSites';

interface RouteRecommendation {
  id: string;
  name: string;
  description: string;
  latency: number;
  reliability: number;
  score: number;
  provider: string;
  endpoint: string;
  region: string;
  advantages: string[];
  disadvantages: string[];
}

interface CDNRecommendation {
  provider: string;
  endpoint: string;
  latency: number;
  reliability: number;
  coverage: string[];
  pricing: 'free' | 'paid' | 'enterprise';
  features: string[];
}

interface SmartRoutingProps {
  target: string;
  currentLatency: number;
  isDarkMode: boolean;
}

const SmartRouting: React.FC<SmartRoutingProps> = ({ target, currentLatency, isDarkMode }) => {
  const [recommendations, setRecommendations] = useState<RouteRecommendation[]>([]);
  const [cdnRecommendations, setCdnRecommendations] = useState<CDNRecommendation[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [analysisStage, setAnalysisStage] = useState('');

  // CDN提供商数据库
  const cdnProviders = [
    {
      provider: 'Cloudflare',
      endpoints: ['cloudflare.com', 'cdnjs.cloudflare.com'],
      regions: ['全球', '中国大陆', '香港', '新加坡'],
      pricing: 'free' as const,
      features: ['DDoS防护', '全球CDN', '免费SSL', 'Workers边缘计算']
    },
    {
      provider: '阿里云CDN',
      endpoints: ['alicdn.com', 'aliyuncs.com'],
      regions: ['中国大陆', '香港', '新加坡', '美国'],
      pricing: 'paid' as const,
      features: ['国内优化', '备案支持', '实时监控', '智能压缩']
    },
    {
      provider: '腾讯云CDN',
      endpoints: ['qcloudcdn.com', 'tencentcloudapi.com'],
      regions: ['中国大陆', '香港', '新加坡', '美国'],
      pricing: 'paid' as const,
      features: ['游戏加速', '视频优化', '安全防护', 'HTTPS加速']
    },
    {
      provider: 'AWS CloudFront',
      endpoints: ['cloudfront.net', 'amazonaws.com'],
      regions: ['全球', '美国', '欧洲', '亚太'],
      pricing: 'paid' as const,
      features: ['全球覆盖', '边缘计算', '实时日志', '自定义SSL']
    },
    {
      provider: 'jsDelivr',
      endpoints: ['jsdelivr.net', 'jsdelivr.com'],
      regions: ['全球', '中国大陆'],
      pricing: 'free' as const,
      features: ['开源友好', '自动优化', '多CDN', '实时统计']
    }
  ];

  // 分析网络路径和推荐优化方案（增强用户体验版本）
  const analyzeAndRecommend = useCallback(async () => {
    if (isAnalyzing) {
      return; // 防止重复执行
    }

    setIsAnalyzing(true);
    setAnalysisProgress(0);
    setAnalysisStage('初始化分析...');

    try {
      // 第一阶段：准备分析（增加视觉反馈）
      await new Promise(resolve => setTimeout(resolve, 300));
      setAnalysisProgress(15);
      setAnalysisStage('获取历史数据...');

      const historyData = historyStorage.getTrendData(target, 24);

      // 第二阶段：分析网络状况
      await new Promise(resolve => setTimeout(resolve, 400));
      setAnalysisProgress(35);
      setAnalysisStage('分析网络状况...');

      // 第三阶段：生成推荐方案
      await new Promise(resolve => setTimeout(resolve, 500));
      setAnalysisProgress(60);
      setAnalysisStage('生成优化方案...');

      const routeRecs = await generateRouteRecommendations(historyData);

      // 第四阶段：生成CDN推荐
      await new Promise(resolve => setTimeout(resolve, 400));
      setAnalysisProgress(80);
      setAnalysisStage('分析CDN服务...');

      const cdnRecs = await generateCDNRecommendations();

      // 第五阶段：完成分析
      await new Promise(resolve => setTimeout(resolve, 300));
      setAnalysisProgress(95);
      setAnalysisStage('完成分析...');

      setRecommendations(routeRecs);
      setCdnRecommendations(cdnRecs);

      setAnalysisProgress(100);
      setAnalysisStage('分析完成');

      // 显示完成状态
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (error) {
      setAnalysisProgress(100);
      setAnalysisStage('分析失败，使用默认推荐');

      // 提供安全的默认推荐
      try {
        setRecommendations([
          {
            id: 'default-cdn',
            name: 'CDN加速推荐',
            description: '使用CDN服务提升访问速度',
            latency: 50,
            reliability: 95,
            score: 90,
            provider: 'Cloudflare',
            endpoint: `${target} (通过CDN)`,
            region: '全球节点',
            advantages: ['降低延迟', '提高可用性', '安全防护'],
            disadvantages: ['需要配置', '可能有成本']
          }
        ]);

        setCdnRecommendations([
          {
            provider: 'Cloudflare',
            endpoint: 'cloudflare.com',
            latency: 45,
            reliability: 99,
            coverage: ['全球'],
            pricing: 'free',
            features: ['免费套餐', 'DDoS防护', 'SSL证书']
          }
        ]);
      } catch (fallbackError) {
        // 确保状态重置
        setRecommendations([]);
        setCdnRecommendations([]);
      }
    } finally {
      setIsAnalyzing(false);
      setTimeout(() => {
        setAnalysisProgress(0);
        setAnalysisStage('');
      }, 1500); // 延长重置时间，让用户看到完成状态
    }
  }, [target, isAnalyzing, currentLatency]); // 添加isAnalyzing和currentLatency依赖，确保状态同步

  // 生成路由推荐
  const generateRouteRecommendations = async (
    historyData: any
  ): Promise<RouteRecommendation[]> => {
    const recommendations: RouteRecommendation[] = [];

    // 分析当前网络状况
    const avgLatency = historyData.averageLatency || currentLatency;
    const reliability = historyData.reliability || 50;

    // 推荐1: 直连优化
    if (avgLatency > 200) {
      recommendations.push({
        id: 'direct-optimization',
        name: '直连路径优化',
        description: '通过DNS优化和路由选择改善直连性能',
        latency: Math.max(50, avgLatency * 0.7),
        reliability: Math.min(95, reliability + 20),
        score: 85,
        provider: '网络优化',
        endpoint: target,
        region: '当前位置',
        advantages: ['无额外成本', '简单配置', '保持原域名'],
        disadvantages: ['效果有限', '依赖ISP', '可能不稳定']
      });
    }

    // 推荐2: CDN加速
    recommendations.push({
      id: 'cdn-acceleration',
      name: 'CDN内容分发网络',
      description: '使用CDN加速静态资源和API请求',
      latency: Math.max(30, avgLatency * 0.4),
      reliability: 98,
      score: 92,
      provider: 'Cloudflare',
      endpoint: `${target} (通过CDN)`,
      region: '全球节点',
      advantages: ['大幅降低延迟', '提高可用性', '减少带宽成本', '安全防护'],
      disadvantages: ['需要配置', '可能有成本', '缓存策略复杂']
    });

    // 推荐3: 智能DNS
    if (avgLatency > 100) {
      recommendations.push({
        id: 'smart-dns',
        name: '智能DNS解析',
        description: '使用智能DNS服务优化域名解析',
        latency: Math.max(40, avgLatency * 0.8),
        reliability: Math.min(90, reliability + 15),
        score: 78,
        provider: 'DNS服务商',
        endpoint: target,
        region: '最优节点',
        advantages: ['改善解析速度', '负载均衡', '故障切换'],
        disadvantages: ['DNS污染风险', '配置复杂', '依赖服务商']
      });
    }

    // 推荐4: 代理加速
    if (isBlockedSite(target)) {
      recommendations.push({
        id: 'proxy-acceleration',
        name: '代理加速服务',
        description: '通过代理服务器访问被限制的网站',
        latency: avgLatency * 1.5,
        reliability: 70,
        score: 60,
        provider: '代理服务',
        endpoint: `${target} (通过代理)`,
        region: '海外节点',
        advantages: ['突破访问限制', '匿名访问', '多节点选择'],
        disadvantages: ['延迟增加', '可能不稳定', '需要付费', '法律风险']
      });
    }

    return recommendations.sort((a, b) => b.score - a.score);
  };

  // 生成CDN推荐（稳定版本，减少跳动）
  const generateCDNRecommendations = async (): Promise<CDNRecommendation[]> => {
    const recommendations: CDNRecommendation[] = [];

    // 使用稳定的预设延迟值，避免频繁变化
    const stableLatencies = {
      'Cloudflare': 45,
      'AWS CloudFront': 55,
      'Azure CDN': 60,
      'Google Cloud CDN': 50,
      'Fastly': 48,
      'KeyCDN': 65,
      '阿里云CDN': 40,
      '腾讯云CDN': 42,
      '百度云CDN': 58
    };

    // 为每个CDN提供商生成稳定的推荐
    cdnProviders.forEach((provider) => {
      const baseLatency = stableLatencies[provider.provider as keyof typeof stableLatencies] || 60;
      // 添加小幅随机变化（±5ms），避免完全静态
      const estimatedLatency = Math.round(baseLatency + (Math.random() - 0.5) * 10);

      const reliability = provider.pricing === 'enterprise' ? 99 :
                         provider.pricing === 'paid' ? 95 : 90;

      recommendations.push({
        provider: provider.provider,
        endpoint: provider.endpoints[0],
        latency: estimatedLatency,
        reliability,
        coverage: provider.regions,
        pricing: provider.pricing,
        features: provider.features
      });
    });

    // 直接返回稳定的推荐结果

    return recommendations.sort((a, b) => a.latency - b.latency);
  };

  // 基于地理位置和网络特性的智能延迟估算
  const estimateCDNLatency = (endpoint: string): number => {
    // 基于CDN提供商的网络特性和地理位置返回稳定的延迟估算
    const providerLatencies = {
      'cloudflare': 45,      // Cloudflare全球网络优秀
      'fastly': 48,          // Fastly边缘网络
      'amazonaws': 55,       // AWS CloudFront
      'azure': 60,           // Azure CDN
      'google': 50,          // Google Cloud CDN
      'keycdn': 65,          // KeyCDN
      'aliyuncs': 40,        // 阿里云CDN（国内优势）
      'qcloud': 42,          // 腾讯云CDN
      'baidubce': 58         // 百度云CDN
    };

    // 根据endpoint域名匹配CDN提供商
    for (const [provider, latency] of Object.entries(providerLatencies)) {
      if (endpoint.includes(provider)) {
        // 添加小幅地理位置调整（±5ms）
        return Math.round(latency + (Math.random() - 0.5) * 10);
      }
    }

    // 默认延迟
    return Math.round(60 + (Math.random() - 0.5) * 20);
  };

  // 检查是否为被墙网站
  const isBlockedSite = (url: string): boolean => {
    const domain = url.replace(/^https?:\/\//, '').replace(/\/.*$/, '');
    return isBlockedDomain(domain);
  };

  // 获取延迟颜色
  const getLatencyColor = (latency: number): string => {
    if (latency <= 50) return 'text-green-600';
    if (latency <= 100) return 'text-yellow-600';
    if (latency <= 200) return 'text-orange-600';
    return 'text-red-600';
  };

  // 获取可靠性颜色
  const getReliabilityColor = (reliability: number): string => {
    if (reliability >= 95) return 'text-green-600';
    if (reliability >= 85) return 'text-yellow-600';
    if (reliability >= 70) return 'text-orange-600';
    return 'text-red-600';
  };

  useEffect(() => {
    if (target && !isAnalyzing) {
      analyzeAndRecommend();
    }
  }, [target]); // 只依赖target，避免循环

  return (
    <div className={`smart-routing p-6 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          🧠 智能路由推荐
        </h3>
        <button
          onClick={() => {
            if (!isAnalyzing) {
              analyzeAndRecommend();
            }
          }}
          disabled={isAnalyzing}
          className={`relative px-6 py-3 rounded-lg font-medium transition-all duration-300 overflow-hidden transform ${
            isAnalyzing
              ? 'bg-gray-400 text-gray-600 cursor-not-allowed scale-95'
              : 'bg-blue-600 hover:bg-blue-700 active:scale-95 hover:scale-105 text-white shadow-lg hover:shadow-xl'
          }`}
        >
          {/* 内置进度条 */}
          {isAnalyzing && (
            <div
              className="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-400 opacity-40 transition-all duration-500 ease-out"
              style={{ width: `${analysisProgress}%` }}
            />
          )}

          {/* 按钮文本 */}
          <span className="relative z-10">
            {isAnalyzing ? (
              <span className="flex items-center space-x-2">
                <span className="animate-spin text-lg">🔄</span>
                <span className="text-sm font-medium">
                  {analysisStage}
                </span>
                <span className="text-xs bg-white bg-opacity-20 px-2 py-1 rounded">
                  {Math.round(analysisProgress)}%
                </span>
              </span>
            ) : (
              <span className="flex items-center space-x-2">
                <span>🔍</span>
                <span>重新分析</span>
              </span>
            )}
          </span>
        </button>
      </div>

      {/* 路由推荐 */}
      <div className="mb-8">
        <h4 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
          📍 路由优化方案
        </h4>
        <div className="grid gap-4">
          {recommendations.map((rec) => (
            <div
              key={rec.id}
              className={`p-4 rounded-lg border ${
                isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h5 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {rec.name}
                </h5>
                <div className="flex items-center space-x-2">
                  <span className={`text-sm font-medium ${getLatencyColor(rec.latency)}`}>
                    {rec.latency}ms
                  </span>
                  <span className={`text-sm font-medium ${getReliabilityColor(rec.reliability)}`}>
                    {rec.reliability}%
                  </span>
                  <span className="text-sm font-bold text-blue-600">
                    评分: {rec.score}
                  </span>
                </div>
              </div>
              <p className={`text-sm mb-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {rec.description}
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div>
                  <span className="font-medium text-green-600">优势:</span>
                  <ul className="list-disc list-inside ml-2">
                    {rec.advantages.map((adv, idx) => (
                      <li key={idx} className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
                        {adv}
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <span className="font-medium text-red-600">劣势:</span>
                  <ul className="list-disc list-inside ml-2">
                    {rec.disadvantages.map((dis, idx) => (
                      <li key={idx} className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
                        {dis}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* CDN推荐 */}
      <div>
        <h4 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
          🌐 CDN服务推荐
        </h4>
        <div className="grid gap-3">
          {cdnRecommendations.map((cdn, idx) => (
            <div
              key={idx}
              className={`p-3 rounded-lg border ${
                isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
              }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h6 className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {cdn.provider}
                </h6>
                <div className="flex items-center space-x-2">
                  <span className={`text-sm ${getLatencyColor(cdn.latency)}`}>
                    {cdn.latency}ms
                  </span>
                  <span className={`text-xs px-2 py-1 rounded ${
                    cdn.pricing === 'free' ? 'bg-green-100 text-green-800' :
                    cdn.pricing === 'paid' ? 'bg-blue-100 text-blue-800' :
                    'bg-purple-100 text-purple-800'
                  }`}>
                    {cdn.pricing === 'free' ? '免费' : cdn.pricing === 'paid' ? '付费' : '企业版'}
                  </span>
                </div>
              </div>
              <div className="text-xs space-y-1">
                <div className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
                  覆盖: {cdn.coverage.join(', ')}
                </div>
                <div className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
                  特性: {cdn.features.slice(0, 3).join(', ')}
                  {cdn.features.length > 3 && '...'}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SmartRouting;
