#!/usr/bin/env node

const http = require('http');

function makeRequest(data) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/enhanced-ping',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 45000
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve(result);
        } catch (error) {
          reject(new Error('Invalid JSON response: ' + responseData));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.write(postData);
    req.end();
  });
}

async function testSite(url, name) {
  console.log(`🌐 测试 ${name} (${url})`);
  console.log('='.repeat(50));
  
  try {
    const startTime = Date.now();
    
    const result = await makeRequest({
      target: url,
      maxNodes: 999
    });

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    console.log(`✅ 响应时间: ${responseTime}ms`);
    console.log(`📊 结果:`, JSON.stringify(result, null, 2));

  } catch (error) {
    console.log('❌ 请求失败:', error.message);
  }
}

// 测试Google
testSite('google.com', 'Google').catch(console.error);
