// 简单的被墙网站配置测试

// 模拟被墙网站配置
const BLOCKED_DOMAINS = [
  // 搜索引擎
  'google.com', 'duckduckgo.com', 'startpage.com', 'wikipedia.org',
  
  // 社交媒体
  'facebook.com', 'instagram.com', 'twitter.com', 'x.com', 'threads.net',
  'reddit.com', 'pinterest.com', 'snapchat.com', 'clubhouse.com', 'tumblr.com', 'quora.com',
  
  // 视频/流媒体
  'youtube.com', 'vimeo.com', 'dailymotion.com', 'twitch.tv', 'netflix.com', 'spotify.com',
  
  // 通讯工具
  'telegram.org', 'signal.org',
  
  // 邮箱/云服务
  'gmail.com', 'mail.google.com', 'drive.google.com', 'dropbox.com', 'protonmail.com', 'tutanota.com',
  
  // 开发者工具
  'copilot.github.com', 'sourceforge.net', 'hub.docker.com',
  
  // 学术知识库
  'archive.org', 'web.archive.org', 'jstor.org', 'wikileaks.org',
  
  // 新闻媒体
  'nytimes.com', 'bbc.com', 'rfa.org', 'voachinese.com', 'epochtimes.com',
  'amnesty.org', 'renminbao.com', 'hongkongfp.com', 'pincong.rocks',
  
  // 隐私工具
  'torproject.org', 'psiphon.ca', 'getlantern.org', 'geti2p.net', 'ultrasurf.us',
  
  // AI服务
  'openai.com', 'claude.ai', 'perplexity.ai', 'huggingface.co'
];

function isBlockedDomain(domain) {
  const domainLower = domain.toLowerCase();
  return BLOCKED_DOMAINS.some(blockedDomain => {
    if (blockedDomain.includes('*')) {
      const pattern = blockedDomain.replace(/\*/g, '.*');
      const regex = new RegExp(pattern, 'i');
      return regex.test(domainLower);
    }
    return domainLower.includes(blockedDomain) || domainLower.endsWith(blockedDomain);
  });
}

// 测试用例
const testCases = [
  // 应该被检测为被墙的网站
  { domain: 'google.com', expected: true, description: 'Google搜索' },
  { domain: 'facebook.com', expected: true, description: 'Facebook社交' },
  { domain: 'youtube.com', expected: true, description: 'YouTube视频' },
  { domain: 'twitter.com', expected: true, description: 'Twitter社交' },
  { domain: 'instagram.com', expected: true, description: 'Instagram社交' },
  { domain: 'openai.com', expected: true, description: 'OpenAI' },
  { domain: 'claude.ai', expected: true, description: 'Claude AI' },
  { domain: 'wikipedia.org', expected: true, description: 'Wikipedia' },
  { domain: 'telegram.org', expected: true, description: 'Telegram' },
  
  // 应该不被检测为被墙的网站
  { domain: 'baidu.com', expected: false, description: '百度搜索' },
  { domain: 'qq.com', expected: false, description: 'QQ' },
  { domain: 'taobao.com', expected: false, description: '淘宝' },
  { domain: 'bilibili.com', expected: false, description: 'B站' },
  { domain: 'zhihu.com', expected: false, description: '知乎' },
];

console.log('🚫 被墙网站配置测试开始...\n');
console.log(`📊 总共配置了 ${BLOCKED_DOMAINS.length} 个被墙域名\n`);

console.log('🧪 测试用例结果:');
let passedTests = 0;
let totalTests = testCases.length;

testCases.forEach(testCase => {
  const result = isBlockedDomain(testCase.domain);
  const passed = result === testCase.expected;
  const status = passed ? '✅' : '❌';
  
  console.log(`${status} ${testCase.domain} (${testCase.description})`);
  console.log(`   预期: ${testCase.expected ? '被墙' : '未被墙'}, 实际: ${result ? '被墙' : '未被墙'}`);
  
  if (passed) passedTests++;
});

console.log(`\n📈 测试结果: ${passedTests}/${totalTests} 通过 (${Math.round(passedTests/totalTests*100)}%)`);

if (passedTests === totalTests) {
  console.log('🎉 所有测试通过！被墙网站配置工作正常。');
} else {
  console.log('⚠️ 部分测试失败，请检查配置。');
}

console.log('\n🚫 被墙网站列表已成功添加到项目中！');
console.log('📁 配置文件位置: src/config/blockedSites.ts');
console.log('🔧 已更新的文件:');
console.log('  - src/services/PingService.ts');
console.log('  - src/services/EnhancedPingService.ts');
console.log('  - src/components/SmartRouting.tsx');
