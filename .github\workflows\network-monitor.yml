# GitHub Actions 专业网络监控配置
# 文件路径: .github/workflows/network-monitor.yml

name: 🌐 Professional Network Monitor

on:
  schedule:
    # 每5分钟执行一次监控 (GitHub Actions最小间隔)
    - cron: '*/5 * * * *'
  
  # 手动触发
  workflow_dispatch:
    inputs:
      target_override:
        description: '自定义监控目标 (可选)'
        required: false
        default: ''

env:
  # 监控配置
  MONITOR_TARGETS: 'baidu.com,qq.com,taobao.com,google.com,facebook.com,twitter.com,github.com,stackoverflow.com'
  PING_COUNT: 4
  TIMEOUT: 10
  
  # 报警配置 (在GitHub Secrets中设置)
  # WEBHOOK_URL: ${{ secrets.WEBHOOK_URL }}
  # EMAIL_WEBHOOK: ${{ secrets.EMAIL_WEBHOOK }}
  # TELEGRAM_BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN }}
  # TELEGRAM_CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}

jobs:
  network-monitoring:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
    - name: 📥 Checkout Repository
      uses: actions/checkout@v4
      
    - name: 🔧 Setup Environment
      run: |
        # 安装网络工具
        sudo apt-get update
        sudo apt-get install -y iputils-ping traceroute mtr-tiny curl jq
        
        # 创建结果目录
        mkdir -p results
        mkdir -p logs
        
        # 设置时间戳
        echo "TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')" >> $GITHUB_ENV
        echo "UNIX_TIMESTAMP=$(date +%s)" >> $GITHUB_ENV
        echo "DATE_ONLY=$(date '+%Y-%m-%d')" >> $GITHUB_ENV
        
    - name: 🌐 Execute Network Tests
      run: |
        echo "🚀 开始网络监控测试 - $TIMESTAMP"

        # 获取监控目标
        if [ -n "${{ github.event.inputs.target_override }}" ]; then
          TARGETS="${{ github.event.inputs.target_override }}"
        else
          TARGETS="${{ env.MONITOR_TARGETS }}"
        fi

        # 创建结果文件
        RESULT_FILE="results/network-test-$UNIX_TIMESTAMP.json"
        echo "{\"timestamp\": \"$TIMESTAMP\", \"tests\": []}" > $RESULT_FILE
        
        # 获取测试节点信息
        NODE_INFO=$(curl -s https://ipapi.co/json/ || echo '{"ip":"unknown","city":"unknown","country":"unknown","org":"unknown"}')
        echo "📍 测试节点信息: $NODE_INFO"
        
        # 分割目标并测试
        IFS=',' read -ra TARGET_ARRAY <<< "$TARGETS"
        
        for target in "${TARGET_ARRAY[@]}"; do
          target=$(echo "$target" | xargs) # 去除空格
          echo "🔍 测试目标: $target"
          
          # 执行ping测试
          ping_result=$(ping -c ${{ env.PING_COUNT }} -W ${{ env.TIMEOUT }} "$target" 2>&1)
          ping_exit_code=$?
          
          # 解析ping结果
          if [ $ping_exit_code -eq 0 ]; then
            # 提取延迟信息
            avg_latency=$(echo "$ping_result" | grep "rtt min/avg/max/mdev" | awk -F'/' '{print $5}' | awk '{print $1}')
            min_latency=$(echo "$ping_result" | grep "rtt min/avg/max/mdev" | awk -F'/' '{print $4}')
            max_latency=$(echo "$ping_result" | grep "rtt min/avg/max/mdev" | awk -F'/' '{print $6}')
            packet_loss=$(echo "$ping_result" | grep "packet loss" | awk '{print $6}' | sed 's/%//')
            
            # 如果解析失败，设置默认值
            avg_latency=${avg_latency:-"0"}
            min_latency=${min_latency:-"0"}
            max_latency=${max_latency:-"0"}
            packet_loss=${packet_loss:-"100"}
            
            status="success"
            echo "✅ $target - 平均延迟: ${avg_latency}ms, 丢包率: ${packet_loss}%"
          else
            avg_latency="0"
            min_latency="0"
            max_latency="0"
            packet_loss="100"
            status="failed"
            echo "❌ $target - 测试失败"
          fi
          
          # 执行traceroute (简化版)
          traceroute_hops="0"
          if [ "$status" = "success" ]; then
            traceroute_result=$(traceroute -m 10 -w 3 "$target" 2>/dev/null | wc -l || echo "0")
            traceroute_hops=$((traceroute_result - 1))
          fi
          
          # 网站分类
          if [[ "$target" =~ (baidu|qq|taobao|weibo|zhihu|bilibili)\.com ]]; then
            category="domestic"
          else
            category="international"
          fi
          
          # 构建JSON结果
          test_result=$(cat <<EOF
{
  "target": "$target",
  "category": "$category",
  "status": "$status",
  "avg_latency": $avg_latency,
  "min_latency": $min_latency,
  "max_latency": $max_latency,
  "packet_loss": $packet_loss,
  "traceroute_hops": $traceroute_hops,
  "test_time": "$TIMESTAMP",
  "node_info": $NODE_INFO
}
EOF
)
          
          # 添加到结果文件
          jq --argjson new_test "$test_result" '.tests += [$new_test]' "$RESULT_FILE" > temp.json && mv temp.json "$RESULT_FILE"
          
          # 短暂延迟避免过快请求
          sleep 2
        done
        
        echo "📊 测试完成，结果保存到: $RESULT_FILE"
        
    - name: 📊 Analyze Results
      run: |
        RESULT_FILE="results/network-test-$UNIX_TIMESTAMP.json"
        
        echo "📈 分析测试结果..."
        
        # 统计成功率
        total_tests=$(jq '.tests | length' "$RESULT_FILE")
        successful_tests=$(jq '[.tests[] | select(.status == "success")] | length' "$RESULT_FILE")
        success_rate=$((successful_tests * 100 / total_tests))
        
        # 分类统计
        domestic_avg=$(jq '[.tests[] | select(.category == "domestic" and .status == "success") | .avg_latency] | add / length' "$RESULT_FILE" 2>/dev/null || echo "0")
        international_avg=$(jq '[.tests[] | select(.category == "international" and .status == "success") | .avg_latency] | add / length' "$RESULT_FILE" 2>/dev/null || echo "0")
        
        # 检测异常
        high_latency_count=$(jq '[.tests[] | select(.avg_latency > 1000)] | length' "$RESULT_FILE")
        high_loss_count=$(jq '[.tests[] | select(.packet_loss > 10)] | length' "$RESULT_FILE")
        
        # 生成报告
        cat > "logs/analysis-$UNIX_TIMESTAMP.txt" <<EOF
🌐 网络监控分析报告
时间: $TIMESTAMP
测试节点: GitHub Actions (\$(echo '$NODE_INFO' | jq -r '.city + ", " + .country'))

📊 总体统计:
- 总测试数: $total_tests
- 成功测试: $successful_tests
- 成功率: $success_rate%

📈 延迟统计:
- 国内网站平均延迟: ${domestic_avg}ms
- 国外网站平均延迟: ${international_avg}ms

⚠️ 异常检测:
- 高延迟网站 (>1000ms): $high_latency_count 个
- 高丢包网站 (>10%): $high_loss_count 个

📋 详细结果:
EOF
        
        # 添加详细结果
        jq -r '.tests[] | "- \(.target): \(.status) | 延迟: \(.avg_latency)ms | 丢包: \(.packet_loss)%"' "$RESULT_FILE" >> "logs/analysis-$UNIX_TIMESTAMP.txt"

        echo "📄 分析报告生成完成"
        cat "logs/analysis-$UNIX_TIMESTAMP.txt"
        
        # 设置环境变量供后续步骤使用
        echo "SUCCESS_RATE=$success_rate" >> $GITHUB_ENV
        echo "HIGH_LATENCY_COUNT=$high_latency_count" >> $GITHUB_ENV
        echo "HIGH_LOSS_COUNT=$high_loss_count" >> $GITHUB_ENV
        
    - name: 🚨 Alert Check
      run: |
        echo "🔍 检查是否需要发送报警..."
        
        # 报警条件
        ALERT_NEEDED=false
        ALERT_MESSAGE=""
        
        # 成功率过低
        if [ "$SUCCESS_RATE" -lt 80 ]; then
          ALERT_NEEDED=true
          ALERT_MESSAGE="⚠️ 网络成功率过低: $SUCCESS_RATE%"
        fi

        # 高延迟网站过多
        if [ "$HIGH_LATENCY_COUNT" -gt 2 ]; then
          ALERT_NEEDED=true
          ALERT_MESSAGE="$ALERT_MESSAGE\n⚠️ 高延迟网站过多: $HIGH_LATENCY_COUNT 个"
        fi

        # 高丢包网站过多
        if [ "$HIGH_LOSS_COUNT" -gt 1 ]; then
          ALERT_NEEDED=true
          ALERT_MESSAGE="$ALERT_MESSAGE\n⚠️ 高丢包网站过多: $HIGH_LOSS_COUNT 个"
        fi
        
        echo "ALERT_NEEDED=$ALERT_NEEDED" >> $GITHUB_ENV
        echo "ALERT_MESSAGE<<EOF" >> $GITHUB_ENV
        echo -e "$ALERT_MESSAGE" >> $GITHUB_ENV
        echo "EOF" >> $GITHUB_ENV
        
        if [ "$ALERT_NEEDED" = "true" ]; then
          echo "🚨 需要发送报警通知"
          echo -e "$ALERT_MESSAGE"
        else
          echo "✅ 网络状态正常，无需报警"
        fi
        
    - name: 📤 Send Notifications
      if: env.ALERT_NEEDED == 'true'
      run: |
        echo "📤 发送报警通知..."
        
        # 构建通知消息
        NOTIFICATION_MESSAGE="🚨 网络监控报警\n时间: $TIMESTAMP\n$ALERT_MESSAGE\n\n查看详情: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
        
        # 发送到Webhook (如果配置了)
        if [ -n "${{ secrets.WEBHOOK_URL }}" ]; then
          curl -X POST "${{ secrets.WEBHOOK_URL }}" \
               -H "Content-Type: application/json" \
               -d "{\"text\": \"$NOTIFICATION_MESSAGE\"}" || echo "Webhook发送失败"
        fi
        
        # 发送到Telegram (如果配置了)
        if [ -n "${{ secrets.TELEGRAM_BOT_TOKEN }}" ] && [ -n "${{ secrets.TELEGRAM_CHAT_ID }}" ]; then
          curl -X POST "https://api.telegram.org/bot${{ secrets.TELEGRAM_BOT_TOKEN }}/sendMessage" \
               -d "chat_id=${{ secrets.TELEGRAM_CHAT_ID }}" \
               -d "text=$NOTIFICATION_MESSAGE" \
               -d "parse_mode=HTML" || echo "Telegram发送失败"
        fi
        
        echo "📤 通知发送完成"
        
    - name: 💾 Archive Results
      uses: actions/upload-artifact@v4
      with:
        name: network-monitor-results-${{ env.DATE_ONLY }}
        path: |
          results/
          logs/
        retention-days: 30
        
    - name: 📊 Update Status Badge
      run: |
        # 生成状态徽章数据
        if [ "$SUCCESS_RATE" -ge 95 ]; then
          BADGE_COLOR="brightgreen"
          BADGE_STATUS="excellent"
        elif [ "$SUCCESS_RATE" -ge 85 ]; then
          BADGE_COLOR="green"
          BADGE_STATUS="good"
        elif [ "$SUCCESS_RATE" -ge 70 ]; then
          BADGE_COLOR="yellow"
          BADGE_STATUS="warning"
        else
          BADGE_COLOR="red"
          BADGE_STATUS="critical"
        fi

        # 创建状态文件
        cat > status.json <<EOF
{
  "schemaVersion": 1,
  "label": "Network Status",
  "message": "$SUCCESS_RATE% ($BADGE_STATUS)",
  "color": "$BADGE_COLOR",
  "cacheSeconds": 300
}
EOF
        
        echo "📊 状态徽章数据已生成"
        
    - name: 📝 Generate Summary
      run: |
        echo "## 🌐 网络监控摘要" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**测试时间**: $TIMESTAMP" >> $GITHUB_STEP_SUMMARY
        echo "**成功率**: $SUCCESS_RATE%" >> $GITHUB_STEP_SUMMARY
        echo "**异常检测**: $HIGH_LATENCY_COUNT 个高延迟, $HIGH_LOSS_COUNT 个高丢包" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY

        if [ "$ALERT_NEEDED" = "true" ]; then
          echo "🚨 **报警状态**: 检测到异常" >> $GITHUB_STEP_SUMMARY
        else
          echo "✅ **网络状态**: 正常" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "📊 查看完整结果请下载构建产物" >> $GITHUB_STEP_SUMMARY
