import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // 测试 Globalping API token 是否有效
    const token = process.env.GLOBALPING_API_TOKEN || 'k5k76pvk6lj7omghnfqlk4naib5kbwcd';
    
    console.log('Testing Globalping API with token:', token.substring(0, 8) + '...');
    
    const response = await fetch('https://api.globalping.io/v1/measurements', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'User-Agent': 'PingTool/1.0 (Test)',
      },
      body: JSON.stringify({
        type: 'ping',
        target: 'google.com',
        locations: [{ magic: 'world' }],
        limit: 1,
        measurementOptions: { packets: 1 }
      }),
      signal: AbortSignal.timeout(10000)
    });

    const responseText = await response.text();
    
    return res.status(200).json({
      success: response.ok,
      status: response.status,
      statusText: response.statusText,
      tokenUsed: token.substring(0, 8) + '...',
      envTokenExists: !!process.env.GLOBALPING_API_TOKEN,
      response: response.ok ? JSON.parse(responseText) : responseText,
      headers: Object.fromEntries(response.headers.entries())
    });

  } catch (error) {
    return res.status(500).json({
      error: error instanceof Error ? error.message : 'Unknown error',
      tokenUsed: (process.env.GLOBALPING_API_TOKEN || 'k5k76pvk6lj7omghnfqlk4naib5kbwcd').substring(0, 8) + '...',
      envTokenExists: !!process.env.GLOBALPING_API_TOKEN
    });
  }
}
