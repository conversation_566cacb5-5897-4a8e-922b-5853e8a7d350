#!/usr/bin/env node

/**
 * 🚀 Vercel Redis 初始化脚本
 * 
 * 用于在部署前确保Redis中有正确的访问计数
 * 防止每次部署后访问次数重置
 */

const https = require('https');

async function initVercelRedis() {
  console.log('🔧 初始化 Vercel Redis 访问统计...');

  // 从环境变量获取配置
  const kvRestApiToken = process.env.KV_REST_API_TOKEN;
  const kvUrl = process.env.KV_URL || process.env.REDIS_URL;

  if (!kvRestApiToken || !kvUrl) {
    console.warn('⚠️ 缺少 Redis 配置环境变量，跳过初始化:');
    console.warn('   - KV_REST_API_TOKEN');
    console.warn('   - KV_URL 或 REDIS_URL');
    console.log('✅ 跳过 Redis 初始化，继续构建...');
    process.exit(0); // 成功退出，不阻止构建
  }

  try {
    // 构建 REST API URL
    const restApiUrl = process.env.KV_REST_API_URL || (() => {
      const redisUrl = new URL(kvUrl);
      return `https://${redisUrl.hostname.replace(':6379', '')}`;
    })();

    console.log(`📡 连接到 Redis: ${restApiUrl}`);

    // 检查当前访问计数
    const getCurrentCount = () => {
      return new Promise((resolve, reject) => {
        const url = `${restApiUrl}/get/visit_count`;
        const options = {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${kvRestApiToken}`,
          },
          timeout: 5000
        };

        const req = https.request(url, options, (res) => {
          let data = '';
          res.on('data', (chunk) => data += chunk);
          res.on('end', () => {
            try {
              const result = JSON.parse(data);
              resolve(result.result || null);
            } catch (e) {
              resolve(null);
            }
          });
        });

        req.on('error', reject);
        req.on('timeout', () => reject(new Error('Request timeout')));
        req.end();
      });
    };

    // 设置访问计数
    const setVisitCount = (count) => {
      return new Promise((resolve, reject) => {
        const url = `${restApiUrl}/set/visit_count/${count}`;
        const options = {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${kvRestApiToken}`,
          },
          timeout: 5000
        };

        const req = https.request(url, options, (res) => {
          let data = '';
          res.on('data', (chunk) => data += chunk);
          res.on('end', () => {
            resolve(res.statusCode === 200);
          });
        });

        req.on('error', reject);
        req.on('timeout', () => reject(new Error('Request timeout')));
        req.end();
      });
    };

    // 获取当前计数
    const currentCount = await getCurrentCount();
    console.log(`📊 当前访问计数: ${currentCount || '未设置'}`);

    // 如果没有计数或计数过低，设置合理的初始值
    const minCount = 1267; // 最小访问计数
    let targetCount = currentCount;

    if (!currentCount || currentCount < minCount) {
      targetCount = minCount;
      console.log(`🔄 设置访问计数为: ${targetCount}`);
      
      const success = await setVisitCount(targetCount);
      if (success) {
        console.log('✅ 访问计数初始化成功!');
      } else {
        console.error('❌ 访问计数初始化失败!');
        process.exit(1);
      }
    } else {
      console.log('✅ 访问计数已存在，无需初始化');
    }

    // 验证设置
    const verifyCount = await getCurrentCount();
    console.log(`🔍 验证访问计数: ${verifyCount}`);

    if (verifyCount >= minCount) {
      console.log('🎉 Vercel Redis 初始化完成!');
      process.exit(0);
    } else {
      console.error('❌ 访问计数验证失败!');
      process.exit(1);
    }

  } catch (error) {
    console.warn('⚠️ Redis 初始化失败，但不影响构建:', error.message);
    console.log('✅ 继续构建，运行时将使用降级数据...');
    process.exit(0); // 成功退出，不阻止构建
  }
}

// 运行初始化
if (require.main === module) {
  initVercelRedis();
}

module.exports = { initVercelRedis };
