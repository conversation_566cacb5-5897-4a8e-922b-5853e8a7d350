#!/usr/bin/env node

/**
 * 🧪 分批控制验证脚本
 * 
 * 用于验证延迟分批控制在生产环境中的正确性
 * 可以在本地和 Vercel 部署后运行
 */

const https = require('https');
const http = require('http');

// 测试网站配置
const TEST_SITES = {
  firstBatch: {
    name: '第一批网站（1-100ms）',
    sites: ['wobshare.us.kg', 'baidu.com', 'taobao.com', 'qq.com', 'weibo.com', 'zhihu.com', 'bilibili.com'],
    expectedRange: [1, 100]
  },
  secondBatch: {
    name: '第二批网站（101-200ms）',
    sites: ['freedidi.com', 'bulianglin.com', 'freeaday.com', 'iweec.com', 'lnovel.org', 'acgndog.com', 'mobinovels.com'],
    expectedRange: [101, 200]
  },
  thirdBatch: {
    name: '第三批网站（≥251ms）',
    sites: ['google.com', 'facebook.com', 'twitter.com', 'instagram.com', 'whatsapp.com', 'telegram.org'],
    expectedRange: [251, 500]
  }
};

// HTTP请求函数
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https:') ? https : http;
    
    const req = protocol.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.setTimeout(30000, () => {
      req.destroy();
      reject(new Error('请求超时'));
    });
  });
}

// 验证延迟是否在预期范围内
function validateLatency(latency, expectedRange, siteName) {
  const [min, max] = expectedRange;
  
  if (expectedRange.length === 2 && max === 500) {
    // 第三批：≥251ms
    return latency >= min;
  } else {
    // 第一批和第二批：在指定范围内
    return latency >= min && latency <= max;
  }
}

// 测试单个网站
async function testSite(baseUrl, site, expectedRange, batchName) {
  console.log(`\n🔍 测试 ${site} (${batchName})`);
  
  try {
    // 测试一致性API
    const consistencyUrl = `${baseUrl}/api/test-consistency`;
    console.log(`   📡 调用一致性测试API...`);
    
    const consistencyResult = await makeRequest(consistencyUrl);
    
    if (!consistencyResult.success) {
      throw new Error(`一致性测试失败: ${consistencyResult.error}`);
    }
    
    // 查找当前网站的结果
    const siteResult = consistencyResult.results.find(r => r.site === site);
    
    if (!siteResult) {
      throw new Error(`未找到网站 ${site} 的测试结果`);
    }
    
    console.log(`   ✅ 一致性检查: ${siteResult.isConsistent ? '通过' : '失败'}`);
    console.log(`   ✅ 范围检查: ${siteResult.isInCorrectRange ? '通过' : '失败'}`);
    console.log(`   📊 延迟值: ${siteResult.latencies[0]}ms (期望: ${siteResult.expectedRange})`);
    
    // 测试实际ping API
    const pingUrl = `${baseUrl}/api/ping-real?target=${site}`;
    console.log(`   📡 调用ping API...`);
    
    const pingResult = await makeRequest(pingUrl);
    
    if (!pingResult.success) {
      throw new Error(`Ping测试失败: ${pingResult.error}`);
    }
    
    // 验证所有省份的延迟
    const invalidResults = [];
    
    for (const result of pingResult.results) {
      if (!validateLatency(result.ping, expectedRange, site)) {
        invalidResults.push({
          province: result.province,
          ping: result.ping,
          expected: expectedRange
        });
      }
    }
    
    if (invalidResults.length > 0) {
      console.log(`   ❌ 发现 ${invalidResults.length} 个超出范围的结果:`);
      invalidResults.forEach(r => {
        console.log(`      ${r.province}: ${r.ping}ms (期望: ${expectedRange[0]}-${expectedRange[1] === 500 ? '∞' : expectedRange[1]}ms)`);
      });
      return false;
    }
    
    console.log(`   ✅ 所有省份延迟都在正确范围内`);
    return true;
    
  } catch (error) {
    console.log(`   ❌ 测试失败: ${error.message}`);
    return false;
  }
}

// 主测试函数
async function runBatchControlTest() {
  const baseUrl = process.argv[2] || 'http://localhost:3000';
  
  console.log('🚀 开始分批控制验证测试');
  console.log(`📍 测试目标: ${baseUrl}`);
  console.log(`⏰ 开始时间: ${new Date().toLocaleString()}`);
  
  const results = {
    total: 0,
    passed: 0,
    failed: 0,
    details: {}
  };
  
  // 测试每个批次
  for (const [batchKey, batchConfig] of Object.entries(TEST_SITES)) {
    console.log(`\n📦 ${batchConfig.name}`);
    console.log(`   期望范围: ${batchConfig.expectedRange[0]}-${batchConfig.expectedRange[1] === 500 ? '∞' : batchConfig.expectedRange[1]}ms`);
    
    const batchResults = [];
    
    for (const site of batchConfig.sites) {
      results.total++;
      const success = await testSite(baseUrl, site, batchConfig.expectedRange, batchConfig.name);
      
      if (success) {
        results.passed++;
        batchResults.push({ site, status: 'passed' });
      } else {
        results.failed++;
        batchResults.push({ site, status: 'failed' });
      }
      
      // 添加延迟避免请求过于频繁
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    results.details[batchKey] = batchResults;
    
    const batchPassed = batchResults.filter(r => r.status === 'passed').length;
    const batchTotal = batchResults.length;
    console.log(`   📊 批次结果: ${batchPassed}/${batchTotal} 通过 (${Math.round(batchPassed/batchTotal*100)}%)`);
  }
  
  // 输出总结
  console.log('\n📋 测试总结');
  console.log('='.repeat(50));
  console.log(`总测试数: ${results.total}`);
  console.log(`通过数: ${results.passed}`);
  console.log(`失败数: ${results.failed}`);
  console.log(`成功率: ${Math.round(results.passed/results.total*100)}%`);
  console.log(`完成时间: ${new Date().toLocaleString()}`);
  
  if (results.failed === 0) {
    console.log('\n🎉 所有测试通过！分批控制工作正常。');
    process.exit(0);
  } else {
    console.log('\n❌ 部分测试失败，请检查分批控制逻辑。');
    
    // 输出失败详情
    for (const [batchKey, batchResults] of Object.entries(results.details)) {
      const failedSites = batchResults.filter(r => r.status === 'failed');
      if (failedSites.length > 0) {
        console.log(`\n${TEST_SITES[batchKey].name} 失败的网站:`);
        failedSites.forEach(r => console.log(`  - ${r.site}`));
      }
    }
    
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runBatchControlTest().catch(error => {
    console.error('❌ 测试运行失败:', error.message);
    process.exit(1);
  });
}

module.exports = { runBatchControlTest };
