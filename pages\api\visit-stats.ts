import type { NextApiRequest, NextApiResponse } from 'next'
import { withCors } from '../../src/utils/cors'

const handler = async function(req: NextApiRequest, res: NextApiResponse) {
  // 处理GET请求 - 获取访问统计
  if (req.method === 'GET') {
    try {
      // 🔧 使用 Vercel Redis (Upstash) 配置
      const kvRestApiToken = process.env.KV_REST_API_TOKEN || process.env.KV_REST_API_READ_ONLY_TOKEN
      const kvUrl = process.env.KV_URL || process.env.REDIS_URL

      // 如果没有配置 Redis 环境变量，返回固定的模拟数据
      if (!kvRestApiToken || !kvUrl) {
        return res.status(200).json({
          visits: 1267, // 固定数值，避免跳跃
          lastVisit: new Date().toISOString(),
          lastUpdated: Date.now(),
          source: 'mock_data',
          success: true
        })
      }

      // 构建 Upstash Redis REST API 端点
      // 优先使用配置的 REST API URL，否则从连接字符串构建
      const restApiUrl = process.env.KV_REST_API_URL || (() => {
        const redisUrl = new URL(kvUrl)
        return `https://${redisUrl.hostname.replace(':6379', '')}`
      })()

      // 获取访问统计 - 使用 Upstash Redis REST API
      const response = await fetch(`${restApiUrl}/get/visit_count`, {
        headers: {
          'Authorization': `Bearer ${kvRestApiToken}`,
        },
        signal: AbortSignal.timeout(5000) // 5秒超时
      })

      if (!response.ok) {
        // 降级到固定的模拟数据
        return res.status(200).json({
          visits: 1267, // 固定数值，避免跳跃
          lastVisit: new Date().toISOString(),
          lastUpdated: Date.now(),
          source: 'fallback_data',
          error: `Redis API returned ${response.status}`
        })
      }

      const data = await response.json()
      const visitCount = data.result || 1267 // 如果没有数据，使用默认值

      res.status(200).json({
        visits: visitCount,
        lastVisit: new Date().toISOString(),
        lastUpdated: Date.now(),
        source: 'redis'
      })
    } catch (error) {
      // 降级到固定的模拟数据而不是返回错误
      res.status(200).json({
        visits: 1267, // 固定数值，避免跳跃
        lastVisit: new Date().toISOString(),
        lastUpdated: Date.now(),
        source: 'error_fallback',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  } else if (req.method === 'POST') {
    try {
      // 🔧 使用 Vercel Redis (Upstash) 配置
      const kvRestApiToken = process.env.KV_REST_API_TOKEN
      const kvUrl = process.env.KV_URL || process.env.REDIS_URL

      // 如果没有配置 Redis 环境变量，返回固定计数
      if (!kvRestApiToken || !kvUrl) {
        return res.status(200).json({
          success: true,
          count: 1267, // 固定数值，避免跳跃
          source: 'local_mock'
        })
      }

      // 构建 Upstash Redis REST API 端点
      // 优先使用配置的 REST API URL，否则从连接字符串构建
      const restApiUrl = process.env.KV_REST_API_URL || (() => {
        const redisUrl = new URL(kvUrl)
        return `https://${redisUrl.hostname.replace(':6379', '')}`
      })()

      // 🔧 优化的访问计数 - 使用 Redis INCR 原子操作
      let retryCount = 0
      const maxRetries = 2

      while (retryCount < maxRetries) {
        try {
          // 使用 Redis INCR 命令原子性地增加计数
          const incrResponse = await fetch(`${restApiUrl}/incr/visit_count`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${kvRestApiToken}`,
            },
            signal: AbortSignal.timeout(3000) // 3秒超时
          })

          if (!incrResponse.ok) {
            throw new Error(`Failed to increment visit count: ${incrResponse.status}`)
          }

          const incrData = await incrResponse.json()
          const newCount = incrData.result || 1267

          // 更新最后访问时间
          const timestamp = new Date().toISOString()
          await fetch(`${restApiUrl}/set/last_visit/${timestamp}`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${kvRestApiToken}`,
            },
            signal: AbortSignal.timeout(2000) // 2秒超时，不影响主要功能
          }).catch(() => {}) // 忽略错误，不影响计数功能

          // 成功更新，返回结果
          return res.status(200).json({
            success: true,
            count: newCount,
            visits: newCount,
            lastVisit: timestamp,
            lastUpdated: Date.now(),
            source: 'redis',
            retryAttempt: retryCount
          })

        } catch (error) {
          retryCount++
          if (retryCount >= maxRetries) {
            throw error
          }
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
        }
      }
    } catch (error) {
      // 降级到成功响应，避免影响用户体验
      res.status(200).json({
        success: true,
        count: 1267, // 固定数值，避免跳跃
        source: 'error_fallback',
        error: 'Failed to update visit stats',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST'])
    res.status(405).json({ error: 'Method not allowed' })
  }
}

export default withCors(handler)
