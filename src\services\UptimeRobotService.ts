// UptimeRobot API 集成服务
export class UptimeRobotService {
  private apiKey: string;
  private baseUrl = 'https://api.uptimerobot.com/v2';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  // 创建监控器
  async createMonitor(url: string, friendlyName: string) {
    const response = await fetch(`${this.baseUrl}/newMonitor`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        api_key: this.apiKey,
        format: 'json',
        type: '1', // HTTP(s)
        url: url,
        friendly_name: friendlyName,
        interval: '300' // 5分钟检查一次
      })
    });

    return await response.json();
  }

  // 获取监控器状态
  async getMonitors() {
    const response = await fetch(`${this.baseUrl}/getMonitors`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        api_key: this.apiKey,
        format: 'json',
        response_times: '1',
        response_times_limit: '24' // 最近24小时
      })
    });

    return await response.json();
  }

  // 获取响应时间数据
  async getResponseTimes(monitorId: string) {
    const response = await fetch(`${this.baseUrl}/getMonitors`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        api_key: this.apiKey,
        format: 'json',
        monitors: monitorId,
        response_times: '1',
        response_times_limit: '50'
      })
    });

    const data = await response.json();
    return data.monitors?.[0]?.response_times || [];
  }
}

// 多服务商网络监控聚合器
// 🌐 Pingdom 监控服务
export class PingdomService {
  private apiKey: string;
  private baseUrl = 'https://api.pingdom.com/api/3.1';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async getChecks() {
    const response = await fetch(`${this.baseUrl}/checks`, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
      }
    });
    return await response.json();
  }

  async getCheckResults(checkId: string) {
    const response = await fetch(`${this.baseUrl}/results/${checkId}`, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
      }
    });
    return await response.json();
  }
}

// 🔍 StatusCake 监控服务
export class StatusCakeService {
  private apiKey: string;
  private baseUrl = 'https://app.statuscake.com/API';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async getTests() {
    const response = await fetch(`${this.baseUrl}/Tests/`, {
      headers: {
        'API': this.apiKey,
      }
    });
    return await response.json();
  }

  async getTestData(testId: string) {
    const response = await fetch(`${this.baseUrl}/Tests/Details/?TestID=${testId}`, {
      headers: {
        'API': this.apiKey,
      }
    });
    return await response.json();
  }
}

// 🌍 增强的网络监控聚合器
export class NetworkMonitoringAggregator {
  private services: {
    uptimeRobot?: UptimeRobotService;
    pingdom?: PingdomService;
    statusCake?: StatusCakeService;
  } = {};

  constructor(config: {
    uptimeRobotApiKey?: string;
    pingdomApiKey?: string;
    statusCakeApiKey?: string;
  }) {
    if (config.uptimeRobotApiKey) {
      this.services.uptimeRobot = new UptimeRobotService(config.uptimeRobotApiKey);
    }
    if (config.pingdomApiKey) {
      this.services.pingdom = new PingdomService(config.pingdomApiKey);
    }
    if (config.statusCakeApiKey) {
      this.services.statusCake = new StatusCakeService(config.statusCakeApiKey);
    }
  }

  // 🔄 聚合多个监控服务的数据
  async getAggregatedNetworkData(url: string) {
    const results: any[] = [];
    const promises: Promise<void>[] = [];

    // UptimeRobot数据
    if (this.services.uptimeRobot) {
      promises.push(
        this.services.uptimeRobot.getMonitors()
          .then(monitors => {
            const relevantMonitor = monitors.monitors?.find((m: any) => m.url === url);
            if (relevantMonitor) {
              return this.services.uptimeRobot!.getResponseTimes(relevantMonitor.id)
                .then(responseTimes => {
                  results.push({
                    provider: 'UptimeRobot',
                    data: responseTimes,
                    uptime: relevantMonitor.all_time_uptime_ratio,
                    status: 'success'
                  });
                });
            }
          })
          .catch(error => {
            results.push({
              provider: 'UptimeRobot',
              error: error.message,
              status: 'error'
            });
          })
      );
    }

    // Pingdom数据
    if (this.services.pingdom) {
      promises.push(
        this.services.pingdom.getChecks()
          .then(checks => {
            const relevantCheck = checks.checks?.find((c: any) => c.hostname === url);
            if (relevantCheck) {
              return this.services.pingdom!.getCheckResults(relevantCheck.id)
                .then(checkResults => {
                  results.push({
                    provider: 'Pingdom',
                    data: checkResults,
                    uptime: relevantCheck.status?.uptime || 0,
                    status: 'success'
                  });
                });
            }
          })
          .catch(error => {
            results.push({
              provider: 'Pingdom',
              error: error.message,
              status: 'error'
            });
          })
      );
    }

    // StatusCake数据
    if (this.services.statusCake) {
      promises.push(
        this.services.statusCake.getTests()
          .then(tests => {
            const relevantTest = tests.find((t: any) => t.WebsiteName === url);
            if (relevantTest) {
              return this.services.statusCake!.getTestData(relevantTest.TestID)
                .then(testData => {
                  results.push({
                    provider: 'StatusCake',
                    data: testData,
                    uptime: relevantTest.Uptime || 0,
                    status: 'success'
                  });
                });
            }
          })
          .catch(error => {
            results.push({
              provider: 'StatusCake',
              error: error.message,
              status: 'error'
            });
          })
      );
    }

    // 等待所有请求完成（最多5秒）
    await Promise.allSettled(promises);

    return results;
  }

  // 🎯 获取简化的监控摘要
  async getMonitoringSummary(url: string) {
    const data = await this.getAggregatedNetworkData(url);

    const summary = {
      totalProviders: Object.keys(this.services).length,
      activeProviders: data.filter(d => d.status === 'success').length,
      averageUptime: 0,
      averageResponseTime: 0,
      providers: data.map(d => ({
        name: d.provider,
        status: d.status,
        uptime: d.uptime || 0
      }))
    };

    const successfulData = data.filter(d => d.status === 'success');
    if (successfulData.length > 0) {
      summary.averageUptime = successfulData.reduce((sum, d) => sum + (d.uptime || 0), 0) / successfulData.length;
    }

    return summary;
  }
}
