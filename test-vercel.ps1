# 测试Vercel部署的混合策略API

$apiUrl = "https://ping.wobshare.us.kg/api/hybrid-ping"

# 国内知名网站
$domesticWebsites = @(
    "baidu.com",
    "taobao.com",
    "jd.com",
    "qq.com",
    "weibo.com",
    "zhihu.com",
    "bilibili.com",
    "xiaohongshu.com",
    "163.com",
    "iqiyi.com"
)

# 被墙的国外网站
$blockedWebsites = @(
    "google.com",
    "facebook.com",
    "twitter.com",
    "instagram.com",
    "whatsapp.com",
    "telegram.org",
    "reddit.com",
    "pinterest.com",
    "vimeo.com",
    "quora.com"
)

# 合并所有测试网站
$testWebsites = $domesticWebsites + $blockedWebsites

Write-Host "🚀 开始测试所有网站..." -ForegroundColor Green
Write-Host "📡 API地址: $apiUrl" -ForegroundColor Cyan
Write-Host "📅 测试时间: $(Get-Date -Format 'yyyy-MM-ddTHH:mm:ss.fffZ')" -ForegroundColor Cyan
Write-Host ("=" * 80) -ForegroundColor Yellow

$results = @()

foreach ($website in $testWebsites) {
    Write-Host "`n🔍 测试网站: $website" -ForegroundColor Yellow
    Write-Host ("=" * 50) -ForegroundColor Gray
    
    try {
        $body = @{target = $website} | ConvertTo-Json
        $startTime = Get-Date
        
        $response = Invoke-RestMethod -Uri $apiUrl -Method POST -ContentType "application/json" -Body $body -TimeoutSec 30
        
        $endTime = Get-Date
        $apiLatency = ($endTime - $startTime).TotalMilliseconds
        
        Write-Host "✅ API响应时间: $([math]::Round($apiLatency))ms" -ForegroundColor Green
        Write-Host "🎯 测试方法: $($response.metadata.testMethod)" -ForegroundColor Cyan
        Write-Host "📂 网站类别: $($response.metadata.category)" -ForegroundColor Cyan
        Write-Host "🎯 置信度: $([math]::Round($response.metadata.confidence * 100, 1))%" -ForegroundColor Cyan
        Write-Host "⚡ 平均延迟: $($response.metadata.averageLatency)ms" -ForegroundColor Cyan
        Write-Host "🌍 Vercel区域: $($response.metadata.vercelRegion)" -ForegroundColor Cyan
        
        if ($response.results -and $response.results.Count -gt 0) {
            Write-Host "`n📊 各省份延迟样本 (前5个):" -ForegroundColor Magenta
            for ($i = 0; $i -lt [math]::Min(5, $response.results.Count); $i++) {
                $result = $response.results[$i]
                Write-Host "  $($result.node): $($result.ping)ms" -ForegroundColor White
            }
        }
        
        if ($response.recommendations -and $response.recommendations.Count -gt 0) {
            Write-Host "`n💡 建议:" -ForegroundColor Blue
            foreach ($rec in $response.recommendations) {
                Write-Host "  - $rec" -ForegroundColor White
            }
        }
        
        $results += @{
            website = $website
            success = $true
            apiLatency = [math]::Round($apiLatency)
            averageLatency = $response.metadata.averageLatency
            testMethod = $response.metadata.testMethod
            category = $response.metadata.category
            confidence = $response.metadata.confidence
            vercelRegion = $response.metadata.vercelRegion
        }
        
    } catch {
        Write-Host "❌ 测试失败: $($_.Exception.Message)" -ForegroundColor Red
        $results += @{
            website = $website
            success = $false
            error = $_.Exception.Message
            apiLatency = 0
            averageLatency = 0
        }
    }
    
    # 等待2秒避免过于频繁的请求
    if ($website -ne $testWebsites[-1]) {
        Write-Host "`n⏳ 等待2秒..." -ForegroundColor Gray
        Start-Sleep -Seconds 2
    }
}

# 生成汇总报告
Write-Host "`n`n📋 测试汇总报告" -ForegroundColor Green
Write-Host ("=" * 80) -ForegroundColor Yellow

$successfulTests = $results | Where-Object { $_.success -eq $true }
$failedTests = $results | Where-Object { $_.success -eq $false }

Write-Host "✅ 成功测试: $($successfulTests.Count)/$($results.Count)" -ForegroundColor Green
Write-Host "❌ 失败测试: $($failedTests.Count)/$($results.Count)" -ForegroundColor Red

if ($successfulTests.Count -gt 0) {
    $avgApiLatency = [math]::Round(($successfulTests | Measure-Object -Property apiLatency -Average).Average)
    $avgPingLatency = [math]::Round(($successfulTests | Measure-Object -Property averageLatency -Average).Average)
    
    Write-Host "⚡ 平均API响应时间: ${avgApiLatency}ms" -ForegroundColor Cyan
    Write-Host "🌐 平均ping延迟: ${avgPingLatency}ms" -ForegroundColor Cyan
    
    # 按测试方法分组
    $methodGroups = $successfulTests | Group-Object -Property testMethod
    Write-Host "`n🎯 测试方法分布:" -ForegroundColor Magenta
    foreach ($group in $methodGroups) {
        $avgLatency = [math]::Round(($group.Group | Measure-Object -Property averageLatency -Average).Average)
        Write-Host "  $($group.Name): $($group.Count)个网站, 平均延迟: ${avgLatency}ms" -ForegroundColor White
    }
    
    # 按网站类别分组
    $categoryGroups = $successfulTests | Group-Object -Property category
    Write-Host "`n📂 网站类别分布:" -ForegroundColor Magenta
    foreach ($group in $categoryGroups) {
        $avgLatency = [math]::Round(($group.Group | Measure-Object -Property averageLatency -Average).Average)
        Write-Host "  $($group.Name): $($group.Count)个网站, 平均延迟: ${avgLatency}ms" -ForegroundColor White
    }
}

if ($failedTests.Count -gt 0) {
    Write-Host "`n❌ 失败的测试:" -ForegroundColor Red
    foreach ($failed in $failedTests) {
        Write-Host "  $($failed.website): $($failed.error)" -ForegroundColor White
    }
}

if ($successfulTests.Count -gt 0) {
    Write-Host "`n🌍 Vercel部署区域: $($successfulTests[0].vercelRegion)" -ForegroundColor Cyan
}
Write-Host "📅 测试完成时间: $(Get-Date -Format 'yyyy-MM-ddTHH:mm:ss.fffZ')" -ForegroundColor Cyan
