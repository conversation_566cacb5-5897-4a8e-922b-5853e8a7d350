# 简化的真实Ping API测试脚本

Write-Host "真实Ping API测试" -ForegroundColor Green

$sites = @("baidu.com", "qq.com", "google.com", "wobshare.us.kg")

foreach ($site in $sites) {
    Write-Host "`n测试网站: $site" -ForegroundColor Cyan
    
    # 本地ping
    try {
        $pingResult = ping $site -n 3 2>$null
        if ($pingResult -match "平均 = (\d+)ms") {
            $localPing = [int]$matches[1]
            Write-Host "本地ping: ${localPing}ms" -ForegroundColor White
        } else {
            $localPing = 9999
            Write-Host "本地ping: 超时" -ForegroundColor Red
        }
    } catch {
        $localPing = 9999
        Write-Host "本地ping: 失败" -ForegroundColor Red
    }
    
    # API测试
    try {
        $body = @{ target = $site; maxNodes = 10 } | ConvertTo-Json
        $response = Invoke-RestMethod -Uri "http://localhost:3002/api/real-ping" -Method POST -ContentType "application/json" -Body $body -TimeoutSec 60
        
        $chinaNodes = $response.results | Where-Object { $_.location.country -eq "CN" }
        
        if ($chinaNodes.Count -gt 0) {
            $apiPing = [math]::Round(($chinaNodes | Measure-Object -Property ping -Average).Average)
            Write-Host "API ping (中国节点): ${apiPing}ms" -ForegroundColor White
            
            if ($localPing -eq 9999) {
                if ($apiPing -gt 250) {
                    Write-Host "被墙网站检测正确" -ForegroundColor Green
                } else {
                    Write-Host "被墙网站检测失败" -ForegroundColor Red
                }
            } else {
                $error = [math]::Abs($apiPing - $localPing)
                if ($error -le 20) {
                    Write-Host "准确性合格 (误差: ${error}ms)" -ForegroundColor Green
                } else {
                    Write-Host "准确性不合格 (误差: ${error}ms)" -ForegroundColor Red
                }
            }
        } else {
            Write-Host "未找到中国节点" -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "API调用失败" -ForegroundColor Red
    }
    
    Start-Sleep -Seconds 2
}

Write-Host "`n测试完成！" -ForegroundColor Green
