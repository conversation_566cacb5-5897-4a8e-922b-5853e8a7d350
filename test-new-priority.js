#!/usr/bin/env node

/**
 * 测试新的API优先级：ITDOG + BOCE + Globalping + 17CE + 其他
 * 验证百度和你的网站的延迟数据是否更合理
 */

const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testNewPriority() {
  console.log('🚀 测试新的API优先级...\n');
  
  const testTargets = [
    { name: '百度', url: 'baidu.com' },
    { name: '你的网站', url: 'wobshare.us.kg' },
    { name: 'Google', url: 'google.com' }
  ];

  for (const target of testTargets) {
    console.log(`\n📡 测试 ${target.name} (${target.url})`);
    console.log('='.repeat(50));
    
    try {
      const startTime = Date.now();
      
      const response = await fetch('http://localhost:3001/api/enhanced-ping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          target: target.url,
          maxNodes: 20
        }),
        timeout: 30000
      });

      const endTime = Date.now();
      const data = await response.json();

      if (data.success && data.results) {
        console.log(`✅ 响应时间: ${endTime - startTime}ms`);
        console.log(`📊 总节点数: ${data.results.length}`);
        console.log(`📈 平均延迟: ${data.metadata?.averageLatency || 0}ms`);
        
        // 按API来源分组统计
        const apiSources = {};
        data.results.forEach(result => {
          const source = result.apiSource || 'Unknown';
          if (!apiSources[source]) {
            apiSources[source] = [];
          }
          apiSources[source].push(result.ping);
        });

        console.log('\n🔍 API来源分析:');
        Object.entries(apiSources).forEach(([source, pings]) => {
          const avgPing = Math.round(pings.reduce((sum, ping) => sum + ping, 0) / pings.length);
          const minPing = Math.min(...pings);
          const maxPing = Math.max(...pings);
          console.log(`  ${source}: ${pings.length}个节点, 平均${avgPing}ms (${minPing}-${maxPing}ms)`);
        });

        // 显示前5个最快的节点
        const sortedResults = data.results
          .filter(r => r.status === 'success' && r.ping > 0)
          .sort((a, b) => a.ping - b.ping)
          .slice(0, 5);

        if (sortedResults.length > 0) {
          console.log('\n🏆 最快的5个节点:');
          sortedResults.forEach((result, index) => {
            console.log(`  ${index + 1}. ${result.node} (${result.province}) - ${result.ping}ms [${result.apiSource}]`);
          });
        }

        // 延迟分析
        const successfulResults = data.results.filter(r => r.status === 'success' && r.ping > 0);
        if (successfulResults.length > 0) {
          const avgLatency = Math.round(successfulResults.reduce((sum, r) => sum + r.ping, 0) / successfulResults.length);
          const minLatency = Math.min(...successfulResults.map(r => r.ping));
          const maxLatency = Math.max(...successfulResults.map(r => r.ping));
          
          console.log('\n📈 延迟统计:');
          console.log(`  平均延迟: ${avgLatency}ms`);
          console.log(`  最低延迟: ${minLatency}ms`);
          console.log(`  最高延迟: ${maxLatency}ms`);
          
          // 延迟合理性分析
          if (target.url === 'baidu.com') {
            if (avgLatency > 100) {
              console.log('  ⚠️  百度延迟偏高，可能需要调整算法');
            } else if (avgLatency < 10) {
              console.log('  ⚠️  百度延迟过低，数据可能不真实');
            } else {
              console.log('  ✅ 百度延迟合理');
            }
          } else if (target.url === 'wobshare.us.kg') {
            if (avgLatency > 500) {
              console.log('  ⚠️  你的网站延迟很高，可能是海外服务器');
            } else if (avgLatency > 200) {
              console.log('  ℹ️  你的网站延迟中等，符合海外网站预期');
            } else {
              console.log('  ✅ 你的网站延迟良好');
            }
          }
        }

      } else {
        console.log('❌ 测试失败:', data.error || '未知错误');
      }

    } catch (error) {
      console.log('❌ 请求失败:', error.message);
    }
  }

  console.log('\n✅ 测试完成！');
  console.log('\n📋 总结:');
  console.log('1. 新的API优先级: ITDOG → BOCE → Globalping → 17CE → 其他');
  console.log('2. 已清理所有控制台日志');
  console.log('3. 简化了节点测试结果组件的动态检查逻辑');
}

// 运行测试
if (require.main === module) {
  testNewPriority().catch(console.error);
}

module.exports = { testNewPriority };
