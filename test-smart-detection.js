// 测试智能检测修复
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 30000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data))
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testSmartDetection() {
  console.log('🔍 测试智能检测修复...');
  
  const testSites = [
    { domain: 'baidu.com', name: '百度', expected: '低延迟' },
    { domain: 'google.com', name: 'Google', expected: '高延迟(被墙)' },
    { domain: 'facebook.com', name: 'Facebook', expected: '高延迟(被墙)' }
  ];
  
  for (const site of testSites) {
    console.log(`\n📡 测试 ${site.name} (${site.domain})...`);
    
    try {
      const response = await fetch('http://localhost:3002/api/enhanced-ping', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ target: site.domain }),
        timeout: 30000
      });
      
      const data = await response.json();
      
      if (!data.success) {
        console.log(`❌ ${site.name} - API错误: ${data.error}`);
        continue;
      }
      
      const results = data.results || [];
      const successResults = results.filter(r => r.status === 'success');
      
      if (successResults.length === 0) {
        console.log(`❌ ${site.name} - 无成功结果`);
        continue;
      }
      
      // 分析结果
      const pings = successResults.map(r => r.ping);
      const avgPing = Math.round(pings.reduce((a, b) => a + b, 0) / pings.length);
      const minPing = Math.min(...pings);
      const maxPing = Math.max(...pings);
      
      console.log(`📊 ${site.name} - 平均延迟: ${avgPing}ms (${minPing}ms - ${maxPing}ms)`);
      
      // 按API来源分析
      const apiGroups = {};
      successResults.forEach(result => {
        const source = result.apiSource || '未知';
        if (!apiGroups[source]) {
          apiGroups[source] = [];
        }
        apiGroups[source].push(result);
      });
      
      console.log(`📡 API来源分析:`);
      Object.entries(apiGroups).forEach(([source, sourceResults]) => {
        const sourcePings = sourceResults.map(r => r.ping);
        const sourceAvg = Math.round(sourcePings.reduce((a, b) => a + b, 0) / sourcePings.length);
        const sourceMin = Math.min(...sourcePings);
        const sourceMax = Math.max(...sourcePings);
        
        console.log(`  - ${source}: ${sourceResults.length}个节点, ${sourceMin}-${sourceMax}ms (平均${sourceAvg}ms)`);
        
        // 检查智能检测标识
        const smartDetected = sourceResults.filter(r => 
          r.testMethod && r.testMethod.includes('动态检测') ||
          r.apiSource === '智能检测'
        );
        
        if (smartDetected.length > 0) {
          console.log(`    ✅ 智能检测生效: ${smartDetected.length}个节点`);
        }
        
        // 检查CDN标识
        const cdnDetected = sourceResults.filter(r => 
          r.testMethod && r.testMethod.includes('CDN') ||
          r.note && r.note.includes('CDN')
        );
        
        if (cdnDetected.length > 0) {
          console.log(`    🌐 CDN检测生效: ${cdnDetected.length}个节点`);
        }
      });
      
      // 评估结果质量
      if (site.expected === '低延迟') {
        if (avgPing < 100) {
          console.log(`✅ ${site.name} - 延迟正常 (国内网站)`);
        } else {
          console.log(`⚠️ ${site.name} - 延迟偏高 (${avgPing}ms)`);
        }
      } else if (site.expected === '高延迟(被墙)') {
        if (avgPing > 200) {
          console.log(`✅ ${site.name} - 延迟正常 (被墙网站)`);
        } else {
          console.log(`❌ ${site.name} - 延迟异常低 (${avgPing}ms) - 智能检测可能未生效`);
        }
      }
      
      // 显示前3个结果示例
      console.log(`📋 结果示例:`);
      successResults.slice(0, 3).forEach((result, index) => {
        console.log(`  ${index + 1}. ${result.node} - ${result.ping}ms (${result.apiSource})`);
        if (result.note) {
          console.log(`     📝 ${result.note}`);
        }
      });
      
    } catch (error) {
      console.log(`❌ ${site.name} - 测试失败: ${error.message}`);
    }
    
    // 等待1秒
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n✅ 智能检测测试完成！');
}

testSmartDetection();
