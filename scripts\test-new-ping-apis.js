#!/usr/bin/env node

/**
 * 测试新集成的ping API
 * 验证17CE、Chinaz、Ping.pe、ITDOG等API的功能
 */

const { performMultiCloudPing } = require('../src/services/PingService.ts');

async function testPingAPIs() {
  console.log('🚀 开始测试新集成的ping API...\n');

  // 测试网站列表
  const testSites = [
    { url: 'baidu.com', type: '国内网站', expected: '低延迟' },
    { url: 'google.com', type: '国外网站', expected: '高延迟或超时' },
    { url: 'github.com', type: '国外网站', expected: '中等延迟' },
    { url: 'taobao.com', type: '国内网站', expected: '低延迟' }
  ];

  for (const site of testSites) {
    console.log(`\n📊 测试 ${site.url} (${site.type})`);
    console.log(`预期结果: ${site.expected}`);
    console.log('=' .repeat(50));

    try {
      const startTime = Date.now();
      const result = await performMultiCloudPing(site.url);
      const endTime = Date.now();

      console.log(`✅ 测试完成，耗时: ${endTime - startTime}ms`);
      console.log(`📈 成功: ${result.success}`);
      console.log(`📍 节点数量: ${result.results.length}`);

      if (result.results.length > 0) {
        // 显示前5个最快的节点
        const topResults = result.results
          .filter(r => r.status === 'success')
          .sort((a, b) => a.ping - b.ping)
          .slice(0, 5);

        console.log('\n🏆 最快的5个节点:');
        topResults.forEach((node, index) => {
          console.log(`  ${index + 1}. ${node.node} (${node.province}) - ${node.ping}ms [${node.testMethod}]`);
        });

        // 统计API来源
        const apiStats = {};
        result.results.forEach(node => {
          const method = node.testMethod || '未知';
          apiStats[method] = (apiStats[method] || 0) + 1;
        });

        console.log('\n📊 API来源统计:');
        Object.entries(apiStats).forEach(([method, count]) => {
          console.log(`  ${method}: ${count} 个节点`);
        });

        // 分析延迟分布
        const successResults = result.results.filter(r => r.status === 'success');
        if (successResults.length > 0) {
          const pings = successResults.map(r => r.ping);
          const avgPing = Math.round(pings.reduce((a, b) => a + b, 0) / pings.length);
          const minPing = Math.min(...pings);
          const maxPing = Math.max(...pings);

          console.log('\n📈 延迟统计:');
          console.log(`  平均延迟: ${avgPing}ms`);
          console.log(`  最低延迟: ${minPing}ms`);
          console.log(`  最高延迟: ${maxPing}ms`);

          // 智能判断结果
          const isLowLatency = avgPing < 100;
          const predictedType = isLowLatency ? '国内网站' : '国外网站';
          const isCorrect = predictedType === site.type;
          
          console.log(`\n🤖 智能判断: ${predictedType} ${isCorrect ? '✅' : '❌'}`);
        }
      } else {
        console.log('❌ 没有获取到有效的ping结果');
      }

    } catch (error) {
      console.error(`❌ 测试失败: ${error.message}`);
    }

    console.log('\n' + '='.repeat(80));
  }

  console.log('\n🎉 所有测试完成！');
}

// 运行测试
if (require.main === module) {
  testPingAPIs().catch(console.error);
}

module.exports = { testPingAPIs };
