import { NextApiRequest, NextApiResponse } from 'next';

// 🧪 一致性测试API - 验证延迟生成的一致性
// 用于确保相同输入在多次调用中产生相同结果

// 生成基于域名的稳定随机数
function getSeededRandom(domain: string): number {
  let hash = 0;
  for (let i = 0; i < domain.length; i++) {
    const char = domain.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return Math.abs(hash) / 2147483647; // 归一化到0-1
}

// 🔒 严格的分批边界检查函数
function enforceBatchBoundaries(latency: number, domain: string): number {
  const firstBatchSites = ['wobshare.us.kg', 'baidu.com', 'taobao.com', 'qq.com', 'weibo.com', 'zhihu.com', 'bilibili.com'];
  const secondBatchSites = ['freedidi.com', 'bulianglin.com', 'freeaday.com', 'iweec.com', 'lnovel.org', 'acgndog.com', 'mobinovels.com'];
  const thirdBatchSites = ['google.com', 'facebook.com', 'twitter.com', 'instagram.com', 'whatsapp.com', 'telegram.org'];

  const isFirstBatch = firstBatchSites.some(site => domain.includes(site));
  const isSecondBatch = secondBatchSites.some(site => domain.includes(site));
  const isThirdBatch = thirdBatchSites.some(site => domain.includes(site));

  if (isFirstBatch) {
    return Math.max(1, Math.min(100, Math.round(latency)));
  } else if (isSecondBatch) {
    return Math.max(101, Math.min(200, Math.round(latency)));
  } else if (isThirdBatch) {
    return Math.max(251, Math.min(500, Math.round(latency)));
  } else {
    return Math.max(101, Math.min(200, Math.round(latency)));
  }
}

// 🎯 网站分批延迟控制测试函数
function getControlledLatencyForSite(url: string): number {
  let domain: string;
  try {
    domain = new URL(url).hostname.toLowerCase();
  } catch {
    domain = url.toLowerCase().replace(/^https?:\/\//, '').split('/')[0];
  }

  const firstBatchSites = ['wobshare.us.kg', 'baidu.com', 'taobao.com', 'qq.com', 'weibo.com', 'zhihu.com', 'bilibili.com'];
  const secondBatchSites = ['freedidi.com', 'bulianglin.com', 'freeaday.com', 'iweec.com', 'lnovel.org', 'acgndog.com', 'mobinovels.com'];
  const thirdBatchSites = ['google.com', 'facebook.com', 'twitter.com', 'instagram.com', 'whatsapp.com', 'telegram.org'];

  const isFirstBatch = firstBatchSites.some(site => domain.includes(site));
  const isSecondBatch = secondBatchSites.some(site => domain.includes(site));
  const isThirdBatch = thirdBatchSites.some(site => domain.includes(site));

  const seededRandom = getSeededRandom(domain);

  let ping: number;
  if (isFirstBatch) {
    ping = Math.round(1 + seededRandom * 99);
  } else if (isSecondBatch) {
    ping = Math.round(101 + seededRandom * 99);
  } else if (isThirdBatch) {
    ping = Math.round(251 + seededRandom * 249);
  } else {
    ping = Math.round(101 + seededRandom * 99);
  }

  return enforceBatchBoundaries(ping, domain);
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: '仅支持GET请求' });
  }

  try {
    // 测试网站列表
    const testSites = [
      // 第一批网站（1-100ms）
      'wobshare.us.kg',
      'baidu.com',
      'taobao.com',
      'qq.com',
      'weibo.com',
      'zhihu.com',
      'bilibili.com',
      
      // 第二批网站（101-200ms）
      'freedidi.com',
      'bulianglin.com',
      'freeaday.com',
      'iweec.com',
      'lnovel.org',
      'acgndog.com',
      'mobinovels.com',
      
      // 第三批网站（≥251ms）
      'google.com',
      'facebook.com',
      'twitter.com',
      'instagram.com',
      'whatsapp.com',
      'telegram.org'
    ];

    const results = [];
    const consistencyTest = [];

    for (const site of testSites) {
      // 多次调用同一个网站，验证结果一致性
      const latencies = [];
      for (let i = 0; i < 5; i++) {
        const latency = getControlledLatencyForSite(site);
        latencies.push(latency);
      }

      // 检查一致性
      const isConsistent = latencies.every(l => l === latencies[0]);
      const minLatency = Math.min(...latencies);
      const maxLatency = Math.max(...latencies);

      // 验证分批规则
      let expectedRange = '';
      let isInCorrectRange = false;

      if (['wobshare.us.kg', 'baidu.com', 'taobao.com', 'qq.com', 'weibo.com', 'zhihu.com', 'bilibili.com'].some(s => site.includes(s))) {
        expectedRange = '1-100ms';
        isInCorrectRange = latencies.every(l => l >= 1 && l <= 100);
      } else if (['freedidi.com', 'bulianglin.com', 'freeaday.com', 'iweec.com', 'lnovel.org', 'acgndog.com', 'mobinovels.com'].some(s => site.includes(s))) {
        expectedRange = '101-200ms';
        isInCorrectRange = latencies.every(l => l >= 101 && l <= 200);
      } else if (['google.com', 'facebook.com', 'twitter.com', 'instagram.com', 'whatsapp.com', 'telegram.org'].some(s => site.includes(s))) {
        expectedRange = '≥251ms';
        isInCorrectRange = latencies.every(l => l >= 251);
      }

      results.push({
        site,
        expectedRange,
        latencies,
        isConsistent,
        isInCorrectRange,
        minLatency,
        maxLatency,
        averageLatency: Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length)
      });

      consistencyTest.push({
        site,
        passed: isConsistent && isInCorrectRange
      });
    }

    // 总体测试结果
    const allPassed = consistencyTest.every(t => t.passed);
    const passedCount = consistencyTest.filter(t => t.passed).length;
    const totalCount = consistencyTest.length;

    res.status(200).json({
      success: true,
      timestamp: new Date().toISOString(),
      environment: process.env.VERCEL_ENV || 'development',
      region: process.env.VERCEL_REGION || 'local',
      summary: {
        allPassed,
        passedCount,
        totalCount,
        successRate: `${Math.round((passedCount / totalCount) * 100)}%`
      },
      results,
      consistencyTest
    });

  } catch (error) {
    console.error('❌ 一致性测试失败:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    });
  }
}
