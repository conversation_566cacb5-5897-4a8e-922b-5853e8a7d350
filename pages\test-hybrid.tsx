import { useState } from 'react';

export default function TestHybrid() {
  const [target, setTarget] = useState('');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testWebsites = [
    'baidu.com',
    'sohu.com', 
    'www.linovel.net',
    'www.xygalaxy.com',
    'freedidi.com',
    'hitpaw.com',
    'feejii.com',
    'google.com',
    'reddit.com',
    'bbc.com',
    'pinterest.com',
    'annas-archive.org',
    'archive.org'
  ];

  const testSite = async (site: string) => {
    setLoading(true);
    setTarget(site);
    
    try {
      const response = await fetch('/api/hybrid-ping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ target: site }),
      });
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error('测试失败:', error);
      setResult({ error: '测试失败' });
    } finally {
      setLoading(false);
    }
  };

  const testAllSites = async () => {
    for (const site of testWebsites) {
      await testSite(site);
      await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🎯 混合策略延迟测试</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <input
          type="text"
          value={target}
          onChange={(e) => setTarget(e.target.value)}
          placeholder="输入网站域名 (如: baidu.com)"
          style={{ 
            padding: '10px', 
            width: '300px', 
            marginRight: '10px',
            border: '1px solid #ccc',
            borderRadius: '4px'
          }}
        />
        <button
          onClick={() => testSite(target)}
          disabled={loading || !target}
          style={{
            padding: '10px 20px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          {loading ? '测试中...' : '测试'}
        </button>
        <button
          onClick={testAllSites}
          disabled={loading}
          style={{
            padding: '10px 20px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          测试所有网站
        </button>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>🌐 预设测试网站:</h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
          {testWebsites.map(site => (
            <button
              key={site}
              onClick={() => testSite(site)}
              disabled={loading}
              style={{
                padding: '8px 12px',
                backgroundColor: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              {site}
            </button>
          ))}
        </div>
      </div>

      {result && (
        <div style={{ 
          marginTop: '20px', 
          padding: '20px', 
          backgroundColor: '#f8f9fa', 
          borderRadius: '8px',
          border: '1px solid #dee2e6'
        }}>
          <h3>📊 测试结果: {result.target}</h3>
          
          {result.metadata && (
            <div style={{ marginBottom: '15px' }}>
              <p><strong>🎯 测试方法:</strong> {result.metadata.testMethod}</p>
              <p><strong>📂 网站类别:</strong> {result.metadata.category}</p>
              <p><strong>🎯 置信度:</strong> {(result.metadata.confidence * 100).toFixed(1)}%</p>
              <p><strong>⚡ 平均延迟:</strong> {result.metadata.averageLatency}ms</p>
              <p><strong>🌍 Vercel区域:</strong> {result.metadata.vercelRegion}</p>
            </div>
          )}

          {result.results && result.results.length > 0 && (
            <div>
              <h4>🗺️ 各省份延迟 (前10个):</h4>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
                {result.results.slice(0, 10).map((item: any, index: number) => (
                  <div key={index} style={{ 
                    padding: '10px', 
                    backgroundColor: 'white', 
                    borderRadius: '4px',
                    border: '1px solid #ddd'
                  }}>
                    <strong>{item.node}:</strong> {item.ping}ms
                  </div>
                ))}
              </div>
            </div>
          )}

          {result.recommendations && (
            <div style={{ marginTop: '15px' }}>
              <h4>💡 建议:</h4>
              <ul>
                {result.recommendations.map((rec: string, index: number) => (
                  <li key={index}>{rec}</li>
                ))}
              </ul>
            </div>
          )}

          <details style={{ marginTop: '15px' }}>
            <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>🔍 完整JSON数据</summary>
            <pre style={{ 
              backgroundColor: '#f1f3f4', 
              padding: '10px', 
              borderRadius: '4px',
              overflow: 'auto',
              fontSize: '12px'
            }}>
              {JSON.stringify(result, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  );
}
