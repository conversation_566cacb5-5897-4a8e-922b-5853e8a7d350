// 网络数据校准和机器学习优化系统
export interface NetworkDataPoint {
  timestamp: number;
  target: string;
  province: string;
  city: string;
  isp: string; // 运营商
  networkType: string; // 网络类型
  deviceType: string; // 设备类型
  realLatency: number; // 真实延迟
  predictedLatency: number; // 预测延迟
  accuracy: number; // 准确度
  userAgent: string;
  ipAddress: string;
}

export interface CalibrationModel {
  province: string;
  baseLatency: number;
  ispFactors: Record<string, number>;
  timeFactors: Record<string, number>; // 时间段因子
  targetFactors: Record<string, number>; // 目标网站因子
  accuracy: number;
  lastUpdated: number;
  sampleCount: number;
}

// 网络数据收集器
export class NetworkDataCollector {
  private dataPoints: NetworkDataPoint[] = [];
  private maxDataPoints = 10000; // 最大存储数据点

  // 收集用户真实网络数据
  async collectUserData(
    target: string,
    realLatency: number,
    userInfo: {
      province: string;
      city: string;
      isp: string;
      networkType: string;
      deviceType: string;
      userAgent: string;
      ipAddress: string;
    }
  ) {
    const dataPoint: NetworkDataPoint = {
      timestamp: Date.now(),
      target,
      realLatency,
      predictedLatency: 0, // 稍后填充
      accuracy: 0,
      ...userInfo
    };

    this.dataPoints.push(dataPoint);

    // 限制数据点数量
    if (this.dataPoints.length > this.maxDataPoints) {
      this.dataPoints = this.dataPoints.slice(-this.maxDataPoints);
    }

    // 异步存储到数据库
    this.persistDataPoint(dataPoint);

    return dataPoint;
  }

  // 获取特定地区的历史数据
  getRegionData(province: string, city?: string): NetworkDataPoint[] {
    return this.dataPoints.filter(point => {
      const matchProvince = point.province === province;
      const matchCity = !city || point.city === city;
      return matchProvince && matchCity;
    });
  }

  // 获取特定目标的历史数据
  getTargetData(target: string): NetworkDataPoint[] {
    return this.dataPoints.filter(point => point.target === target);
  }

  private async persistDataPoint(dataPoint: NetworkDataPoint) {
    try {
      // 检查是否在浏览器环境中
      if (typeof window !== 'undefined' && window.localStorage) {
        // 存储到本地存储或发送到服务器
        const stored = localStorage.getItem('networkDataPoints');
        const existingData = stored ? JSON.parse(stored) : [];

        existingData.push(dataPoint);

        // 限制本地存储大小
        if (existingData.length > 1000) {
          existingData.splice(0, existingData.length - 1000);
        }

        localStorage.setItem('networkDataPoints', JSON.stringify(existingData));
      }
    } catch (error) {
      // Failed to persist data point
    }
  }
}

// 机器学习预测模型
export class NetworkLatencyPredictor {
  private models: Map<string, CalibrationModel> = new Map();
  private dataCollector: NetworkDataCollector;

  constructor(dataCollector: NetworkDataCollector) {
    this.dataCollector = dataCollector;
    this.loadModels();
  }

  // 预测网络延迟
  predictLatency(
    target: string,
    province: string,
    city: string,
    isp: string,
    networkType: string,
    currentHour: number
  ): number {
    const modelKey = `${province}-${city}`;
    const model = this.models.get(modelKey);

    if (!model) {
      // 使用默认模型
      return this.getDefaultLatency(province, target);
    }

    let predictedLatency = model.baseLatency;

    // 应用ISP因子
    const ispFactor = model.ispFactors[isp] || 1.0;
    predictedLatency *= ispFactor;

    // 应用时间因子
    const timeKey = this.getTimeKey(currentHour);
    const timeFactor = model.timeFactors[timeKey] || 1.0;
    predictedLatency *= timeFactor;

    // 应用目标网站因子
    const targetFactor = model.targetFactors[target] || 1.0;
    predictedLatency *= targetFactor;

    // 添加网络类型调整
    const networkFactor = this.getNetworkTypeFactor(networkType);
    predictedLatency *= networkFactor;

    return Math.round(predictedLatency);
  }

  // 训练模型
  async trainModel(province: string, city: string) {
    const regionData = this.dataCollector.getRegionData(province, city);
    
    if (regionData.length < 10) {

      return;
    }

    const model: CalibrationModel = {
      province,
      baseLatency: this.calculateBaseLatency(regionData),
      ispFactors: this.calculateISPFactors(regionData),
      timeFactors: this.calculateTimeFactors(regionData),
      targetFactors: this.calculateTargetFactors(regionData),
      accuracy: this.calculateModelAccuracy(regionData),
      lastUpdated: Date.now(),
      sampleCount: regionData.length
    };

    const modelKey = `${province}-${city}`;
    this.models.set(modelKey, model);

    // 持久化模型
    this.saveModel(modelKey, model);

    // console.log removed}%`);
  }

  private calculateBaseLatency(data: NetworkDataPoint[]): number {
    const latencies = data.map(d => d.realLatency);
    return latencies.reduce((a, b) => a + b, 0) / latencies.length;
  }

  private calculateISPFactors(data: NetworkDataPoint[]): Record<string, number> {
    const ispGroups = this.groupBy(data, 'isp');
    const baseLatency = this.calculateBaseLatency(data);
    const factors: Record<string, number> = {};

    for (const [isp, points] of Object.entries(ispGroups)) {
      const avgLatency = points.reduce((sum, p) => sum + p.realLatency, 0) / points.length;
      factors[isp] = avgLatency / baseLatency;
    }

    return factors;
  }

  private calculateTimeFactors(data: NetworkDataPoint[]): Record<string, number> {
    const timeGroups: Record<string, NetworkDataPoint[]> = {};
    const baseLatency = this.calculateBaseLatency(data);

    // 按时间段分组
    data.forEach(point => {
      const hour = new Date(point.timestamp).getHours();
      const timeKey = this.getTimeKey(hour);
      
      if (!timeGroups[timeKey]) {
        timeGroups[timeKey] = [];
      }
      timeGroups[timeKey].push(point);
    });

    const factors: Record<string, number> = {};
    for (const [timeKey, points] of Object.entries(timeGroups)) {
      const avgLatency = points.reduce((sum, p) => sum + p.realLatency, 0) / points.length;
      factors[timeKey] = avgLatency / baseLatency;
    }

    return factors;
  }

  private calculateTargetFactors(data: NetworkDataPoint[]): Record<string, number> {
    const targetGroups = this.groupBy(data, 'target');
    const baseLatency = this.calculateBaseLatency(data);
    const factors: Record<string, number> = {};

    for (const [target, points] of Object.entries(targetGroups)) {
      const avgLatency = points.reduce((sum, p) => sum + p.realLatency, 0) / points.length;
      factors[target] = avgLatency / baseLatency;
    }

    return factors;
  }

  private calculateModelAccuracy(data: NetworkDataPoint[]): number {
    let totalError = 0;
    let validPredictions = 0;

    data.forEach(point => {
      if (point.predictedLatency > 0) {
        const error = Math.abs(point.realLatency - point.predictedLatency) / point.realLatency;
        totalError += error;
        validPredictions++;
      }
    });

    if (validPredictions === 0) return 0;

    const avgError = totalError / validPredictions;
    return Math.max(0, (1 - avgError) * 100);
  }

  private groupBy<T>(array: T[], key: keyof T): Record<string, T[]> {
    return array.reduce((groups, item) => {
      const groupKey = String(item[key]);
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(item);
      return groups;
    }, {} as Record<string, T[]>);
  }

  private getTimeKey(hour: number): string {
    if (hour >= 0 && hour < 6) return 'night';
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 18) return 'afternoon';
    return 'evening';
  }

  private getNetworkTypeFactor(networkType: string): number {
    const factors: Record<string, number> = {
      '5G': 0.7,
      '4G': 1.0,
      'WiFi': 0.8,
      '3G': 1.5,
      '2G': 3.0
    };
    return factors[networkType] || 1.0;
  }

  private getDefaultLatency(province: string, target: string): number {
    // 基于地理位置和目标的默认延迟
    const provinceFactors: Record<string, number> = {
      '北京': 50, '上海': 55, '广东': 60, '浙江': 65,
      '江苏': 70, '山东': 75, '河北': 80, '河南': 85,
      '湖北': 90, '湖南': 95, '四川': 100, '重庆': 105,
      '陕西': 110, '山西': 115, '安徽': 120, '江西': 125,
      '福建': 130, '广西': 135, '云南': 140, '贵州': 145,
      '甘肃': 150, '青海': 160, '宁夏': 165, '新疆': 180,
      '西藏': 200, '内蒙古': 170, '黑龙江': 175, '吉林': 180,
      '辽宁': 185, '海南': 190, '台湾': 250, '香港': 200, '澳门': 210
    };

    const baseLatency = provinceFactors[province] || 100;

    // 目标网站调整
    const targetFactors: Record<string, number> = {
      'baidu.com': 0.8,
      'qq.com': 0.9,
      'taobao.com': 0.85,
      'google.com': 2.5,
      'facebook.com': 3.0,
      'twitter.com': 2.8,
      'youtube.com': 2.2,
      'github.com': 1.8
    };

    const domain = new URL(target).hostname.replace('www.', '');
    const targetFactor = targetFactors[domain] || 1.0;

    return Math.round(baseLatency * targetFactor);
  }

  private loadModels() {
    try {
      // 检查是否在浏览器环境中
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem('networkModels');
        if (stored) {
          const modelsData = JSON.parse(stored);
          for (const [key, model] of Object.entries(modelsData)) {
            this.models.set(key, model as CalibrationModel);
          }
        }
      }
    } catch (error) {
      // console.warn removed
    }
  }

  private saveModel(key: string, model: CalibrationModel) {
    try {
      // 检查是否在浏览器环境中
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem('networkModels');
        const models = stored ? JSON.parse(stored) : {};
        models[key] = model;
        localStorage.setItem('networkModels', JSON.stringify(models));
      }
    } catch (error) {

    }
  }

  // 获取模型统计信息
  getModelStats() {
    const stats = Array.from(this.models.entries()).map(([key, model]) => ({
      region: key,
      accuracy: model.accuracy,
      sampleCount: model.sampleCount,
      lastUpdated: new Date(model.lastUpdated).toLocaleString()
    }));

    return stats.sort((a, b) => b.accuracy - a.accuracy);
  }
}
