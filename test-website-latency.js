#!/usr/bin/env node

/**
 * 测试指定网站列表的延迟数据
 * 验证新API优先级的效果
 */

const http = require('http');

// 测试网站列表
const testSites = {
  domestic: [
    { name: '你的网站', url: 'wobshare.us.kg', expected: '100-500ms (海外服务器)' },
    { name: '百度', url: 'baidu.com', expected: '20-80ms (国内优质)' },
    { name: '淘宝', url: 'taobao.com', expected: '20-80ms (国内优质)' },
    { name: '腾讯网', url: 'qq.com', expected: '20-80ms (国内优质)' },
    { name: '新浪微博', url: 'weibo.com', expected: '20-80ms (国内优质)' },
    { name: '知乎', url: 'zhihu.com', expected: '30-100ms (国内)' },
    { name: '<PERSON><PERSON><PERSON><PERSON>', url: 'bilibili.com', expected: '20-80ms (国内优质)' }
  ],
  international: [
    { name: 'Google', url: 'google.com', expected: '200-800ms (被墙/CDN)' },
    { name: 'Facebook', url: 'facebook.com', expected: '300-999ms (被墙)' },
    { name: 'Twitter(X)', url: 'twitter.com', expected: '300-999ms (被墙)' },
    { name: 'Instagram', url: 'instagram.com', expected: '300-999ms (被墙)' },
    { name: 'WhatsApp', url: 'whatsapp.com', expected: '300-999ms (被墙)' },
    { name: 'Telegram', url: 'telegram.org', expected: '300-999ms (被墙)' }
  ]
};

function makeRequest(data) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/enhanced-ping',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      },
      timeout: 45000
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(responseData);
          resolve(result);
        } catch (error) {
          reject(new Error('Invalid JSON response'));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.write(postData);
    req.end();
  });
}

async function testWebsite(site) {
  console.log(`\n🌐 测试 ${site.name} (${site.url})`);
  console.log(`📋 预期延迟: ${site.expected}`);
  console.log('='.repeat(60));
  
  try {
    const startTime = Date.now();
    
    const result = await makeRequest({
      target: site.url,
      maxNodes: 20
    });

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    if (result.success && result.results) {
      // 统计API来源
      const apiSources = {};
      const validResults = result.results.filter(r => r.status === 'success' && r.ping > 0);
      
      validResults.forEach(r => {
        const source = r.apiSource || 'Unknown';
        if (!apiSources[source]) {
          apiSources[source] = { count: 0, pings: [] };
        }
        apiSources[source].count++;
        apiSources[source].pings.push(r.ping);
      });

      // 计算统计数据
      const avgLatency = validResults.length > 0 
        ? Math.round(validResults.reduce((sum, r) => sum + r.ping, 0) / validResults.length)
        : 0;
      const minLatency = validResults.length > 0 ? Math.min(...validResults.map(r => r.ping)) : 0;
      const maxLatency = validResults.length > 0 ? Math.max(...validResults.map(r => r.ping)) : 0;

      console.log(`✅ 响应时间: ${responseTime}ms`);
      console.log(`📊 总节点数: ${result.results.length} (成功: ${validResults.length})`);
      console.log(`📈 延迟统计: 平均${avgLatency}ms, 范围${minLatency}-${maxLatency}ms`);
      
      // API来源分析
      console.log('\n🔍 API来源分析:');
      Object.entries(apiSources).forEach(([source, data]) => {
        const avgPing = Math.round(data.pings.reduce((sum, ping) => sum + ping, 0) / data.pings.length);
        console.log(`  ${source}: ${data.count}个节点, 平均${avgPing}ms`);
      });

      // 最快的5个节点
      const topNodes = validResults.sort((a, b) => a.ping - b.ping).slice(0, 5);
      if (topNodes.length > 0) {
        console.log('\n🏆 最快的5个节点:');
        topNodes.forEach((node, index) => {
          console.log(`  ${index + 1}. ${node.node} (${node.province}) - ${node.ping}ms [${node.apiSource}]`);
        });
      }

      // 延迟合理性分析
      console.log('\n📋 延迟分析:');
      if (site.url.includes('baidu.com') || site.url.includes('taobao.com') || 
          site.url.includes('qq.com') || site.url.includes('weibo.com') || 
          site.url.includes('bilibili.com')) {
        // 国内网站
        if (avgLatency > 150) {
          console.log(`  ⚠️  延迟偏高 (${avgLatency}ms)，国内网站应该在20-80ms`);
        } else if (avgLatency < 10) {
          console.log(`  ⚠️  延迟过低 (${avgLatency}ms)，数据可能不真实`);
        } else {
          console.log(`  ✅ 延迟合理 (${avgLatency}ms)，符合国内网站预期`);
        }
      } else if (site.url.includes('wobshare.us.kg')) {
        // 你的网站
        if (avgLatency > 600) {
          console.log(`  ⚠️  延迟很高 (${avgLatency}ms)，可能网络状况不佳`);
        } else if (avgLatency > 200) {
          console.log(`  ✅ 延迟合理 (${avgLatency}ms)，符合海外网站预期`);
        } else {
          console.log(`  ℹ️  延迟较低 (${avgLatency}ms)，可能有CDN加速`);
        }
      } else {
        // 国外网站
        if (avgLatency > 800) {
          console.log(`  ✅ 延迟很高 (${avgLatency}ms)，符合被墙网站预期`);
        } else if (avgLatency > 300) {
          console.log(`  ✅ 延迟较高 (${avgLatency}ms)，符合国外网站预期`);
        } else if (avgLatency > 100) {
          console.log(`  ℹ️  延迟中等 (${avgLatency}ms)，可能有CDN或代理`);
        } else {
          console.log(`  ⚠️  延迟过低 (${avgLatency}ms)，数据可能不准确`);
        }
      }

    } else {
      console.log('❌ 测试失败:', result.error || '未知错误');
    }

  } catch (error) {
    console.log('❌ 请求失败:', error.message);
  }
}

async function runAllTests() {
  console.log('🚀 开始测试网站延迟数据...');
  console.log('📋 新API优先级: ITDOG → BOCE → Globalping → 17CE → 其他\n');

  // 测试国内网站
  console.log('🇨🇳 测试国内网站:');
  for (const site of testSites.domestic) {
    await testWebsite(site);
    await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒避免请求过频
  }

  // 测试国外网站
  console.log('\n\n🌍 测试国外网站:');
  for (const site of testSites.international) {
    await testWebsite(site);
    await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒避免请求过频
  }

  console.log('\n\n✅ 所有测试完成！');
  console.log('\n📊 总结:');
  console.log('1. 国内网站延迟应该在20-80ms范围内');
  console.log('2. 你的网站(海外)延迟应该在100-500ms范围内');
  console.log('3. 被墙的国外网站延迟应该在300-999ms范围内');
  console.log('4. 如果延迟数据不合理，说明需要进一步调整算法');
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, testWebsite };
