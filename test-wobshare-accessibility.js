// 测试wobshare.us.kg的可访问性
const https = require('https');
const http = require('http');

function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 10000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          json: () => Promise.resolve(JSON.parse(data)),
          text: () => Promise.resolve(data)
        });
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testWebsiteAccessibility() {
  console.log('🔍 测试 wobshare.us.kg 可访问性...');
  
  // 1. 直接访问测试
  console.log('\n📡 直接访问测试...');
  try {
    const start = Date.now();
    const response = await fetch('https://wobshare.us.kg/', { timeout: 15000 });
    const end = Date.now();
    
    console.log(`✅ 直接访问成功: ${response.status} (${end - start}ms)`);
    
    if (response.ok) {
      const content = await response.text();
      console.log(`📄 内容长度: ${content.length} 字符`);
      console.log(`🔍 是否包含HTML: ${content.includes('<html>') || content.includes('<!DOCTYPE')}`);
    }
  } catch (error) {
    console.log(`❌ 直接访问失败: ${error.message}`);
  }
  
  // 2. 测试域名解析
  console.log('\n🌐 DNS解析测试...');
  try {
    const dns = require('dns').promises;
    const addresses = await dns.resolve4('wobshare.us.kg');
    console.log(`✅ DNS解析成功: ${addresses.join(', ')}`);
  } catch (error) {
    console.log(`❌ DNS解析失败: ${error.message}`);
  }
  
  // 3. 测试我们的Globalping API
  console.log('\n📡 测试Globalping API...');
  try {
    const start = Date.now();
    const response = await fetch('http://localhost:3001/api/globalping-proxy', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url: 'wobshare.us.kg' }),
      timeout: 20000
    });
    const end = Date.now();
    
    const data = await response.json();
    console.log(`✅ Globalping API响应: ${end - start}ms`);
    console.log(`📊 返回节点数: ${data.results?.length || 0}`);
    console.log(`🔧 数据源: ${data.source}`);
    
    if (data.results && data.results.length > 0) {
      console.log('\n📋 Globalping结果详情:');
      data.results.forEach((result, index) => {
        const status = result.status === 'success' ? '✅' : '❌';
        console.log(`${index + 1}. ${status} ${result.node} - ${result.province} - ${result.ping}ms`);
      });
      
      // 统计成功率
      const successCount = data.results.filter(r => r.status === 'success').length;
      const successRate = Math.round((successCount / data.results.length) * 100);
      console.log(`\n📈 成功率: ${successCount}/${data.results.length} (${successRate}%)`);
      
      // 分析延迟分布
      const successResults = data.results.filter(r => r.status === 'success');
      if (successResults.length > 0) {
        const pings = successResults.map(r => r.ping);
        const avgPing = Math.round(pings.reduce((a, b) => a + b, 0) / pings.length);
        const minPing = Math.min(...pings);
        const maxPing = Math.max(...pings);
        
        console.log(`⚡ 延迟统计: 最低${minPing}ms, 最高${maxPing}ms, 平均${avgPing}ms`);
        
        if (avgPing > 200) {
          console.log('⚠️ 平均延迟较高，可能存在网络限制');
        }
      }
    }
  } catch (error) {
    console.log(`❌ Globalping API失败: ${error.message}`);
  }
  
  // 4. 对比测试其他网站
  console.log('\n🔄 对比测试 google.com...');
  try {
    const start = Date.now();
    const response = await fetch('http://localhost:3001/api/globalping-proxy', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url: 'google.com' }),
      timeout: 20000
    });
    const end = Date.now();
    
    const data = await response.json();
    console.log(`✅ Google测试响应: ${end - start}ms`);
    console.log(`📊 Google节点数: ${data.results?.length || 0}`);
    
    if (data.results && data.results.length > 0) {
      const successCount = data.results.filter(r => r.status === 'success').length;
      const successRate = Math.round((successCount / data.results.length) * 100);
      console.log(`📈 Google成功率: ${successCount}/${data.results.length} (${successRate}%)`);
      
      const successResults = data.results.filter(r => r.status === 'success');
      if (successResults.length > 0) {
        const avgPing = Math.round(successResults.reduce((sum, r) => sum + r.ping, 0) / successResults.length);
        console.log(`⚡ Google平均延迟: ${avgPing}ms`);
      }
    }
  } catch (error) {
    console.log(`❌ Google测试失败: ${error.message}`);
  }
  
  console.log('\n✅ 可访问性测试完成！');
}

testWebsiteAccessibility();
