import { NextApiRequest, NextApiResponse } from 'next';

// 🎯 实用客户端ping API - 真正可部署的方案
// 支持客户端真实测试数据上传和智能省份结果生成

// 生成基于域名的稳定随机数
function getSeededRandom(domain: string): number {
  let hash = 0;
  for (let i = 0; i < domain.length; i++) {
    const char = domain.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  return Math.abs(hash) / 2147483647; // 归一化到0-1
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'GET' && req.method !== 'POST') {
    return res.status(405).json({ error: '仅支持GET和POST请求' });
  }

  try {
    // 获取参数
    const { target, clientResults, generateProvinces = true } = 
      req.method === 'GET' ? req.query : req.body;

    if (!target) {
      return res.status(400).json({ 
        error: '缺少目标URL参数',
        usage: 'GET /api/ping-practical?target=example.com 或 POST {"target": "example.com", "clientResults": [...]}'
      });
    }

    console.log(`🎯 实用ping API请求: ${target}`);

    let results = [];
    let metadata = {
      testType: 'server-estimation',
      timestamp: Date.now(),
      source: 'practical-ping-api',
      region: process.env.VERCEL_REGION || 'hkg1'
    };

    // 优先使用客户端传来的真实测试结果
    if (clientResults && Array.isArray(clientResults) && clientResults.length > 0) {
      console.log(`📱 使用客户端真实测试结果: ${clientResults.length} 个数据点`);
      results = await processClientResults(target as string, clientResults);
      metadata.testType = 'client-real-data';
      metadata.source = 'client-browser';
    } else {
      console.log(`🧠 生成智能估算结果`);
      // 生成基于算法的智能估算结果
      results = await generateSmartEstimation(target as string);
      metadata.testType = 'smart-estimation';
    }

    // 注意：generateSmartEstimation 和 processClientResults 已经包含了省份结果生成
    // 这里不需要再次调用 generateProvinceResults

    console.log(`✅ 实用ping测试完成: ${results.length} 个结果`);

    return res.status(200).json({
      success: true,
      target,
      results,
      metadata: {
        ...metadata,
        nodeCount: results.length,
        architecture: 'Practical Client-Side Ping',
        realData: clientResults && clientResults.length > 0
      }
    });

  } catch (error) {
    console.error('❌ 实用ping测试失败:', error);
    
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      results: generateFallbackResults(req.query.target as string || req.body?.target || 'unknown')
    });
  }
}

// 📱 处理客户端真实测试结果
async function processClientResults(target: string, clientResults: any[]): Promise<any[]> {
  console.log(`🔄 处理客户端测试结果: ${clientResults.length} 条`);
  
  // 分析客户端测试结果
  const successfulResults = clientResults.filter(r => r.status === 'success' && r.reliable);
  const baseLatency = successfulResults.length > 0 
    ? Math.min(...successfulResults.map(r => r.latency))
    : 999;

  const isBlocked = successfulResults.length === 0 || baseLatency > 800;
  const isDomestic = isDomesticSite(target) || (baseLatency < 150 && !isBlocked);

  console.log(`📊 分析结果: 基准延迟=${baseLatency}ms, 被墙=${isBlocked}, 国内=${isDomestic}`);

  // 基于真实测试结果生成省份数据
  return generateProvinceResults(target, [], baseLatency, isBlocked, isDomestic, 0.9);
}

// 🧠 生成智能估算结果
async function generateSmartEstimation(target: string): Promise<any[]> {
  console.log(`🧠 为 ${target} 生成智能估算`);
  
  // 智能网站分类
  const domain = new URL(target).hostname.toLowerCase();
  const isDomestic = isDomesticSite(domain);
  const isBlocked = isBlockedSite(domain);

  console.log(`🔍 网站分析: domain=${domain}, 国内=${isDomestic}, 被墙=${isBlocked}`);

  // 基于网站类型估算基准延迟 - 使用种子随机数确保一致性
  const domainSeed = getSeededRandom(domain);
  let baseLatency: number;
  if (isBlocked) {
    baseLatency = 999; // 被墙网站直接设为超时
  } else if (isDomestic) {
    baseLatency = 30 + domainSeed * 50; // 国内网站 30-80ms
  } else {
    baseLatency = 150 + domainSeed * 100; // 国外网站 150-250ms
  }

  console.log(`🎯 智能估算: 基准延迟=${Math.round(baseLatency)}ms, 被墙=${isBlocked}, 国内=${isDomestic}`);

  return generateProvinceResults(target, [], baseLatency, isBlocked, isDomestic, 0.6);
}

// 🗺️ 生成省份级别的ping结果
async function generateProvinceResults(
  target: string, 
  existingResults: any[] = [], 
  baseLatency: number = 100,
  isBlocked: boolean = false,
  isDomestic: boolean = false,
  confidence: number = 0.7
): Promise<any[]> {
  
  // 中国34个省级行政区（基于真实网络基础设施）
  const provinces = [
    { name: '北京', tier: 1, multiplier: 0.85, quality: 'excellent' },
    { name: '上海', tier: 1, multiplier: 0.90, quality: 'excellent' },
    { name: '广东', tier: 1, multiplier: 0.95, quality: 'excellent' },
    { name: '浙江', tier: 2, multiplier: 1.00, quality: 'good' },
    { name: '江苏', tier: 2, multiplier: 0.95, quality: 'good' },
    { name: '山东', tier: 2, multiplier: 1.05, quality: 'good' },
    { name: '河南', tier: 2, multiplier: 1.10, quality: 'average' },
    { name: '四川', tier: 2, multiplier: 1.15, quality: 'average' },
    { name: '湖北', tier: 2, multiplier: 1.05, quality: 'good' },
    { name: '湖南', tier: 2, multiplier: 1.10, quality: 'average' },
    { name: '河北', tier: 3, multiplier: 1.15, quality: 'average' },
    { name: '福建', tier: 2, multiplier: 1.00, quality: 'good' },
    { name: '安徽', tier: 3, multiplier: 1.20, quality: 'average' },
    { name: '陕西', tier: 2, multiplier: 1.25, quality: 'average' },
    { name: '辽宁', tier: 3, multiplier: 1.30, quality: 'average' },
    { name: '重庆', tier: 2, multiplier: 1.15, quality: 'good' },
    { name: '天津', tier: 1, multiplier: 0.90, quality: 'good' },
    { name: '江西', tier: 3, multiplier: 1.25, quality: 'average' },
    { name: '广西', tier: 3, multiplier: 1.35, quality: 'average' },
    { name: '山西', tier: 3, multiplier: 1.40, quality: 'poor' },
    { name: '吉林', tier: 4, multiplier: 1.45, quality: 'poor' },
    { name: '云南', tier: 3, multiplier: 1.50, quality: 'poor' },
    { name: '贵州', tier: 4, multiplier: 1.60, quality: 'poor' },
    { name: '新疆', tier: 4, multiplier: 2.00, quality: 'poor' },
    { name: '甘肃', tier: 4, multiplier: 1.70, quality: 'poor' },
    { name: '内蒙古', tier: 4, multiplier: 1.80, quality: 'poor' },
    { name: '黑龙江', tier: 4, multiplier: 1.55, quality: 'poor' },
    { name: '宁夏', tier: 4, multiplier: 1.90, quality: 'poor' },
    { name: '青海', tier: 4, multiplier: 2.10, quality: 'poor' },
    { name: '海南', tier: 3, multiplier: 1.40, quality: 'average' },
    { name: '西藏', tier: 4, multiplier: 2.50, quality: 'poor' },
    { name: '香港', tier: 1, multiplier: 0.75, quality: 'excellent' },
    { name: '澳门', tier: 1, multiplier: 0.80, quality: 'excellent' },
    { name: '台湾', tier: 1, multiplier: 0.85, quality: 'excellent' }
  ];

  return provinces.map((province, index) => {
    let adjustedLatency = baseLatency * province.multiplier;

    // 根据网站类型调整
    if (isBlocked) {
      // 被墙网站：大部分省份超时，少数有连接
      // 使用省份索引作为种子，确保结果一致性
      const isTimeout = (index % 5) !== 0; // 80%概率超时 (4/5)

      if (isTimeout) {
        adjustedLatency = 999; // 超时
      } else {
        // 使用省份名称和域名生成种子随机数
        const provinceSeed = getSeededRandom(domain + province.name + index.toString());
        adjustedLatency = Math.max(adjustedLatency, 800) + provinceSeed * 500;
      }
    } else if (isDomestic) {
      // 国内网站：延迟较低且稳定
      adjustedLatency = Math.min(adjustedLatency, 200);
    } else {
      // 国外网站：根据省份网络质量差异较大
      const qualityMultiplier = {
        'excellent': 0.8,
        'good': 0.9,
        'average': 1.1,
        'poor': 1.3
      }[province.quality] || 1.0;
      
      adjustedLatency *= qualityMultiplier;
    }
    
    // 记录是否为超时状态（在添加波动之前）
    const isTimeoutLatency = adjustedLatency >= 999;

    // 添加真实的网络波动 (±15%)，但不对超时值添加波动
    if (!isTimeoutLatency) {
      // 使用省份名称和域名生成种子随机数确保一致性
      const variationSeed = getSeededRandom(domain + province.name + 'variation');
      const variation = (variationSeed - 0.5) * 0.3;
      adjustedLatency *= (1 + variation);
    }

    // 确保最小值为1ms
    const finalLatency = Math.round(Math.max(adjustedLatency, 1));

    // 确定状态
    let status: 'success' | 'timeout' | 'blocked';
    if (isTimeoutLatency || finalLatency >= 999) {
      status = 'timeout';
    } else if (finalLatency > 500 && isBlocked) {
      status = 'blocked';
    } else {
      status = 'success';
    }

    return {
      node: `${province.name}-智能检测`,
      province: province.name,
      ping: finalLatency,
      status,
      timestamp: Date.now(),
      location: {
        city: province.name,
        country: province.name === '香港' ? 'HK' : (province.name === '台湾' ? 'TW' : 'CN'),
        region: province.name,
        province: province.name,
        latitude: 0,
        longitude: 0,
        asn: 0,
        network: '智能估算'
      },
      apiSource: 'PracticalPing',
      testMethod: '客户端智能估算',
      priority: 1,
      confidence: confidence,
      networkTier: province.tier,
      networkQuality: province.quality
    };
  });
}

// 🏠 判断是否为国内网站
function isDomesticSite(domain: string): boolean {
  const domesticDomains = [
    'baidu.com', 'qq.com', 'taobao.com', 'jd.com', 'alibaba.com',
    'zhihu.com', 'weibo.com', 'bilibili.com', 'xiaohongshu.com', 'meituan.com'
  ];
  return domesticDomains.some(d => domain.includes(d)) || domain.endsWith('.cn');
}

// 🚫 判断是否为被墙网站
function isBlockedSite(domain: string): boolean {
  const blockedDomains = [
    'google.com', 'youtube.com', 'facebook.com', 'twitter.com',
    'wikipedia.org', 'netflix.com', 'reddit.com', 'github.com', 'linkedin.com'
  ];
  return blockedDomains.some(d => domain.includes(d));
}

// 🆘 生成兜底结果
function generateFallbackResults(target: string): any[] {
  return [{
    node: '兜底检测',
    province: '未知',
    ping: 999,
    status: 'error',
    timestamp: Date.now(),
    location: {
      city: '未知',
      country: 'CN',
      region: '未知',
      province: '未知',
      latitude: 0,
      longitude: 0,
      asn: 0,
      network: '兜底网络'
    },
    apiSource: 'Fallback',
    testMethod: '兜底方案',
    priority: 0
  }];
}
