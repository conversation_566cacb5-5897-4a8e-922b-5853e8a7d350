<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            background: #2d2d2d;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #3a3a3a;
            border-radius: 8px;
        }
        .test-button {
            background: #007acc;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #005a9e;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background: #4a4a4a;
            border-radius: 6px;
            white-space: pre-wrap;
        }
        .success { color: #4ade80; }
        .error { color: #f87171; }
        .warning { color: #fbbf24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 修复测试页面</h1>
        <p>测试性能告警功能和全球功能组件的修复效果</p>
        
        <div class="test-section">
            <h2>📊 性能告警功能测试</h2>
            <p>测试告警阈值降低后是否能正常触发告警</p>
            <button class="test-button" onclick="testAlertSystem()">测试告警系统</button>
            <button class="test-button" onclick="generateTestAlert()">生成测试告警</button>
            <button class="test-button" onclick="checkAlerts()">检查现有告警</button>
            <div id="alert-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>🌍 全球功能组件测试</h2>
            <p>测试全球地区数据是否能正确显示多个地区</p>
            <button class="test-button" onclick="testGlobalRegions()">测试全球地区</button>
            <button class="test-button" onclick="generateGlobalData()">生成全球数据</button>
            <div id="global-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>💾 本地存储测试</h2>
            <p>测试localStorage是否正常工作</p>
            <button class="test-button" onclick="testLocalStorage()">测试本地存储</button>
            <div id="storage-result" class="result"></div>
        </div>
    </div>

    <script>
        // 测试告警系统
        function testAlertSystem() {
            const resultDiv = document.getElementById('alert-result');
            resultDiv.innerHTML = '🔄 正在测试告警系统...';
            
            try {
                // 模拟一个高延迟的测试记录
                const testRecord = {
                    target: 'wobshare.us.kg',
                    timestamp: Date.now(),
                    latency: 250, // 超过新的200ms阈值
                    jitter: 10,
                    packetLoss: 2, // 超过新的1%阈值
                    bandwidth: 80, // 低于100Kbps阈值
                    downloadSpeed: 0,
                    uploadSpeed: 0,
                    mtu: 1500,
                    status: 'success',
                    testMethod: '测试告警',
                    reliability: 50
                };

                // 保存到localStorage模拟历史记录
                const existingRecords = JSON.parse(localStorage.getItem('ping_history') || '[]');
                existingRecords.push({...testRecord, id: Date.now().toString()});
                localStorage.setItem('ping_history', JSON.stringify(existingRecords));

                // 模拟告警检查逻辑
                const alerts = [];
                if (testRecord.latency > 200) {
                    alerts.push({
                        id: Date.now().toString(),
                        type: 'latency_spike',
                        severity: testRecord.latency > 300 ? 'high' : 'medium',
                        message: `延迟峰值: ${testRecord.latency}ms (目标: ${testRecord.target})`,
                        timestamp: testRecord.timestamp,
                        target: testRecord.target,
                        value: testRecord.latency,
                        threshold: 200
                    });
                }
                if (testRecord.packetLoss > 1) {
                    alerts.push({
                        id: (Date.now() + 1).toString(),
                        type: 'packet_loss',
                        severity: 'medium',
                        message: `丢包率过高: ${testRecord.packetLoss}% (目标: ${testRecord.target})`,
                        timestamp: testRecord.timestamp,
                        target: testRecord.target,
                        value: testRecord.packetLoss,
                        threshold: 1
                    });
                }

                // 保存告警
                const existingAlerts = JSON.parse(localStorage.getItem('ping_alerts') || '[]');
                existingAlerts.push(...alerts);
                localStorage.setItem('ping_alerts', JSON.stringify(existingAlerts));

                resultDiv.innerHTML = `<span class="success">✅ 告警系统测试成功！</span>
生成了 ${alerts.length} 个告警：
${alerts.map(alert => `- ${alert.message} (严重程度: ${alert.severity})`).join('\n')}

测试记录已保存到历史记录中。`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 告警系统测试失败: ${error.message}</span>`;
            }
        }

        // 生成测试告警
        function generateTestAlert() {
            const resultDiv = document.getElementById('alert-result');
            try {
                const testAlert = {
                    id: Date.now().toString(),
                    type: 'latency_spike',
                    severity: 'medium',
                    message: `测试告警: 延迟较高 (目标: wobshare.us.kg)`,
                    timestamp: Date.now(),
                    target: 'wobshare.us.kg',
                    value: 250,
                    threshold: 200
                };
                
                const existingAlerts = JSON.parse(localStorage.getItem('ping_alerts') || '[]');
                existingAlerts.push(testAlert);
                localStorage.setItem('ping_alerts', JSON.stringify(existingAlerts));
                
                resultDiv.innerHTML = `<span class="success">✅ 测试告警已生成！</span>
告警信息: ${testAlert.message}
严重程度: ${testAlert.severity}
时间: ${new Date(testAlert.timestamp).toLocaleString()}`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 生成测试告警失败: ${error.message}</span>`;
            }
        }

        // 检查现有告警
        function checkAlerts() {
            const resultDiv = document.getElementById('alert-result');
            try {
                const alerts = JSON.parse(localStorage.getItem('ping_alerts') || '[]');
                if (alerts.length === 0) {
                    resultDiv.innerHTML = '<span class="warning">⚠️ 暂无告警记录</span>';
                } else {
                    resultDiv.innerHTML = `<span class="success">📋 找到 ${alerts.length} 个告警记录：</span>
${alerts.slice(-5).map(alert => 
    `- ${alert.message} (${alert.severity}) - ${new Date(alert.timestamp).toLocaleString()}`
).join('\n')}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 检查告警失败: ${error.message}</span>`;
            }
        }

        // 测试全球地区
        function testGlobalRegions() {
            const resultDiv = document.getElementById('global-result');
            resultDiv.innerHTML = '🔄 正在测试全球地区数据...';
            
            try {
                // 模拟全球地区数据
                const globalRegions = [
                    '中国大陆', '港澳台', '亚太地区', '北美地区', 
                    '欧洲地区', '南美地区', '中东非洲', '大洋洲'
                ];
                
                const simulatedData = [];
                globalRegions.forEach(region => {
                    const cities = getRegionCities(region);
                    cities.forEach(city => {
                        simulatedData.push({
                            node: city,
                            city: city,
                            ping: Math.round(Math.random() * 200 + 50),
                            status: 'success',
                            location: {
                                region: region,
                                city: city
                            }
                        });
                    });
                });
                
                // 按地区分组
                const regionGroups = {};
                globalRegions.forEach(region => {
                    regionGroups[region] = simulatedData.filter(d => d.location.region === region);
                });
                
                const regionStats = Object.entries(regionGroups).map(([region, data]) => {
                    const avgLatency = data.reduce((sum, d) => sum + d.ping, 0) / data.length;
                    return `${region}: ${data.length}个节点, 平均延迟${Math.round(avgLatency)}ms`;
                });
                
                resultDiv.innerHTML = `<span class="success">✅ 全球地区测试成功！</span>
发现 ${globalRegions.length} 个地区:
${regionStats.join('\n')}

总计 ${simulatedData.length} 个测试节点`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 全球地区测试失败: ${error.message}</span>`;
            }
        }

        // 获取地区城市
        function getRegionCities(region) {
            const regionCities = {
                '中国大陆': ['北京', '上海', '广州', '深圳', '杭州'],
                '港澳台': ['香港', '澳门', '台北'],
                '亚太地区': ['东京', '首尔', '新加坡', '悉尼'],
                '北美地区': ['纽约', '洛杉矶', '多伦多'],
                '欧洲地区': ['伦敦', '法兰克福', '巴黎'],
                '南美地区': ['圣保罗', '布宜诺斯艾利斯'],
                '中东非洲': ['迪拜', '开普敦'],
                '大洋洲': ['悉尼', '墨尔本']
            };
            return regionCities[region] || [];
        }

        // 生成全球数据
        function generateGlobalData() {
            const resultDiv = document.getElementById('global-result');
            try {
                const globalData = [];
                const regions = ['中国大陆', '港澳台', '亚太地区', '北美地区', '欧洲地区', '南美地区', '中东非洲', '大洋洲'];
                
                regions.forEach(region => {
                    const cities = getRegionCities(region);
                    cities.forEach(city => {
                        globalData.push({
                            region,
                            city,
                            latency: Math.round(Math.random() * 200 + 30),
                            status: 'success'
                        });
                    });
                });
                
                // 保存到localStorage
                localStorage.setItem('global_test_data', JSON.stringify(globalData));
                
                resultDiv.innerHTML = `<span class="success">✅ 全球数据已生成！</span>
生成了 ${globalData.length} 个节点数据
覆盖 ${regions.length} 个地区
数据已保存到本地存储`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 生成全球数据失败: ${error.message}</span>`;
            }
        }

        // 测试本地存储
        function testLocalStorage() {
            const resultDiv = document.getElementById('storage-result');
            try {
                // 测试写入
                const testData = { test: true, timestamp: Date.now() };
                localStorage.setItem('storage_test', JSON.stringify(testData));
                
                // 测试读取
                const readData = JSON.parse(localStorage.getItem('storage_test'));
                
                // 测试删除
                localStorage.removeItem('storage_test');
                
                // 检查现有数据
                const historyCount = JSON.parse(localStorage.getItem('ping_history') || '[]').length;
                const alertCount = JSON.parse(localStorage.getItem('ping_alerts') || '[]').length;
                
                resultDiv.innerHTML = `<span class="success">✅ 本地存储测试成功！</span>
写入测试: ✅
读取测试: ✅
删除测试: ✅

现有数据:
- 历史记录: ${historyCount} 条
- 告警记录: ${alertCount} 条`;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 本地存储测试失败: ${error.message}</span>
可能原因: 浏览器禁用了localStorage或存储空间不足`;
            }
        }
    </script>
</body>
</html>
