'use client';

import React from 'react';
import { Loader2, Wifi, Globe, Zap } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  message?: string;
  progress?: number;
  variant?: 'default' | 'network' | 'ping' | 'analysis';
  className?: string;
  showProgress?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  message,
  progress,
  variant = 'default',
  className = '',
  showProgress = true
}) => {
  // 尺寸映射
  const sizeClasses = {
    xs: 'w-4 h-4',
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  // 图标映射
  const getIcon = () => {
    switch (variant) {
      case 'network':
        return <Wifi className={`${sizeClasses[size]} animate-pulse`} />;
      case 'ping':
        return <Globe className={`${sizeClasses[size]} animate-spin`} />;
      case 'analysis':
        return <Zap className={`${sizeClasses[size]} animate-bounce`} />;
      default:
        return <Loader2 className={`${sizeClasses[size]} animate-spin`} />;
    }
  };

  // 颜色主题
  const getColorClasses = () => {
    switch (variant) {
      case 'network':
        return 'text-green-500';
      case 'ping':
        return 'text-blue-500';
      case 'analysis':
        return 'text-purple-500';
      default:
        return 'text-blue-500';
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center p-4 ${className}`}>
      {/* 主要加载图标 */}
      <div className={`${getColorClasses()} mb-3`}>
        {getIcon()}
      </div>

      {/* 加载消息 */}
      {message && (
        <p className="text-sm text-gray-600 dark:text-gray-400 text-center mb-3 max-w-xs">
          {message}
        </p>
      )}

      {/* 进度条 */}
      {showProgress && progress !== undefined && (
        <div className="w-full max-w-xs">
          <div className="flex justify-between text-xs text-gray-500 mb-1">
            <span>进度</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ease-out ${
                variant === 'network' ? 'bg-green-500' :
                variant === 'ping' ? 'bg-blue-500' :
                variant === 'analysis' ? 'bg-purple-500' :
                'bg-blue-500'
              }`}
              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
            />
          </div>
        </div>
      )}

      {/* 脉冲动画背景 */}
      {variant === 'ping' && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="w-20 h-20 border-2 border-blue-300 rounded-full animate-ping opacity-20" />
          <div className="w-16 h-16 border-2 border-blue-400 rounded-full animate-ping opacity-30 absolute" style={{ animationDelay: '0.5s' }} />
        </div>
      )}
    </div>
  );
};

// 骨架屏加载组件
export const SkeletonLoader: React.FC<{
  lines?: number;
  className?: string;
}> = ({ lines = 3, className = '' }) => {
  return (
    <div className={`animate-pulse ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={`bg-gray-300 dark:bg-gray-600 rounded h-4 mb-3 ${
            index === lines - 1 ? 'w-3/4' : 'w-full'
          }`}
        />
      ))}
    </div>
  );
};

// 卡片加载组件
export const CardSkeleton: React.FC<{
  showAvatar?: boolean;
  className?: string;
}> = ({ showAvatar = false, className = '' }) => {
  return (
    <div className={`animate-pulse p-4 ${className}`}>
      <div className="flex items-center space-x-4">
        {showAvatar && (
          <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full" />
        )}
        <div className="flex-1 space-y-2">
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4" />
          <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2" />
        </div>
      </div>
      <div className="mt-4 space-y-2">
        <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded" />
        <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-5/6" />
      </div>
    </div>
  );
};

// 表格加载组件
export const TableSkeleton: React.FC<{
  rows?: number;
  columns?: number;
  className?: string;
}> = ({ rows = 5, columns = 4, className = '' }) => {
  return (
    <div className={`animate-pulse ${className}`}>
      {/* 表头 */}
      <div className="flex space-x-4 mb-4">
        {Array.from({ length: columns }).map((_, index) => (
          <div key={index} className="flex-1 h-6 bg-gray-300 dark:bg-gray-600 rounded" />
        ))}
      </div>
      
      {/* 表格行 */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4 mb-3">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div 
              key={colIndex} 
              className={`flex-1 h-4 bg-gray-300 dark:bg-gray-600 rounded ${
                colIndex === 0 ? 'w-1/4' : colIndex === columns - 1 ? 'w-1/6' : ''
              }`} 
            />
          ))}
        </div>
      ))}
    </div>
  );
};

// 全屏加载组件
export const FullScreenLoader: React.FC<{
  message?: string;
  progress?: number;
  variant?: 'default' | 'network' | 'ping' | 'analysis';
}> = ({ message = '加载中...', progress, variant = 'default' }) => {
  return (
    <div className="fixed inset-0 bg-white dark:bg-gray-900 bg-opacity-90 dark:bg-opacity-90 flex items-center justify-center z-50">
      <div className="text-center">
        <LoadingSpinner
          size="xl"
          message={message}
          progress={progress}
          variant={variant}
          showProgress={progress !== undefined}
        />
      </div>
    </div>
  );
};

// 按钮加载状态
export const ButtonLoader: React.FC<{
  isLoading: boolean;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
}> = ({ isLoading, children, className = '', disabled = false, onClick }) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`relative ${className} ${
        isLoading || disabled ? 'opacity-75 cursor-not-allowed' : ''
      }`}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Loader2 className="w-4 h-4 animate-spin" />
        </div>
      )}
      <span className={isLoading ? 'opacity-0' : 'opacity-100'}>
        {children}
      </span>
    </button>
  );
};

export default LoadingSpinner;
