'use client';

import React, { useState, Suspense, lazy } from 'react';
import LoadingSpinner from '@/components/LoadingSpinner';
import Link from 'next/link';

// 懒加载组件以提升初始加载速度
const NetworkMonitor = lazy(() => import('@/components/NetworkMonitor'));
const PingTool = lazy(() => import('@/components/PingTool'));
const EnhancedPingTester = lazy(() => import('@/components/EnhancedPingTester'));

export default function Home() {
  const [isDarkMode, setIsDarkMode] = useState(true); // 默认黑夜模式

  return (
    <div className={`min-h-screen transition-colors duration-300 ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      {/* Home Icon - Top Left */}
      <div className="fixed top-4 left-4 z-50">
        <a
          href="https://wobshare.us.kg/"
          target="_blank"
          rel="noopener noreferrer"
          className={`p-3 rounded-lg shadow-lg transition-colors duration-300 ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-white hover:bg-gray-100'} border ${isDarkMode ? 'border-gray-600' : 'border-gray-200'} block`}
          title="访问主站"
        >
          <svg className={`h-5 w-5 ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
        </a>
      </div>

      {/* Top Navigation */}
      <div className="fixed top-4 right-4 z-50">
        {/* Dark/Light Mode Toggle */}
        <button
          onClick={() => setIsDarkMode(!isDarkMode)}
          className={`p-3 rounded-lg shadow-lg transition-colors duration-300 ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-white hover:bg-gray-100'} border ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}
          title={isDarkMode ? '切换到白天模式' : '切换到夜间模式'}
        >
          {isDarkMode ? (
            <svg className="h-5 w-5 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
          ) : (
            <svg className="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
            </svg>
          )}
        </button>
      </div>

      {/* Ping Tool Section - 顶部 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6 pb-4">
        <Suspense fallback={
          <div className="flex items-center justify-center min-h-[400px]">
            <LoadingSpinner />
          </div>
        }>
          <PingTool isDarkMode={isDarkMode} />
        </Suspense>
      </div>

      {/* Network Monitor Section */}
      <Suspense fallback={
        <div className="flex items-center justify-center min-h-[200px]">
          <LoadingSpinner />
        </div>
      }>
        <NetworkMonitor isDarkMode={isDarkMode} setIsDarkMode={setIsDarkMode} />
      </Suspense>
    </div>
  );
}
