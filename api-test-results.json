{"results": {"domestic": {"wobshare.us.kg": {"ITDOG": {"api": "ITDOG", "status": "failed", "responseTime": 567, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<html>\\r\\n<head><title>302 Found</title></head>\\r\\n<body>\\r\\n<center><h1>302 Found</h1></center>\\r\\n<hr><center>openresty</center>\\r\\n</body>\\r\\n</html>\\r\\n\"}"}, "PingPE": {"api": "PingPE", "status": "error", "error": "getaddrinfo ENOTFOUND api.pingpe.com"}, "17CE": {"api": "17CE", "status": "failed", "responseTime": 480, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html>\\n<head>\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=utf-8\\\" />\\n<meta name=\\\"keywords\\\" content=\\\"PING测试 网站速度测试 17CE\\\"/>\\n<me"}, "GlobalPing": {"api": "GlobalPing", "status": "async", "responseTime": 1086, "message": "需要异步获取结果"}, "BOCE": {"api": "BOCE", "status": "failed", "responseTime": 234, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <link rel=\\\"shortcut icon\\\" href=\\\"/favicon.ico\\\"/>\\n    <title>    boce.com:网站测速-ping检测-域"}}, "baidu.com": {"ITDOG": {"api": "ITDOG", "status": "failed", "responseTime": 618, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<html>\\r\\n<head><title>302 Found</title></head>\\r\\n<body>\\r\\n<center><h1>302 Found</h1></center>\\r\\n<hr><center>openresty</center>\\r\\n</body>\\r\\n</html>\\r\\n\"}"}, "PingPE": {"api": "PingPE", "status": "error", "error": "getaddrinfo ENOTFOUND api.pingpe.com"}, "17CE": {"api": "17CE", "status": "failed", "responseTime": 458, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html>\\n<head>\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=utf-8\\\" />\\n<meta name=\\\"keywords\\\" content=\\\"PING测试 网站速度测试 17CE\\\"/>\\n<me"}, "GlobalPing": {"api": "GlobalPing", "status": "async", "responseTime": 856, "message": "需要异步获取结果"}, "BOCE": {"api": "BOCE", "status": "failed", "responseTime": 251, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <link rel=\\\"shortcut icon\\\" href=\\\"/favicon.ico\\\"/>\\n    <title>    boce.com:网站测速-ping检测-域"}}, "taobao.com": {"ITDOG": {"api": "ITDOG", "status": "failed", "responseTime": 261, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<html>\\r\\n<head><title>302 Found</title></head>\\r\\n<body>\\r\\n<center><h1>302 Found</h1></center>\\r\\n<hr><center>openresty</center>\\r\\n</body>\\r\\n</html>\\r\\n\"}"}, "PingPE": {"api": "PingPE", "status": "error", "error": "getaddrinfo ENOTFOUND api.pingpe.com"}, "17CE": {"api": "17CE", "status": "failed", "responseTime": 429, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html>\\n<head>\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=utf-8\\\" />\\n<meta name=\\\"keywords\\\" content=\\\"PING测试 网站速度测试 17CE\\\"/>\\n<me"}, "GlobalPing": {"api": "GlobalPing", "status": "async", "responseTime": 886, "message": "需要异步获取结果"}, "BOCE": {"api": "BOCE", "status": "failed", "responseTime": 214, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <link rel=\\\"shortcut icon\\\" href=\\\"/favicon.ico\\\"/>\\n    <title>    boce.com:网站测速-ping检测-域"}}, "qq.com": {"ITDOG": {"api": "ITDOG", "status": "failed", "responseTime": 249, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<html>\\r\\n<head><title>302 Found</title></head>\\r\\n<body>\\r\\n<center><h1>302 Found</h1></center>\\r\\n<hr><center>openresty</center>\\r\\n</body>\\r\\n</html>\\r\\n\"}"}, "PingPE": {"api": "PingPE", "status": "error", "error": "getaddrinfo ENOTFOUND api.pingpe.com"}, "17CE": {"api": "17CE", "status": "failed", "responseTime": 468, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html>\\n<head>\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=utf-8\\\" />\\n<meta name=\\\"keywords\\\" content=\\\"PING测试 网站速度测试 17CE\\\"/>\\n<me"}, "GlobalPing": {"api": "GlobalPing", "status": "async", "responseTime": 806, "message": "需要异步获取结果"}, "BOCE": {"api": "BOCE", "status": "failed", "responseTime": 261, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <link rel=\\\"shortcut icon\\\" href=\\\"/favicon.ico\\\"/>\\n    <title>    boce.com:网站测速-ping检测-域"}}, "weibo.com": {"ITDOG": {"api": "ITDOG", "status": "failed", "responseTime": 322, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<html>\\r\\n<head><title>302 Found</title></head>\\r\\n<body>\\r\\n<center><h1>302 Found</h1></center>\\r\\n<hr><center>openresty</center>\\r\\n</body>\\r\\n</html>\\r\\n\"}"}, "PingPE": {"api": "PingPE", "status": "error", "error": "getaddrinfo ENOTFOUND api.pingpe.com"}, "17CE": {"api": "17CE", "status": "failed", "responseTime": 416, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html>\\n<head>\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=utf-8\\\" />\\n<meta name=\\\"keywords\\\" content=\\\"PING测试 网站速度测试 17CE\\\"/>\\n<me"}, "GlobalPing": {"api": "GlobalPing", "status": "async", "responseTime": 629, "message": "需要异步获取结果"}, "BOCE": {"api": "BOCE", "status": "failed", "responseTime": 205, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <link rel=\\\"shortcut icon\\\" href=\\\"/favicon.ico\\\"/>\\n    <title>    boce.com:网站测速-ping检测-域"}}, "zhihu.com": {"ITDOG": {"api": "ITDOG", "status": "failed", "responseTime": 1254, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<html>\\r\\n<head><title>302 Found</title></head>\\r\\n<body>\\r\\n<center><h1>302 Found</h1></center>\\r\\n<hr><center>openresty</center>\\r\\n</body>\\r\\n</html>\\r\\n\"}"}, "PingPE": {"api": "PingPE", "status": "error", "error": "getaddrinfo ENOTFOUND api.pingpe.com"}, "17CE": {"api": "17CE", "status": "failed", "responseTime": 420, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html>\\n<head>\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=utf-8\\\" />\\n<meta name=\\\"keywords\\\" content=\\\"PING测试 网站速度测试 17CE\\\"/>\\n<me"}, "GlobalPing": {"api": "GlobalPing", "status": "async", "responseTime": 1141, "message": "需要异步获取结果"}, "BOCE": {"api": "BOCE", "status": "failed", "responseTime": 282, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <link rel=\\\"shortcut icon\\\" href=\\\"/favicon.ico\\\"/>\\n    <title>    boce.com:网站测速-ping检测-域"}}, "bilibili.com": {"ITDOG": {"api": "ITDOG", "status": "failed", "responseTime": 690, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<html>\\r\\n<head><title>302 Found</title></head>\\r\\n<body>\\r\\n<center><h1>302 Found</h1></center>\\r\\n<hr><center>openresty</center>\\r\\n</body>\\r\\n</html>\\r\\n\"}"}, "PingPE": {"api": "PingPE", "status": "error", "error": "getaddrinfo ENOTFOUND api.pingpe.com"}, "17CE": {"api": "17CE", "status": "failed", "responseTime": 401, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html>\\n<head>\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=utf-8\\\" />\\n<meta name=\\\"keywords\\\" content=\\\"PING测试 网站速度测试 17CE\\\"/>\\n<me"}, "GlobalPing": {"api": "GlobalPing", "status": "async", "responseTime": 998, "message": "需要异步获取结果"}, "BOCE": {"api": "BOCE", "status": "failed", "responseTime": 356, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <link rel=\\\"shortcut icon\\\" href=\\\"/favicon.ico\\\"/>\\n    <title>    boce.com:网站测速-ping检测-域"}}}, "international": {"google.com": {"ITDOG": {"api": "ITDOG", "status": "failed", "responseTime": 312, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<html>\\r\\n<head><title>302 Found</title></head>\\r\\n<body>\\r\\n<center><h1>302 Found</h1></center>\\r\\n<hr><center>openresty</center>\\r\\n</body>\\r\\n</html>\\r\\n\"}"}, "PingPE": {"api": "PingPE", "status": "error", "error": "getaddrinfo ENOTFOUND api.pingpe.com"}, "17CE": {"api": "17CE", "status": "failed", "responseTime": 453, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html>\\n<head>\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=utf-8\\\" />\\n<meta name=\\\"keywords\\\" content=\\\"PING测试 网站速度测试 17CE\\\"/>\\n<me"}, "GlobalPing": {"api": "GlobalPing", "status": "async", "responseTime": 715, "message": "需要异步获取结果"}, "BOCE": {"api": "BOCE", "status": "failed", "responseTime": 279, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <link rel=\\\"shortcut icon\\\" href=\\\"/favicon.ico\\\"/>\\n    <title>    boce.com:网站测速-ping检测-域"}}, "facebook.com": {"ITDOG": {"api": "ITDOG", "status": "failed", "responseTime": 604, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<html>\\r\\n<head><title>302 Found</title></head>\\r\\n<body>\\r\\n<center><h1>302 Found</h1></center>\\r\\n<hr><center>openresty</center>\\r\\n</body>\\r\\n</html>\\r\\n\"}"}, "PingPE": {"api": "PingPE", "status": "error", "error": "getaddrinfo ENOTFOUND api.pingpe.com"}, "17CE": {"api": "17CE", "status": "failed", "responseTime": 397, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html>\\n<head>\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=utf-8\\\" />\\n<meta name=\\\"keywords\\\" content=\\\"PING测试 网站速度测试 17CE\\\"/>\\n<me"}, "GlobalPing": {"api": "GlobalPing", "status": "async", "responseTime": 824, "message": "需要异步获取结果"}, "BOCE": {"api": "BOCE", "status": "failed", "responseTime": 318, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <link rel=\\\"shortcut icon\\\" href=\\\"/favicon.ico\\\"/>\\n    <title>    boce.com:网站测速-ping检测-域"}}, "twitter.com": {"ITDOG": {"api": "ITDOG", "status": "failed", "responseTime": 1405, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<html>\\r\\n<head><title>302 Found</title></head>\\r\\n<body>\\r\\n<center><h1>302 Found</h1></center>\\r\\n<hr><center>openresty</center>\\r\\n</body>\\r\\n</html>\\r\\n\"}"}, "PingPE": {"api": "PingPE", "status": "error", "error": "getaddrinfo ENOTFOUND api.pingpe.com"}, "17CE": {"api": "17CE", "status": "failed", "responseTime": 392, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html>\\n<head>\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=utf-8\\\" />\\n<meta name=\\\"keywords\\\" content=\\\"PING测试 网站速度测试 17CE\\\"/>\\n<me"}, "GlobalPing": {"api": "GlobalPing", "status": "async", "responseTime": 871, "message": "需要异步获取结果"}, "BOCE": {"api": "BOCE", "status": "failed", "responseTime": 244, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <link rel=\\\"shortcut icon\\\" href=\\\"/favicon.ico\\\"/>\\n    <title>    boce.com:网站测速-ping检测-域"}}, "instagram.com": {"ITDOG": {"api": "ITDOG", "status": "failed", "responseTime": 264, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<html>\\r\\n<head><title>302 Found</title></head>\\r\\n<body>\\r\\n<center><h1>302 Found</h1></center>\\r\\n<hr><center>openresty</center>\\r\\n</body>\\r\\n</html>\\r\\n\"}"}, "PingPE": {"api": "PingPE", "status": "error", "error": "getaddrinfo ENOTFOUND api.pingpe.com"}, "17CE": {"api": "17CE", "status": "failed", "responseTime": 456, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html>\\n<head>\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=utf-8\\\" />\\n<meta name=\\\"keywords\\\" content=\\\"PING测试 网站速度测试 17CE\\\"/>\\n<me"}, "GlobalPing": {"api": "GlobalPing", "status": "async", "responseTime": 846, "message": "需要异步获取结果"}, "BOCE": {"api": "BOCE", "status": "failed", "responseTime": 685, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <link rel=\\\"shortcut icon\\\" href=\\\"/favicon.ico\\\"/>\\n    <title>    boce.com:网站测速-ping检测-域"}}, "whatsapp.com": {"ITDOG": {"api": "ITDOG", "status": "failed", "responseTime": 364, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<html>\\r\\n<head><title>302 Found</title></head>\\r\\n<body>\\r\\n<center><h1>302 Found</h1></center>\\r\\n<hr><center>openresty</center>\\r\\n</body>\\r\\n</html>\\r\\n\"}"}, "PingPE": {"api": "PingPE", "status": "error", "error": "getaddrinfo ENOTFOUND api.pingpe.com"}, "17CE": {"api": "17CE", "status": "failed", "responseTime": 563, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html>\\n<head>\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=utf-8\\\" />\\n<meta name=\\\"keywords\\\" content=\\\"PING测试 网站速度测试 17CE\\\"/>\\n<me"}, "GlobalPing": {"api": "GlobalPing", "status": "async", "responseTime": 700, "message": "需要异步获取结果"}, "BOCE": {"api": "BOCE", "status": "failed", "responseTime": 222, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <link rel=\\\"shortcut icon\\\" href=\\\"/favicon.ico\\\"/>\\n    <title>    boce.com:网站测速-ping检测-域"}}, "telegram.org": {"ITDOG": {"api": "ITDOG", "status": "failed", "responseTime": 313, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<html>\\r\\n<head><title>302 Found</title></head>\\r\\n<body>\\r\\n<center><h1>302 Found</h1></center>\\r\\n<hr><center>openresty</center>\\r\\n</body>\\r\\n</html>\\r\\n\"}"}, "PingPE": {"api": "PingPE", "status": "error", "error": "getaddrinfo ENOTFOUND api.pingpe.com"}, "17CE": {"api": "17CE", "status": "failed", "responseTime": 461, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html>\\n<head>\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=utf-8\\\" />\\n<meta name=\\\"keywords\\\" content=\\\"PING测试 网站速度测试 17CE\\\"/>\\n<me"}, "GlobalPing": {"api": "GlobalPing", "status": "async", "responseTime": 808, "message": "需要异步获取结果"}, "BOCE": {"api": "BOCE", "status": "failed", "responseTime": 233, "error": "解析响应失败", "response": "{\"error\":\"Invalid JSON\",\"raw\":\"<!DOCTYPE html>\\n<html lang=\\\"en\\\">\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <link rel=\\\"shortcut icon\\\" href=\\\"/favicon.ico\\\"/>\\n    <title>    boce.com:网站测速-ping检测-域"}}}, "apiScores": {"ITDOG": {"totalTests": 13, "successfulTests": 0, "avgResponseTime": 0, "avgNodeCount": 0, "avgSuccessRate": 0, "responseTimes": [], "errors": ["解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败"], "successRate": 0}, "PingPE": {"totalTests": 13, "successfulTests": 0, "avgResponseTime": 0, "avgNodeCount": 0, "avgSuccessRate": 0, "responseTimes": [], "errors": ["getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com"], "successRate": 0}, "17CE": {"totalTests": 13, "successfulTests": 0, "avgResponseTime": 0, "avgNodeCount": 0, "avgSuccessRate": 0, "responseTimes": [], "errors": ["解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败"], "successRate": 0}, "GlobalPing": {"totalTests": 13, "successfulTests": 0, "avgResponseTime": 0, "avgNodeCount": 0, "avgSuccessRate": 0, "responseTimes": [], "errors": ["async", "async", "async", "async", "async", "async", "async", "async", "async", "async", "async", "async", "async"], "successRate": 0}, "BOCE": {"totalTests": 13, "successfulTests": 0, "avgResponseTime": 0, "avgNodeCount": 0, "avgSuccessRate": 0, "responseTimes": [], "errors": ["解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败"], "successRate": 0}}}, "recommendations": {"primary": "ITDOG", "backups": ["PingPE", "17CE", "GlobalPing"], "rankings": [{"name": "ITDOG", "totalTests": 13, "successfulTests": 0, "avgResponseTime": 0, "avgNodeCount": 0, "avgSuccessRate": 0, "responseTimes": [], "errors": ["解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败"], "successRate": 0, "overallScore": 30}, {"name": "PingPE", "totalTests": 13, "successfulTests": 0, "avgResponseTime": 0, "avgNodeCount": 0, "avgSuccessRate": 0, "responseTimes": [], "errors": ["getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com", "getaddrinfo ENOTFOUND api.pingpe.com"], "successRate": 0, "overallScore": 30}, {"name": "17CE", "totalTests": 13, "successfulTests": 0, "avgResponseTime": 0, "avgNodeCount": 0, "avgSuccessRate": 0, "responseTimes": [], "errors": ["解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败"], "successRate": 0, "overallScore": 30}, {"name": "GlobalPing", "totalTests": 13, "successfulTests": 0, "avgResponseTime": 0, "avgNodeCount": 0, "avgSuccessRate": 0, "responseTimes": [], "errors": ["async", "async", "async", "async", "async", "async", "async", "async", "async", "async", "async", "async", "async"], "successRate": 0, "overallScore": 30}, {"name": "BOCE", "totalTests": 13, "successfulTests": 0, "avgResponseTime": 0, "avgNodeCount": 0, "avgSuccessRate": 0, "responseTimes": [], "errors": ["解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败", "解析响应失败"], "successRate": 0, "overallScore": 30}]}, "timestamp": "2025-07-28T12:54:57.664Z"}